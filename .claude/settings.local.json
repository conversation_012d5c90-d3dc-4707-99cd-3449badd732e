{"permissions": {"allow": ["Bash(find:*)", "Bash(grep:*)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(poetry run:*)", "<PERSON><PERSON>(python test:*)", "Bash(python -m pytest software/tests/ -v -k \"test_director\" --tb=short)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(sed:*)", "Bash(node:*)", "<PERSON><PERSON>(echo:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(rg:*)", "<PERSON><PERSON>(timeout:*)", "Bash(git checkout:*)", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_select_option", "mcp__playwright__browser_click", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_navigate", "Bash(cp:*)", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_close", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs"], "deny": []}}