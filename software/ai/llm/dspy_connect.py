"""
DSPy-<PERSON><PERSON>hain Adapter
Wraps LangChain models from llm_connect.py for DSPy compatibility
"""

import asyncio
import nest_asyncio
from typing import Optional, List, Dict, Any, Union
from langchain_core.messages import HumanMessage, AIMessage
import dspy
from software.ai.llm.llm_connect import get_llm_connect

# Apply nest_asyncio to handle nested event loops
nest_asyncio.apply()


class LangChainDSPyAdapter(dspy.LM):
    """Adapter to use LangChain models from llm_connect with DSPy"""
    
    def __init__(self, langchain_model=None, model_type: str = "chat", model_name: str = None, **kwargs):
        """
        Initialize the adapter with a LangChain model
        
        Args:
            langchain_model: The LangChain model instance (if None, will use default)
            model_type: Type of model ("chat" or "text")
            model_name: Model name for DSPy (if None, will use "langchain")
            **kwargs: Additional parameters (temperature, max_tokens, etc.)
        """
        self.langchain_model = langchain_model
        self.model_type = model_type
        self.kwargs = kwargs
        self._loop = None
        
        # DSPy LM initialization - use a generic model name to avoid provider inference
        super().__init__(model=model_name or "langchain", model_type="chat")
        
    def _get_or_create_loop(self):
        """Get existing event loop or create new one"""
        try:
            loop = asyncio.get_running_loop()
            return loop, False
        except RuntimeError:
            if self._loop is None:
                self._loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self._loop)
            return self._loop, True
            
    async def _ensure_model(self):
        """Ensure we have a LangChain model instance"""
        if self.langchain_model is None:
            llm_connect = get_llm_connect()
            self.langchain_model = await llm_connect.get_llm()
            
    def forward(self, prompt: str, **kwargs) -> List[str]:
        """
        Synchronous forward pass for DSPy
        
        Args:
            prompt: The input prompt
            **kwargs: Additional parameters (temperature, max_tokens, etc.)
            
        Returns:
            List of generated responses
        """
        try:
            # Try to get the running loop
            loop = asyncio.get_running_loop()
            # If we have a running loop, use nest_asyncio to allow nested execution
            result = loop.run_until_complete(self.aforward(prompt, **kwargs))
        except RuntimeError:
            # No running loop, create and run
            result = asyncio.run(self.aforward(prompt, **kwargs))
            
        return result
        
    async def aforward(self, prompt: str, **kwargs) -> List[str]:
        """
        Asynchronous forward pass for DSPy
        
        Args:
            prompt: The input prompt
            **kwargs: Additional parameters (temperature, max_tokens, etc.)
            
        Returns:
            List of generated responses
        """
        # Ensure we have a model
        await self._ensure_model()
        
        # Merge kwargs
        params = {**self.kwargs, **kwargs}
        
        # Extract DSPy-specific parameters
        n = params.pop('n', 1)  # Number of completions
        temperature = params.get('temperature', 0.7)
        max_tokens = params.get('max_tokens', None)
        
        # Prepare LangChain parameters
        lc_params = {}
        if temperature is not None:
            lc_params['temperature'] = temperature
        if max_tokens is not None:
            lc_params['max_tokens'] = max_tokens
            
        # Generate responses
        responses = []
        for _ in range(n):
            # Create message
            messages = [HumanMessage(content=prompt)]
            
            # Invoke model
            response = await self.langchain_model.ainvoke(messages, **lc_params)
            
            # Extract text from response
            if isinstance(response, AIMessage):
                text = response.content
                # Handle None content
                if text is None:
                    text = ""
            elif hasattr(response, 'content'):
                text = response.content
                if text is None:
                    text = ""
            else:
                text = str(response)
                
            responses.append(text)
            
        return responses
        
    def __call__(self, prompt: str = None, **kwargs) -> List[str]:
        """Allow calling the adapter directly"""
        # Handle different calling conventions
        if prompt is None and 'messages' in kwargs:
            # Extract prompt from messages if called with messages
            messages = kwargs.pop('messages')
            if messages and len(messages) > 0:
                prompt = messages[-1].get('content', '')
        elif prompt is None:
            prompt = kwargs.get('prompt', '')
            
        return self.forward(prompt, **kwargs)


async def get_dspy_llm(stage_id: Optional[str] = None) -> LangChainDSPyAdapter:
    """
    Get a DSPy-compatible LLM using the default from llm_connect
    
    Args:
        stage_id: Optional stage ID for stage-specific models
        
    Returns:
        LangChainDSPyAdapter instance ready for DSPy
    """
    llm_connect = get_llm_connect()
    
    # Get LangChain model
    if stage_id:
        langchain_model = await llm_connect.get_llm(stage_id=stage_id)
    else:
        langchain_model = await llm_connect.get_llm()
        
    # Wrap in DSPy adapter
    return LangChainDSPyAdapter(langchain_model=langchain_model)


def get_dspy_llm_sync() -> LangChainDSPyAdapter:
    """
    Synchronous helper to get DSPy LLM
    
    Returns:
        LangChainDSPyAdapter instance
    """
    # Return adapter that will lazy-load the model
    return LangChainDSPyAdapter()