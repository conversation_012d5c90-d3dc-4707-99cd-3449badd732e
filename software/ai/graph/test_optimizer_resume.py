"""Test script to demonstrate resuming the optimizer workflow with MongoDB persistence"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from ai.graph.optimizer_prompt import get_optimizer_graph

async def resume_workflow():
    """Resume the optimizer workflow with feedback"""
    # Get the graph (will use existing MongoDB checkpointer)
    graph = await get_optimizer_graph()
    
    # Configuration with the same thread ID to resume
    config = {"configurable": {"thread_id": "optimizer-session"}}
    
    # Update state with feedback
    print("📝 Providing feedback to the optimizer...")
    await graph.aupdate_state(
        config, 
        {"feedback": "Make it more technical and include programming terms like functions, variables, and loops"}
    )
    
    # Resume the workflow
    print("▶️ Resuming workflow...")
    result = await graph.ainvoke(None, config)
    
    print("\n✅ Workflow resumed successfully!")
    
    # Check if we need more iterations
    if result.get("current_iteration", 0) < result.get("iterations", 3):
        print(f"\n⏸️ Workflow paused again at iteration {result.get('current_iteration', 0)}")
        print("Run this script again with new feedback to continue.")

if __name__ == "__main__":
    asyncio.run(resume_workflow())