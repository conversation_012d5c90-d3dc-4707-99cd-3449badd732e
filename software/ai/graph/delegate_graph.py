from typing import List
from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage,
    ToolMessage
)
from software.ai.graph.director_state import DirectorState, Plan, AnalystList, console, print_step, print_debug, print_pretty, AnalystState, print_markdown, vero_agent
from software.ai.graph.output_memory_graph import graph as output_memory_graph
from software.ai.graph.deep_search_graph import graph as deep_search_graph
from software.ai.graph.feature_requestor_graph import graph as feature_requestor_graph
from software.ai.graph.plan_forge_graph import graph as plan_forge_graph
from software.ai.tools import registry
import json

from langgraph.types import Send
from software.ai.llm.llm_connect import get_llm_connect
from software.db.research_repository import ResearchRepository
from software.db.research_director_repository import ResearchDirectorRepository

research_repo = ResearchRepository()
research_director_repo = ResearchDirectorRepository()

@vero_agent("Init")
async def init_graph(state: DirectorState) -> DirectorState:
    """Entering init graph"""

    # Preserve all existing state keys
    result_state = {k: v for k, v in state.items()}

    director_id = state.get("director_id", "")
    letta_agent_id = await research_director_repo.get_letta_agent_id(director_id)
    reset_letta_agent_messages = await research_director_repo.reset_letta_agent_messages(letta_agent_id)
    business_question = state.get("business_question", "")

    init_message = HumanMessage(content=f'''I have a plan to answer the following business question: {business_question}

Can you search anything that can help to jumpstart and brainstorm critical things I should consider and things I should avoid? if I don't have anything just say "Nothing in memory", avoid creating memories.''')

    # Update state with new values while preserving other keys
    result_state.update({
        'messages': [init_message],
        'tool_logs': []  # Initialize empty tool_logs for the DirectorState
    })

    return result_state

@vero_agent("Deep thinking")
async def critical_thinking_planning(state: DirectorState) -> DirectorState:
    """Entering critical thinking planning"""
    messages = state.get("messages", [])
    memory_block = messages[-1].content if messages else ""
    business_question = state.get("business_question", "Should we invest in Nvidia (NVDA) in the next 30 days?")
    director_id = state.get("director_id", "")

    # Get Letta memory block
    letta_agent_id = await research_director_repo.get_letta_agent_id(director_id)
    memory_block = await research_director_repo.get_memory_block(letta_agent_id, "research_planning")

    # Get LLM for critical thinking
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "critical_thinking_planning")

    # Create enhanced system message for critical thinking
    system_message = SystemMessage(content=f"""<role>
You are a strategic hedge fund research director analyzing a business question to create an optimal research plan.
Your expertise is in breaking down complex financial questions into actionable research tasks.
</role>

<context>
BUSINESS QUESTION: {business_question}

HISTORICAL INSIGHTS:
{memory_block['content']}
</context>

<task>
Analyze the business question and provide strategic insights to guide the research planning process.
Focus on identifying the most effective approach based on past successful analyses.
</task>

<output_format>
Provide a concise analysis with:
1. Key companies/tickers that should be researched
2. Critical financial metrics to focus on
3. Optimal timeframe considerations
4. Most effective tool combinations based on past successes
5. Potential risks or blind spots to address
</output_format>""")

    # Create human message with the task
    human_message = HumanMessage(content=f"Analyze this investment question strategically: {business_question}")

    # Get critical analysis from LLM
    response = await model.ainvoke([
        system_message,
        human_message
    ])

    critical_thinking_planning_result = response.content
    return {'critical_thinking_planning_result': critical_thinking_planning_result}

@vero_agent("Planning")
async def create_plan(state: DirectorState) -> DirectorState:
    """Having a chosen framework, we are crafting a plan for the main business question"""

    await research_repo.update_workflow_stage(state["thread_id"], "delegate", "in_progress")
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(state["director_id"], "create_plan")

    # Get critical thinking analysis
    critical_thinking_planning = state.get("critical_thinking_planning_result", "")
    system_prompt_plan = state.get("system_prompt_plan", "")
    business_question = state.get("business_question", "Should we invest in Nvidia (NVDA) in the next 30 days?")
    # Create structured output model
    structured_model = model.with_structured_output(Plan)
    json_plan = Plan().model_dump_json()
    tool_prompt = await registry.generate_system_message_async()
    tool_names = await registry.get_tool_names_async()
    tool_names_comma = ', '.join(tool_names)
    fin_lang = f"""
    <critical_thinking_section>
    {critical_thinking_planning}
    </critical_thinking_section>

    <system_prompt_plan_section>
    {system_prompt_plan}
    </system_prompt_plan_section>

    <requirements_section>
    Create a list containing ONE task that will be delegated to an analyst.
    The task should be a single line description of what to do.
    Return it as a list with one element.
    </requirements_section>

    Your format ultimately must be a valid JSON matching this schema:
    {json_plan}

    """
    human_message = HumanMessage(content=f"Please create a plan for the following business question: {business_question}")

    # Use structured output model
    plan = await structured_model.ainvoke([
        SystemMessage(content=fin_lang),
        human_message
    ])

    # Simple bullet point display of tasks
    print("\n[bold]📋 Plan tasks[/bold]")
    for i, task in enumerate(plan.tasks, 1):
        print(f"  [bold]{i}.[/bold] {task}")

    state["plan"] = plan

    return state

@vero_agent("Building")
async def create_personas(state: DirectorState) -> DirectorState:
    """Building personas for the plan just created. Each persona will be assigned a task to analyze."""

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(state["director_id"], "create_personas")

    # Create structured output model
    structured_model = model.with_structured_output(AnalystList)

    # Get the plan from state
    plan_obj = state.get("plan")
    if plan_obj is None:
        print_debug("Plan is None in create_personas", "Error")
        return {"analysts": AnalystList(analysts=[])}

    # Get tasks from the plan
    tasks = plan_obj.tasks if hasattr(plan_obj, 'tasks') else []
    business_question = state.get("business_question", "Should we invest in Nvidia (NVDA) in the next 30 days?")
    
    # Concatenate business question with each task
    contextualized_tasks = [
        f"Given Director's Business question: {business_question}, {task}"
        for task in tasks
    ]

    system_message = SystemMessage(content=f"""<role>
You create analyst personas that match their assigned tasks perfectly.
Each task gets exactly ONE analyst with expertise aligned to that specific task.
</role>

<business_question>
{business_question}
</business_question>

<tasks_to_assign>
{json.dumps(contextualized_tasks, indent=2)}
</tasks_to_assign>

<instructions>
For EACH task in the list above, create ONE analyst:

1. Generate a unique analyst profile that matches the task's requirements
2. Ensure the analyst's expertise aligns with what the task demands
3. Create realistic but diverse profiles (vary age, background, specialization)

For each analyst provide:
- business_question: The exact task from the list (copy it verbatim)
- name_analyst: A realistic name
- age_analyst: Between 28-65
- title_analyst: A title that reflects their expertise for this task
- background_analyst: 2-4 sentences covering education, experience, and specific expertise relevant to their assigned task
</instructions>

<output_format>
Return a valid AnalystList with one analyst per task.
Total number of analysts MUST equal the number of tasks.
{json.dumps(AnalystList.model_json_schema(), indent=2)}
</output_format>""")


    try:
        # Use structured output model
        analysts = await structured_model.ainvoke([
            system_message,
            HumanMessage(content="Create analyst profiles for each task")
        ])
    except Exception as e:
        print_debug(f"Error in create_personas: {str(e)}", "Error")
        raise e #we stop the graph here

    # Simple display of analyst information
    print("\n[bold]👥 Team assembled[/bold]")
    for i, analyst in enumerate(analysts.analysts, 1):
        print(f"\n  [bold]{i}.[/bold] {analyst.name_analyst} ({analyst.age_analyst}) • {analyst.title_analyst}")
        print(f"     [dim]{analyst.background_analyst}[/dim]")
        print(f"     📋 {analyst.business_question}")
        print(f"     [dim]Operations limit: {analyst.max_operations}[/dim]")
    return {"analysts": analysts}

@vero_agent("Routing")
async def call_asyn_agent(state: DirectorState) -> List[Send]:
    """Send API calls to analyst tool agent"""
    analyst_states = []
    for analyst in state["analysts"].analysts:
        # Ensure the state passed matches AnalystState
        analyst_state_data = analyst.model_dump()
        # Pass necessary parts of DirectorState if needed by analyst_tool_agent
        analyst_state_data["director_id"] = state.get("director_id")
        analyst_state_data["thread_id"] = state.get("thread_id")
        analyst_state_data["successful_tools"] = {}
        # Pass the messages field as is
        analyst_state_data["messages"] = []
        # Ensure tool_logs starts empty for each analyst
        analyst_state_data["tool_logs"] = []
        analyst_states.append(analyst_state_data)
    # print how many analysts are being sent to the analyst tool agent
    print(f"Analysts being sent to analyst tool agent: {len(analyst_states)}")
    return [
        Send("analyst_tool_agent", analyst_state)
        for analyst_state in analyst_states
    ]

@vero_agent("Collecting")
def collect_reports(state: DirectorState):
    """Collecting reports after the analysts have finished their work"""
    # completed_reports is now automatically aggregated by the state definition
    # This node can be used for any final processing after aggregation, if needed.
    print(f"Number of reports collected: {len(state.get('completed_reports', [])) // 2}")
    # Tool logs are automatically aggregated by the state definition
    
    # print_pretty(state.get("completed_reports", [])) # Print the collected reports
    # Return an empty dict or specific updates if further processing happens here
    return {}

@vero_agent("Judging")
async def judge_analysis(state: DirectorState) -> DirectorState:
    """Let's get the judge's thinking process"""
    
    business_question = state.get("business_question", "Should we invest in Nvidia (NVDA) in the next 30 days?")
    completed_reports = state.get("completed_reports", [])
    director_id = state.get("director_id", "")
    
    # Get LLM for judge thinking
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "judge_thinking")
    
    # Count actual AI messages (reports)
    ai_message_count = sum(1 for msg in completed_reports if isinstance(msg, AIMessage))
    
    system_message = SystemMessage(content=f"""<role>
You are an Investment Analysis Judge tasked with developing comprehensive critical thinking based on research questions and findings to form analytical insights on an investment decision.
</role>

<context>
INVESTMENT QUESTION: {business_question}

You have received {ai_message_count} set(s) of research questions and findings from analysts. Your role is to synthesize these inputs, develop critical thinking around the evidence, identify patterns and gaps, but NOT to make a final investment recommendation - only to provide thorough analytical reasoning.
</context>

<critical_thinking_framework>
Your comprehensive critical analysis should:

1. **Synthesize Key Findings**: What substantive discoveries emerge from the research? What patterns do you observe?
2. **Evaluate Evidence Quality**: How robust and reliable are the findings? What methodologies were used?
3. **Identify Convergent Themes**: Where do multiple findings point to similar conclusions?
4. **Explore Divergent Perspectives**: What contradictions or tensions exist in the findings?
5. **Assess Information Completeness**: What critical questions remain unanswered? What would strengthen the analysis?
6. **Weigh Risk-Reward Dynamics**: Based on findings, what are the potential upsides and downsides?
7. **Consider Time Sensitivity**: How do the findings relate to the 30-day investment timeframe?
</critical_thinking_framework>

<analytical_approach>
- Build your analysis from the actual findings provided, not hypothetical scenarios
- If findings are sparse or incomplete, acknowledge this and explain the implications
- Connect disparate findings to form coherent investment narratives
- Challenge assumptions embedded in the research questions
- Maintain objectivity while developing nuanced perspectives
</analytical_approach>

<output_format>
Generate a structured critical thinking process that:
- Synthesizes the research findings into coherent themes
- Develops multiple investment perspectives based on evidence
- Identifies critical uncertainties and their implications
- Uses clear headings and logical progression of ideas
- Focuses on analytical depth rather than definitive conclusions
</output_format>""")
    
    human_message = HumanMessage(content="Based on the research questions and findings provided, develop comprehensive critical thinking that synthesizes the evidence, identifies patterns and gaps, and provides multi-faceted analytical perspectives on the investment question.")
    
    try:
        # Create messages for LLM with system message at position 0
        messages_for_llm = [system_message] + completed_reports + [human_message]
        response = await model.ainvoke(messages_for_llm)
        if "</think>" in response.content:
            judge_thinking = response.content.split("</think>", 1)[1].strip()
        else:
            judge_thinking = response.content
        print("We have heard the judge's thinking process, let's see what the jury has to say...")
        
        completed_reports.append(human_message)
        completed_reports.append(AIMessage(content=judge_thinking))
        # Return messages to be appended to completed_reports via add_messages
        return {
            "judge_thinking": judge_thinking, 
            "completed_reports": completed_reports
        }
        
    except Exception as e:
        print_debug(f"Error in judge thinking: {str(e)}", "Judge Error")
        return {"judge_thinking": "Judge thinking process failed - proceeding with jury reports as presented."}

@vero_agent("Improving")
def send_to_feature_requestor(state: DirectorState):
    """Are we missing something? Let's ask the feature requestor to help us bring more tools to the table"""

    # if feature_requestor contains we need to return feature_requestor
    for report in state.get("completed_reports", []):
        if "<feature_requestor>" in str(report): #check if feature_requestor is one of the reports
            print("Feature requestor detected, continuing to feature requestor node")
            return "feature_requestor"

    # This part is reached ONLY if the loop completes without finding the tag in ANY report
    print("No feature requestor detected, continuing to judge thinking node")
    return "judge_thinking"

@vero_agent("Collaborating")
async def jury_analysis(state: DirectorState) -> DirectorState:
    """Let's get the our jury's independent collective analysis"""

    business_question = state.get("business_question", "Should we invest in Nvidia (NVDA) in the next 30 days?")
    completed_reports = state.get("completed_reports", [])
    director_id = state.get("director_id", "")
    plan = state.get("plan")
    analysts = state.get("analysts")
    judge_thinking = state.get("judge_thinking", "No judge thinking available")

    # Get LLM for jury analysis
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "jury_analysis")

    # Extract task and analyst information
    tasks = plan.tasks if plan else []
    task_details = "\n".join([f"- {task}" for task in tasks]) if tasks else "None"

    analyst_details = ""
    if analysts and hasattr(analysts, 'analysts'):
        for i, analyst in enumerate(analysts.analysts):
            analyst_details += f"\nJUROR {i+1}:\n"
            analyst_details += f"- Name: {analyst.name_analyst}\n"
            analyst_details += f"- Role: {analyst.title_analyst}\n"
            analyst_details += f"- Background: {analyst.background_analyst}\n"
            analyst_details += f"- Evidence Assignment: {analyst.business_question}\n"

    # Count AI messages
    ai_message_count = sum(1 for msg in completed_reports if isinstance(msg, AIMessage))

    system_message = SystemMessage(content=f"""<role>
You are an independent Jury developing collaborative critical thinking that builds upon and challenges the evidence and judge's analysis through collective deliberation.
</role>

<context>
INVESTMENT QUESTION: {business_question}

RESEARCH ASSIGNMENTS:
{task_details}

JURY MEMBERS & EXPERTISE:
{analyst_details}

DELIBERATION CONTEXT:
- Total Jury Members: {len(analysts.analysts) if analysts and hasattr(analysts, 'analysts') else 0}
- Research Findings Presented: {ai_message_count}
- Judge's Critical Analysis: Available for review
</context>

<collaborative_critical_thinking_mandate>
As an independent jury, your role is to:
1. Think critically beyond what has been presented
2. Challenge assumptions in both the evidence and judge's analysis
3. Develop new perspectives through collaborative reasoning
4. Identify overlooked angles and unexplored implications
5. Build upon strong points while questioning weak arguments
</collaborative_critical_thinking_mandate>

<independent_deliberation_framework>
1. **Critical Evidence Review**: Don't just evaluate - challenge and extend the findings
2. **Judge's Analysis Critique**: Identify what the judge may have missed or overemphasized
3. **Collaborative Insight Generation**: Develop new insights that emerge from collective thinking
4. **Alternative Perspectives**: Present viewpoints that weren't considered by individual analysts or judge
5. **Synthesis Beyond Summary**: Create new understanding rather than just combining existing views
6. **Strategic Implications**: Think beyond the immediate question to broader implications
</independent_deliberation_framework>

<critical_thinking_approach>
- Question underlying assumptions in all analyses
- Identify cognitive biases that may have influenced conclusions
- Consider contrarian viewpoints and devil's advocate positions
- Explore interdependencies between different findings
- Develop scenarios not covered by individual analyses
- Challenge the framing of the original question itself
</critical_thinking_approach>

<output_format>
Generate independent collaborative critical thinking that:
- Critiques and extends the evidence with fresh perspectives
- Challenges the judge's thinking with reasoned counterpoints
- Develops novel insights through collective reasoning
- Identifies hidden risks and unexplored opportunities
- Proposes alternative analytical frameworks
- Questions fundamental assumptions about the investment decision
- Uses clear structure with bold headers for key insights
</output_format>""")

    human_message = HumanMessage(content="Engage in independent collaborative critical thinking that challenges assumptions, develops new perspectives, and creates insights beyond what the individual analyses and judge have presented.")


    # Create messages for LLM with system message at position 0
    messages_for_llm = [system_message] + completed_reports + [human_message]
    response = await model.ainvoke(messages_for_llm)
    if "</think>" in response.content:
        jury_thinking = response.content.split("</think>", 1)[1].strip()
    else:
        jury_thinking = response.content
    print("We have heard the jury's collective analysis, let's see what the verdict is...")
    
    completed_reports.append(human_message)
    completed_reports.append(AIMessage(content=jury_thinking))
    # Return messages to be appended to completed_reports via add_messages
    return {
        "jury_thinking": jury_thinking,
        "completed_reports": completed_reports
    }

@vero_agent("Verdicting")
async def verdict_trial(state: DirectorState) -> DirectorState:
    """Let's get the final verdict"""
    
    business_question = state.get("business_question", "Should we invest in Nvidia (NVDA) in the next 30 days?")
    completed_reports = state.get("completed_reports", [])
    director_id = state.get("director_id", "")
    
    # Get LLM for verdict
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "verdict_trial")
    
    system_message = SystemMessage(content=f"""<role>
You are the Chief Investment Officer synthesizing all research findings, critical analysis, and collaborative insights to make the final investment decision.
</role>

<context>
INVESTMENT QUESTION: {business_question}

You have reviewed:
- Research questions and findings from analysts
- Judge's critical thinking analysis synthesizing patterns and gaps
- Jury's independent collaborative insights challenging assumptions

Your role is to make the FINAL INVESTMENT DECISION based on the collective intelligence gathered.
</context>

<decision_framework>
Consider the full spectrum of analysis:
1. Original research findings and their quality
2. Critical perspectives developed by the judge
3. Novel insights and challenges raised by the jury
4. Risk-reward dynamics across different timeframes
5. Alternative scenarios and their probabilities
</decision_framework>

<verdict_requirements>
You MUST include ALL of the following information:
1. Extract the stock ticker symbol from the business question
2. Find the current/last closing price from research findings (or state if not available)
3. State a clear verdict: BUY, SELL, or HOLD
4. Provide confidence level (0-100%)
5. Set specific price target in dollars
6. Define time horizon in specific number of days
7. List key risk factors based on the critical analysis
8. Specify conditions that would invalidate the verdict
</verdict_requirements>

<output_format>
TICKER: [Extract ticker symbol from business question]
CURRENT PRICE: $[Extract from findings or state "Not available in research findings"]
VERDICT: [BUY/SELL/HOLD]
CONFIDENCE: [X]%
PRICE TARGET: $[Specific dollar amount]
TIME HORIZON: [X] days

RATIONALE:
[2-3 sentences synthesizing the core reasoning from all analyses]

KEY RISKS:
- [Risk 1 from critical analysis]
- [Risk 2 from alternative perspectives]
- [Risk 3 from identified gaps]

VERDICT CHANGE CONDITIONS:
- [Specific condition that would invalidate the investment thesis]
</output_format>""")
    
    human_message = HumanMessage(content="Based on all research findings, critical analyses, and collaborative insights, please render your final investment decision.")
    
    try:
        # Create messages for LLM with system message at position 0
        messages_for_llm = [system_message] + completed_reports + [human_message]
        response = await model.ainvoke(messages_for_llm)

        if "</think>" in response.content:
            verdict = response.content.split("</think>", 1)[1].strip()
        else:
            verdict = response.content
        
        completed_reports.append(human_message)
        completed_reports.append(AIMessage(content=verdict))
        print("We have heard the verdict, let's continue to finalizing the report")
        # Return messages to be appended to completed_reports via add_messages
        return {
            "final_verdict": verdict,
            "completed_reports": completed_reports
        }
        
    except Exception as e:
        print_debug(f"Error in verdict trial: {str(e)}", "Verdict Error")
        return {"final_verdict": "Verdict could not be rendered due to error."}

def create_delegate_subgraph() -> StateGraph:
    """Subgraph for delegation operations"""
    subgraph = StateGraph(DirectorState)
    subgraph.add_node("init_graph", init_graph)
    subgraph.add_node("output_memory", output_memory_graph)
    subgraph.add_node("critical_thinking_planning", critical_thinking_planning)
    subgraph.add_node("plan_forge", plan_forge_graph)
    subgraph.add_node("create_plan", create_plan)
    subgraph.add_node("create_personas", create_personas)
    subgraph.add_node("analyst_tool_agent", deep_search_graph)
    subgraph.add_node("collect_reports", collect_reports)
    subgraph.add_node("judge_analysis", judge_analysis)
    subgraph.add_node("feature_requestor", feature_requestor_graph)
    subgraph.add_node("jury_analysis", jury_analysis)
    subgraph.add_node("verdict_trial", verdict_trial)

    # Define the flow of the graph
    subgraph.add_edge(START, "init_graph")
    subgraph.add_edge("init_graph", "output_memory")
    subgraph.add_edge("output_memory", "critical_thinking_planning")
    subgraph.add_edge("critical_thinking_planning", "plan_forge")
    subgraph.add_edge("plan_forge", "create_plan")
    subgraph.add_edge("create_plan", "create_personas")
    # Change this edge to a conditional edge using call_asyn_agent as the decider
    subgraph.add_conditional_edges(
        "create_personas",
        call_asyn_agent, # This function now returns List[Send]
        ["analyst_tool_agent"] # The target node for the Send commands
    )
    # The analyst_tool_agent node now implicitly goes to collect_reports
    # when all parallel branches launched by Send complete.
    subgraph.add_edge("analyst_tool_agent", "collect_reports")
    subgraph.add_conditional_edges(
        "collect_reports",
        send_to_feature_requestor,
        {"feature_requestor": "feature_requestor", "judge_thinking": "judge_analysis"}
    )
    subgraph.add_edge("feature_requestor", "judge_analysis")
    subgraph.add_edge("judge_analysis", "jury_analysis")
    subgraph.add_edge("jury_analysis", "verdict_trial")
    subgraph.add_edge("verdict_trial", END)
    return subgraph.compile()

graph = create_delegate_subgraph()

__all__ = ['graph']