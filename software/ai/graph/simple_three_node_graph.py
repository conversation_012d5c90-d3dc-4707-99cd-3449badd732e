from langgraph.graph import StateGraph, START, END
from typing import TypedDict, Annotated
from typing_extensions import TypedDict
import operator
import time
import random


class SimpleState(TypedDict):
    """Simple state for the graph"""
    counter: Annotated[int, operator.add]
    route_data: str
    results: Annotated[list, operator.add]

def vero_agent(message: str = None):
    """Production-ready decorator for visualizing parallel graph execution with colored output.
    
    Args:
        message: Optional custom action message (e.g., "Processing", "Analyzing")
                If not provided, a random action verb will be selected.
    """
    from rich.console import Console
    from rich.padding import Padding
    from datetime import datetime
    from functools import wraps
    import threading
    import builtins
    
    # Initialize shared resources if not already done
    if not hasattr(vero_agent, '_initialized'):
        vero_agent._initialized = True
        vero_agent._console_lock = threading.Lock()
        vero_agent._assigned_colors = {}
        vero_agent._color_index = 0
        vero_agent._active_tasks = {}
        vero_agent._tasks_lock = threading.Lock()
        vero_agent._thread_local = threading.local()
        vero_agent._available_colors = [
            "bright_cyan", "bright_green", "bright_yellow", "bright_magenta",
            "bright_blue", "bright_red", "cyan", "green", "yellow", "magenta",
            "blue", "red", "bright_white", "orange1", "purple", "violet",
            "spring_green1", "dodger_blue1", "deep_pink1", "gold1", "orchid1",
            "turquoise2", "orange3", "light_coral", "salmon1", "dark_orange3",
            "plum1", "medium_purple1", "light_slate_blue", "light_pink1", "wheat1"
        ]
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            action_verbs = [
                "Analyzing", "Processing", "Computing", "Evaluating",
                "Optimizing", "Synthesizing", "Reasoning", "Learning"
            ]
            
            func_name = func.__name__
            readable_name = ' '.join(word.capitalize() for word in func_name.split('_'))
            
            # Assign color to this function if not already assigned
            if func_name not in vero_agent._assigned_colors:
                vero_agent._assigned_colors[func_name] = vero_agent._available_colors[
                    vero_agent._color_index % len(vero_agent._available_colors)
                ]
                vero_agent._color_index += 1
            node_color = vero_agent._assigned_colors[func_name]
            
            action = message if message else random.choice(action_verbs)
            action_base = action.rstrip(".")
            
            console = Console()
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            # Build and print header
            header_lines = []
            header_lines.append(f"\n[{node_color}]● {readable_name}[/{node_color}] [dim]started at {timestamp}[/dim]")
            
            if func.__doc__:
                doc_lines = func.__doc__.strip().split('\n')
                for doc_line in doc_lines:
                    header_lines.append(f"  [dim italic]{doc_line.strip()}[/dim italic]")
            
            spinner_chars = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
            spinner_char = spinner_chars[int(time.time() * 10) % len(spinner_chars)]
            header_lines.append(f"  [{node_color}]{spinner_char}[/{node_color}] [italic]{action_base.lower()}[/italic]")
            
            with vero_agent._console_lock:
                console.print("\n".join(header_lines))
            
            with vero_agent._tasks_lock:
                vero_agent._active_tasks[func_name] = {
                    'name': readable_name,
                    'action': action_base.lower(),
                    'color': node_color,
                    'start_time': time.time()
                }
            
            start_time = time.time()
            original_print = print
            original_console_print = console.print
            captured_color = node_color
            
            def make_indented_print(color):
                def indented_print(*args, **kwargs):
                    if len(args) == 1 and hasattr(args[0], '__rich_console__'):
                        padded = Padding(args[0], pad=(0, 0, 0, 4))
                        with vero_agent._console_lock:
                            original_console_print(padded)
                    else:
                        message = " ".join(str(arg) for arg in args)
                        lines = message.split('\n')
                        with vero_agent._console_lock:
                            for line in lines:
                                original_console_print(f"  [{color}]│[/{color}] {line}")
                return indented_print
            
            node_print = make_indented_print(captured_color)
            vero_agent._thread_local.node_color = captured_color
            vero_agent._thread_local.console_print = original_console_print
            
            def thread_aware_print(*args, **kwargs):
                if hasattr(vero_agent._thread_local, 'node_color'):
                    color = vero_agent._thread_local.node_color
                    console_print = vero_agent._thread_local.console_print
                    message = " ".join(str(arg) for arg in args)
                    lines = message.split('\n')
                    with vero_agent._console_lock:
                        for line in lines:
                            console_print(f"  [{color}]│[/{color}] {line}")
                else:
                    original_print(*args, **kwargs)
            
            orig_builtin_print = builtins.print
            builtins.print = thread_aware_print
            console.print = lambda *args, **kwargs: node_print(*args, **kwargs)
            
            try:
                result = func(*args, **kwargs)
                
                elapsed = time.time() - start_time
                time_str = f"{elapsed:.2f}s" if elapsed < 1 else f"{int(elapsed)}s"
                
                builtins.print = orig_builtin_print
                console.print = original_console_print
                
                if hasattr(vero_agent._thread_local, 'node_color'):
                    del vero_agent._thread_local.node_color
                if hasattr(vero_agent._thread_local, 'console_print'):
                    del vero_agent._thread_local.console_print
                
                with vero_agent._tasks_lock:
                    if func_name in vero_agent._active_tasks:
                        del vero_agent._active_tasks[func_name]
                
                end_timestamp = datetime.now().strftime("%H:%M:%S")
                with vero_agent._console_lock:
                    console.print(f"[{node_color}]✓ {readable_name} {action_base.lower()}[/{node_color}] [dim]completed in {time_str} at {end_timestamp}[/dim]\n")
                
                return result
            except Exception as e:
                builtins.print = orig_builtin_print
                console.print = original_console_print
                
                if hasattr(vero_agent._thread_local, 'node_color'):
                    del vero_agent._thread_local.node_color
                if hasattr(vero_agent._thread_local, 'console_print'):
                    del vero_agent._thread_local.console_print
                
                elapsed = time.time() - start_time
                time_str = f"{int(elapsed)}s" if elapsed >= 1 else f"{elapsed:.2f}s"
                
                with vero_agent._tasks_lock:
                    if func_name in vero_agent._active_tasks:
                        del vero_agent._active_tasks[func_name]
                
                end_timestamp = datetime.now().strftime("%H:%M:%S")
                with vero_agent._console_lock:
                    console.print(f"[red]✗ {readable_name}[/red] [dim]failed in {time_str} at {end_timestamp}[/dim]")
                    console.print(f"  [dim red]Error: {str(e)}[/dim red]\n")
                raise e
                
        return wrapper
    return decorator




@vero_agent("Analyzing")
def node_one(state: SimpleState) -> SimpleState:
    """First node - processes initial data
    lorem ipsum dolor sit amet123123123123
    consectetur adipiscing elit123123123
    """
    print("Processing initial data")
    print("Processing initial data")
    print("""Processing initial data\n lorem ipsum dolor sit amet\n consectetur adipiscing elit""")
    print("""Processing initial data\n lorem ipsum dolor sit amet\n consectetur adipiscing elit""")
    
    time.sleep(random.uniform(5, 9))


    return {
        "counter": 1,
        "results": ["Node One: Processed initial data"]
    }


@vero_agent("Researching")
def node_two(state: SimpleState) -> SimpleState:
    """Second node - analyzes research data"""
    print("Analyzing research data")
    time.sleep(random.uniform(2, 4))
    return {
        "counter": 10,
        "results": ["Node Two: Analyzed research data"]
    }


@vero_agent("Finding")
def node_three(state: SimpleState) -> SimpleState:
    """Third node - finalizes results"""
    print("Finalizing results")
    time.sleep(random.uniform(2, 4))
    return {
        "counter": 100,
        "results": ["Node Three: Finalized results"]
    }


@vero_agent("Validating")
def node_four(state: SimpleState) -> SimpleState:
    """Fourth node - validates data"""
    print("Validating data integrity")
    time.sleep(random.uniform(2, 4))
    return {
        "counter": 1000,
        "results": ["Node Four: Data validated"]
    }


@vero_agent("Transforming")
def node_five(state: SimpleState) -> SimpleState:
    """Fifth node - transforms data"""
    print("Transforming data format")
    time.sleep(random.uniform(2, 4))
    return {
        "counter": 10000,
        "results": ["Node Five: Data transformed"]
    }


@vero_agent("Caching")
def node_six(state: SimpleState) -> SimpleState:
    """Sixth node - caches results"""
    print("Caching processed results")
    time.sleep(random.uniform(2, 4))
    return {
        "counter": 100000,
        "results": ["Node Six: Results cached"]
    }


@vero_agent("Routing")
def router_node(state: SimpleState) -> SimpleState:
    """Router node - distributes work to all three nodes"""
    print("Distributing tasks to all processing nodes")
    time.sleep(random.uniform(1, 2))
    return {
        "route_data": "Task distributed to all nodes",
        "counter": state.get("counter", 0)
    }


@vero_agent("Collecting")
def collector_node(state: SimpleState) -> SimpleState:
    """Collector node - aggregates results from all three nodes"""
    print("Aggregating results from all processing nodes")
    time.sleep(random.uniform(1, 2))
    
    # The state already has aggregated results and counter
    results = state.get("results", [])
    counter = state.get("counter", 0)
    
    print(f"Collected {len(results)} results")
    print(f"Total processing value: {counter}")
    
    # Show all results
    for result in results:
        print(f"- {result}")
    
    # Add final summary
    return {
        "results": [f"Collector: Final aggregation complete with counter={counter}"]
    }


def create_simple_graph() -> StateGraph:
    """Create a graph with router distributing to 6 nodes, then collecting results"""
    builder = StateGraph(SimpleState)
    
    # Add all nodes
    builder.add_node("router", router_node)
    builder.add_node("node_one", node_one)
    builder.add_node("node_two", node_two)
    builder.add_node("node_three", node_three)
    builder.add_node("node_four", node_four)
    builder.add_node("node_five", node_five)
    builder.add_node("node_six", node_six)
    builder.add_node("collector", collector_node)
    
    # Router distributes to all nodes
    builder.add_edge(START, "router")
    builder.add_edge("router", "node_one")
    builder.add_edge("router", "node_two")
    builder.add_edge("router", "node_three")
    builder.add_edge("router", "node_four")
    builder.add_edge("router", "node_five")
    builder.add_edge("router", "node_six")
    
    # All nodes send to collector
    builder.add_edge("node_one", "collector")
    builder.add_edge("node_two", "collector")
    builder.add_edge("node_three", "collector")
    builder.add_edge("node_four", "collector")
    builder.add_edge("node_five", "collector")
    builder.add_edge("node_six", "collector")
    
    # Collector to end
    builder.add_edge("collector", END)
    
    return builder.compile()


graph = create_simple_graph()

__all__ = ['graph', 'vero_agent']


if __name__ == "__main__":
    """Main function to run the graph"""
    print("Starting Router-Collector Graph")
    
    # Initialize state
    initial_state = {"counter": 0, "route_data": "", "results": []}
    
    try:
        # Run the graph
        result = graph.invoke(initial_state)
        print(f"\nFinal counter value: {result['counter']}")
        print("Graph execution completed")
    except Exception as e:
        print(f"\nGraph execution failed: {str(e)}")