import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from typing import Dict, Any, List, TypedDict
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, ToolMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, START, END
from software.ai.graph.mongodbsaver import VeroMongoDBSaver
from software.ai.llm.llm_connect import get_llm_connect
from langgraph.prebuilt import create_react_agent
from langmem import create_prompt_optimizer
from software.ai.graph.director_state import vero_agent
from pydantic import BaseModel, Field


class VeroAgent:
    """Self-tuning ReAct agent with automatic prompt optimization"""
    
    class EvaluationResult(BaseModel):
        """Model for structured evaluation of prompt optimization"""
        score: float = Field(
            ge=0, 
            le=100, 
            description="Overall performance score from 0-100 based on how well the agent met ALL requirements"
        )
        reasoning: str = Field(
            description="Comprehensive analysis explaining the score, including specific examples from the agent's response that demonstrate success or failure"
        )
        adherence_to_prompt: str = Field(
            description="Detailed assessment of prompt compliance: Did the agent follow formatting requirements? Did it use the specified tone/role? Include specific violations or successes with examples"
        )
        adherence_to_task: str = Field(
            description="Detailed assessment of task completion: Did the agent provide the correct answer? Was the approach appropriate? Include what was done correctly vs incorrectly with specific evidence"
        )
        adherence_to_parameters: str = Field(
            description="Detailed assessment of parameter compliance: Did the agent meet each evaluation parameter? List each parameter and whether it was satisfied with specific examples from the response"
        )

    class OptimizerState(TypedDict):
        """State for prompt optimization workflow"""
        prompts: List[str]
        task: str
        conversation: List[BaseMessage]
        evaluation_result: List[Dict[str, Any]]
        feedback: str
        eval_parameters: str
        optimized: bool
        score_threshold: int
        max_attempts: int
        current_prompt_idx: int
        attempts: int
    
    def __init__(self, evaluator_model, feedback_model, optimize_model, agent_executor=None, checkpointer=None):
        self.evaluator_model = evaluator_model
        self.feedback_model = feedback_model
        self.optimize_model = optimize_model
        self.agent_executor = agent_executor
        self.checkpointer = checkpointer
        self._graph = None
    
    async def tune(self, initial_prompt: str = None, task: str = None, eval_parameters: str = None, config: Dict[str, Any] = None, score_threshold: int = 100, max_attempts: int = 3) -> Dict[str, Any]:
        """Tune the agent's prompt for optimal task performance.
        
        Args:
            initial_prompt: Starting prompt (if None, uses checkpoint value)
            task: The task for the agent to perform
            eval_parameters: Evaluation criteria for scoring
            config: Optional LangGraph config (defaults to standard settings)
            score_threshold: Target score to achieve (0-100, default 100)
            
        Returns:
            Optimization result with final prompt and scores
        """
        if self._graph is None:
            self._graph = self.create_graph()
        
        if config is None:
            config = {
                "configurable": {"thread_id": "vero-agent"},
                "recursion_limit": 50
            }
        
        initial_state = {
            "prompts": [initial_prompt] if initial_prompt is not None else [],
            "task": task,
            "eval_parameters": eval_parameters,
            "conversation": [],
            "evaluation_result": [],
            "feedback": "",
            "optimized": False,
            "score_threshold": score_threshold,
            "max_attempts": max_attempts,
            "attempts": 0
        }
        
        return await self._graph.ainvoke(initial_state, config)
    
    
    def print_conversation(self, messages: List[BaseMessage]) -> str:
        """Format conversation messages as a string"""
        import json
        
        lines = []
        
        for msg in messages:
            if isinstance(msg, HumanMessage):
                lines.append(f"👤 Human: {msg.content}")
                
            elif isinstance(msg, AIMessage):
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    for tool_call in msg.tool_calls:
                        tool_name = tool_call.get('name', 'unknown')
                        lines.append(f"🤖 AI calls {tool_name} tool")
                        
                        args = tool_call.get('args', {})
                        if isinstance(args, dict) and args:
                            for key, value in args.items():
                                if isinstance(value, str):
                                    if 'code' in key.lower() and '\n' in value:
                                        lines.append(f"   with {key}:")
                                        for line in value.split('\n'):
                                            if line.strip():
                                                lines.append(f"      {line}")
                                    else:
                                        lines.append(f"   with {key}: {value}")
                                else:
                                    lines.append(f"   with {key}: {value}")
                            
                elif msg.content:
                    if msg.content.strip().startswith('{'):
                        try:
                            data = json.loads(msg.content)
                            if data.get('type') == 'function' or 'name' in data:
                                tool_name = data.get('name', 'unknown')
                                lines.append(f"🤖 AI tries to call {tool_name} (malformed)")
                                params = data.get('parameters', {})
                                if isinstance(params, dict):
                                    for key, value in params.items():
                                        if isinstance(value, str) and 'code' in key.lower() and '\n' in value:
                                            lines.append(f"   with {key}:")
                                            for line in value.split('\n'):
                                                if line.strip():
                                                    lines.append(f"      {line}")
                                        else:
                                            lines.append(f"   with {key}: {value}")
                            else:
                                lines.append(f"🤖 AI: {msg.content}")
                        except:
                            lines.append(f"🤖 AI: {msg.content}")
                    else:
                        lines.append(f"🤖 AI: {msg.content}")
                        
            elif isinstance(msg, ToolMessage):
                lines.append(f"🔧 Tool [{msg.name}] output: {msg.content}")
                    
            else:
                msg_type = type(msg).__name__
                lines.append(f"📨 {msg_type}: {getattr(msg, 'content', '[no content]')}")
        
        return '\n'.join(lines)

    @vero_agent("Agenting")
    async def run_react_agent(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Run the react agent with current prompt"""
        
        prompts = state.get("prompts", ["You are an assistant."])
        evaluation_results = state.get("evaluation_result", [])
        
        # Always use the latest prompt
        current_prompt = prompts[-1] if prompts else "You are an assistant."
        current_prompt_idx = len(prompts) - 1 if prompts else 0
        
        task = state.get("task", "1+1?")
        
        print(f"Using prompt at index {current_prompt_idx}: {current_prompt[:100]}...")
        
        result = await self.agent_executor.ainvoke({
            "messages": [("user", task)],
            "prompt": current_prompt
        })
    
        
        return {
            "conversation": result["messages"],
            "current_prompt_idx": current_prompt_idx
        }

    @vero_agent("Evaluating")
    async def evaluate_result(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Evaluate the system prompt and agent response against eval parameters"""
        
        prompts = state.get("prompts", [])
        evaluation_results = state.get("evaluation_result", [])
        current_idx = state.get("current_prompt_idx", 0)
        
        # Get current prompt being evaluated
        prompt = prompts[current_idx] if current_idx < len(prompts) else prompts[-1]
        print("Current prompt: ", prompt)
        
        # Build historical context from previous evaluations
        history_context = ""
        num_evals = len(evaluation_results)
        
        # Show last 2 evaluations for relative scoring
        if num_evals >= 1:
            idx = num_evals - 1
            history_context = f"\nPrevious attempt:\nPrompt: {prompts[idx][:100]}...\nScore: {evaluation_results[idx].get('score', 0)}/100"
        
        if num_evals >= 2:
            idx = num_evals - 2
            history_context += f"\n\nAttempt before that:\nPrompt: {prompts[idx][:100]}...\nScore: {evaluation_results[idx].get('score', 0)}/100"
        
        conversation = state.get("conversation", [])
        eval_parameters = state.get("eval_parameters", "")
            
        structured_model = self.evaluator_model.with_structured_output(self.EvaluationResult)
        
        formatted_conversation = self.print_conversation(conversation)
        
        eval_message = f"""<evaluation_task>
You are evaluating an AI agent's performance based on specific criteria.

<input_data>
<system_prompt>
{prompt}
</system_prompt>

<evaluation_parameters>
{eval_parameters}
</evaluation_parameters>

<agent_conversation>
{formatted_conversation}
</agent_conversation>{history_context and f'''

<historical_context>
{history_context}
</historical_context>''' or ''}
</input_data>

<evaluation_process>
1. Quote specific parts of the agent's response when evaluating
2. Compare what the agent did vs what was required
3. List each requirement and whether it was met with evidence
4. Provide detailed explanations with examples for each assessment
5. Be comprehensive and specific - avoid vague statements
6. If historical context is provided, consider relative improvement or regression
</evaluation_process>

<required_output>
IMPORTANT: Each field must contain detailed analysis with specific examples.
For each adherence field, you MUST:
- Quote relevant parts of the agent's response
- Explain exactly what was required vs what was delivered
- Provide specific evidence for your assessment
- Avoid brief or generic statements
</required_output>

<output_format>
{self.EvaluationResult.model_json_schema()}
</output_format>
</evaluation_task>"""
            
        evaluation = await structured_model.ainvoke([HumanMessage(content=eval_message)])
        
        current_results = state.get("evaluation_result", [])
        new_results = current_results + [evaluation.model_dump()]
        print("Eval Message: ",eval_message)
        print("Score: ",evaluation.score)
        print("Reasoning: ", evaluation.reasoning)
        print("Adherence to Prompt:", evaluation.adherence_to_prompt)
        print("Adherence to Task:", evaluation.adherence_to_task)
        print("Adherence to Parameters:", evaluation.adherence_to_parameters)
        
        attempts = state.get("attempts", 0) + 1
        return {"evaluation_result": new_results, "attempts": attempts}

    @vero_agent("Feedbacking")
    async def provide_feedback(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Generate feedback for optimization"""
        
        evaluation_results = state.get("evaluation_result", [])
        prompts = state.get("prompts", [])
        latest_result = evaluation_results[-1] if evaluation_results else {}
        score = latest_result.get("score", 0)
        
        score_threshold = state.get("score_threshold", 100)
        if score >= score_threshold:
            return {"feedback": "", "optimized": True}
            
        # Get current prompt and historical prompts
        current_prompt = prompts[-1] if prompts else ""
        conversation = state.get("conversation", [])
        eval_params = state.get("eval_parameters", "")
        
        formatted_conversation = self.print_conversation(conversation)
        
        # Build comprehensive context for human-like feedback
        historical_context = ""
        num_attempts = len(evaluation_results)
        
        # Extract patterns from previous attempts
        all_scores = [r.get('score', 0) for r in evaluation_results]
        score_trend = "improving" if len(all_scores) > 1 and all_scores[-1] > all_scores[-2] else "declining" if len(all_scores) > 1 and all_scores[-1] < all_scores[-2] else "stable"
        
        # Format current conversation for context
        current_conversation_formatted = self.print_conversation(conversation)
        
        if num_attempts >= 1:
            idx = num_attempts - 1
            historical_context = f"\nPrevious attempt (Attempt {idx + 1}):\nPrompt: {prompts[idx][:200]}...\nScore: {evaluation_results[idx].get('score', 0)}/100\nKey issues: {evaluation_results[idx].get('reasoning', '')[:100]}..."
        
        if num_attempts >= 2:
            idx = num_attempts - 2
            historical_context += f"\n\nAttempt before that (Attempt {idx + 1}):\nPrompt: {prompts[idx][:200]}...\nScore: {evaluation_results[idx].get('score', 0)}/100"
        
        # Add pattern insights
        historical_context += f"\n\nPattern Analysis:\n- Score trend: {score_trend} ({' → '.join(map(str, all_scores))})\n- Total attempts so far: {num_attempts}\n- Max attempts allowed: {state.get('max_attempts', 3)}"
        
        # Format evaluation results as natural language
        eval_text = f"""Score: {latest_result.get('score', 0)}/100
Reasoning: {latest_result.get('reasoning', 'N/A')}
Prompt Adherence: {latest_result.get('adherence_to_prompt', 'N/A')}
Task Adherence: {latest_result.get('adherence_to_task', 'N/A')}
Parameter Adherence: {latest_result.get('adherence_to_parameters', 'N/A')}"""
        
        feedback_prompt = f"""<feedback_task>
Generate concise, specific feedback to improve the prompt based on the evaluation.

<current_prompt>
{current_prompt}
</current_prompt>

<evaluation_results>
{eval_text}
</evaluation_results>

<evaluation_parameters>
{eval_params}
</evaluation_parameters>

<current_agent_conversation>
{current_conversation_formatted}
</current_agent_conversation>{historical_context and f'''

<historical_attempts>
{historical_context}
</historical_attempts>''' or ''}

<instructions>
1. Act as a human reviewer analyzing the optimization progress
2. Focus on the most critical issues preventing success
3. Provide 3-5 specific, actionable improvements 
4. Keep suggestions brief and practical
5. Consider the pattern: Are scores improving? Is the agent's behavior changing?
6. If scores are declining despite prompt changes, identify why (e.g., "The agent seems to ignore the prompt entirely")
7. Be honest if the optimization isn't working (e.g., "Despite detailed prompts, the agent continues using the same approach")
8. Suggest strategic pivots if needed, not just incremental changes
</instructions>
</feedback_task>"""
        
        response = await self.feedback_model.ainvoke([HumanMessage(content=feedback_prompt)])
        if "</think>" in response.content:
            feedback = response.content.split("</think>", 1)[1].strip()
        else:
            feedback = response.content

        # print("feedbackprompt: ", feedback_prompt)
        # print("feedback results: ",feedback)
            
        return {"feedback": feedback}

    @vero_agent("Optimizing")
    async def optimize_prompt(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Optimize the prompt based on feedback"""
        
        prompts = state.get("prompts", ["You are an assistant."])
        evaluation_results = state.get("evaluation_result", [])
        feedback = state.get("feedback", "")
        task = state.get("task", "")
        eval_parameters = state.get("eval_parameters", "")
        conversation = state.get("conversation", [])
        
        if not feedback:
            return {"optimized": True}
        
        # Always optimize from the latest prompt
        current_prompt = prompts[-1] if prompts else "You are an assistant."
        
        # Build comprehensive context for the optimizer
        formatted_conversation = self.print_conversation(conversation)
        
        # Get historical context
        historical_context = ""
        for i, (prompt, result) in enumerate(zip(prompts, evaluation_results)):
            score = result.get('score', 0)
            historical_context += f"\nAttempt {i+1}:\nPrompt: {prompt[:150]}...\nScore: {score}/100\n"
        
        optimization_prompt = f"""<optimization_task>
You are a human prompt engineer optimizing a system prompt for an AI agent.

<current_prompt>
{current_prompt}
</current_prompt>

<task_description>
The agent needs to: {task}
</task_description>

<evaluation_criteria>
These are the regulatory requirements that MUST be met:
{eval_parameters}
</evaluation_criteria>

<latest_conversation>
This is what happened when the agent used the current prompt:
{formatted_conversation}
</latest_conversation>

<feedback_from_reviewer>
{feedback}
</feedback_from_reviewer>

<historical_attempts>
{historical_context}
</historical_attempts>

<instructions>
1. Analyze why the current prompt is failing based on the conversation and feedback
2. Create an improved prompt that addresses the specific issues raised
3. Ensure the new prompt clearly instructs the agent to meet ALL evaluation criteria
4. Keep the prompt clear and actionable - don't make it overly complex
5. If the feedback suggests the agent is ignoring prompts entirely, try a radically different approach
6. Return ONLY the new optimized prompt text, nothing else
</instructions>
</optimization_task>"""
        
        response = await self.optimize_model.ainvoke([HumanMessage(content=optimization_prompt)])
        optimized_prompt = response.content.strip()
        
        # Remove any wrapper text if the model added explanation
        if optimized_prompt.startswith("Here") or optimized_prompt.startswith("The"):
            lines = optimized_prompt.split('\n')
            # Find the actual prompt content
            for i, line in enumerate(lines):
                if line.strip() and not line.startswith(("Here", "The", "This", "I")):
                    optimized_prompt = '\n'.join(lines[i:])
                    break
        
        # Append the new prompt to the list
        new_prompts = prompts + [optimized_prompt]
            
        return {
            "prompts": new_prompts,
            "optimized": True
        }

    def should_continue(self, state: "VeroAgent.OptimizerState") -> str:
        """Decide whether to continue optimization"""
        evaluation_results = state.get("evaluation_result", [])
        latest_result = evaluation_results[-1] if evaluation_results else {}
        score = latest_result.get("score", 0)
        
        score_threshold = state.get("score_threshold", 100)
        max_attempts = state.get("max_attempts", 3)
        attempts = state.get("attempts", 0)
        
        # Check if we reached score threshold
        if score >= score_threshold:
            return "end"
        
        # Check if we reached max attempts
        if attempts >= max_attempts:
            return "end"
            
        return "optimize"
    
    def create_graph(self) -> StateGraph:
        """Create optimizer graph with 4 nodes"""
        builder = StateGraph(self.OptimizerState)
        
        builder.add_node("run_agent", self.run_react_agent)
        builder.add_node("evaluate", self.evaluate_result)
        builder.add_node("provide_feedback", self.provide_feedback)
        builder.add_node("optimize", self.optimize_prompt)
        
        builder.add_edge(START, "run_agent")
        builder.add_edge("run_agent", "evaluate")
        builder.add_edge("evaluate", "provide_feedback")
        
        builder.add_conditional_edges(
            "provide_feedback",
            self.should_continue,
            {
                "optimize": "optimize",
                "end": END
            }
        )
        
        builder.add_edge("optimize", "run_agent")
        
        return builder.compile(checkpointer=self.checkpointer)

async def main():
    """Demonstrates self-tuning VeroAgent with automatic prompt optimization."""

    @tool
    def python_sandbox(code: str) -> str:
        """Execute Python code in a sandbox environment.
        
        Args:
            code: Python code to execute
            
        Returns:
            The output of the code execution
        """
        try:
            namespace = {}
            exec(code, namespace)
            
            if 'result' in namespace:
                return str(namespace['result'])
            else:
                return "Code executed successfully"
        except Exception as e:
            return f"Error: {str(e)}"

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()
    evaluator_model = model
    feedback_model = model
    optimize_model = model
    
    agent_executor = create_react_agent(
        model=model,
        tools=[python_sandbox],
    )
    
    agent = VeroAgent(
        evaluator_model=evaluator_model, #must support structure output
        feedback_model=feedback_model, #can be thinking model
        optimize_model=optimize_model, #must support long context
        agent_executor=agent_executor,
        checkpointer=VeroMongoDBSaver()
    )
    
    
    result = await agent.tune(
        initial_prompt="You are helpful assistant.",
        task="How many words in this sentence, given we are now in 2025?",
        eval_parameters="Correctness: Must provide exact answer, which is 11 • Must use tools, using XML format • Do not leak the answer in the prompt",
        config = {
        "configurable": {"thread_id": "vero-agent-test"},
        "recursion_limit": 50
        },
        score_threshold=100,
        max_attempts=3
    )
    
    # Get the best prompt based on scores
    evaluation_results = result.get('evaluation_result', [])
    prompts = result.get('prompts', [])
    
    if evaluation_results:
        best_idx = max(range(len(evaluation_results)), 
                      key=lambda i: evaluation_results[i].get("score", 0))
        best_prompt = prompts[best_idx] if best_idx < len(prompts) else prompts[-1]
        best_score = evaluation_results[best_idx].get("score", 0)
    else:
        best_prompt = prompts[-1] if prompts else "No prompt"
        best_score = 0
    
    print(f"🎯 Best prompt (score: {best_score:.0f}): {best_prompt}")
    scores = [f"{e['score']:.0f}" for e in evaluation_results]
    print(f"📊 All scores: {', '.join(scores)}")
    
    # Print the agent's final conversation
    print(f"\n{'='*50}")
    print("🤖 Agent's Final Output:")
    print(agent.print_conversation(result['conversation']))
    

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())