from typing import Dict, Any, List, TypedDict, Annotated, TYPE_CHECKING
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, BaseMessage
from langmem import create_prompt_optimizer
from software.ai.llm.llm_connect import get_llm_connect
from langgraph.store.memory import InMemoryStore
from langgraph.prebuilt import create_react_agent
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from software.ai.graph.director_state import vero_agent
from software.ai.graph.mongodbsaver import VeroMongoDBSaver
from pydantic import BaseModel, Field
import asyncio

if TYPE_CHECKING:
    from langgraph.checkpoint.base import BaseCheckpointSaver

# Global configuration variables
INITIAL_PROMPT = "You are a helpful assistant. Always be concise and clear."
EVAL_PARAMETERS = """Key evaluation parameters:
- Non-bias: 100% (responses should be unbiased)
- Universal: 100% (responses should work for all users)
- Dynamic: 100% (responses should adapt to context)
- No hardcoding: 100% (responses should be flexible, not hardcoded)"""


store = InMemoryStore()

# State definition for the optimizer workflow
class OptimizerState(TypedDict):
    """State for prompt optimization workflow"""
    messages: Annotated[List[BaseMessage], add_messages]
    task: str
    current_iteration: int
    agent_result: Dict[str, Any]
    feedback: str
    optimized_prompt: str
    eval_parameters: str

# Pydantic model for evaluation results
class EvaluationResult(BaseModel):
    """Model for structured evaluation of prompt optimization"""
    score: float = Field(ge=0, le=100, description="Score from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the score")
    adherence_to_prompt: str = Field(description="How well the agent followed the optimized system prompt")
    adherence_to_feedback: str = Field(description="How well the optimized prompt addressed the user's feedback intent")
    adherence_to_parameters: str = Field(description="How well it met evaluation parameters")

@vero_agent("Running Agent")
async def run_agent_with_task(state: OptimizerState) -> Dict[str, Any]:
    """Run the React agent with the current prompt from store"""
    task = state.get("task", "Write a short poem about coding")
    tools = []  # Default to empty tools list
    
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()
    
    # First check if we have an optimized prompt in state
    current_prompt = state.get("optimized_prompt", INITIAL_PROMPT)
    iteration = state.get("current_iteration", 0)
    
    # If not in state or still initial, try the store
    if current_prompt == INITIAL_PROMPT:
        try:
            current_item = store.get(("agent_prompt",), key="current")
            if current_item:
                current_prompt = current_item.value['prompt']
                iteration = current_item.value.get("iteration", 0)
        except:
            pass
    
    # Create prompt function for the agent
    def agent_prompt(state):
        sys_prompt = {"role": "system", "content": current_prompt}
        return [sys_prompt] + state['messages']
    
    # Create React agent with store
    agent = create_react_agent(
        model,
        prompt=agent_prompt,
        tools=tools or [],
        store=store,  # This enables memory in LangGraph Studio
        name="self_improving_agent",
        debug=False
    )
    
    # Run agent with ONLY the current task - don't include historical messages
    # The agent should always see the task fresh, without conversation history
    result = await agent.ainvoke({
        "messages": [{"role": "user", "content": task}]
    })
    
    messages = result["messages"]
    
    # Find the last user message and the response after it
    last_user_idx = -1
    for i in range(len(messages) - 1, -1, -1):
        if hasattr(messages[i], 'type') and messages[i].type == "human":
            last_user_idx = i
            break
        elif isinstance(messages[i], dict) and messages[i].get("role") == "user":
            last_user_idx = i
            break
    
    # Display last exchange
    if last_user_idx >= 0:
        for msg in messages[last_user_idx:]:
            if hasattr(msg, 'type'):
                msg_type = msg.type
                if msg_type in ["human", "ai", "system"]:
                    role_emoji = {"human": "👤 User", "ai": "🤖 Assistant", "system": "⚙️ System"}
                    print(f"\n{role_emoji[msg_type]}: {msg.content}")
            elif isinstance(msg, dict):
                role = msg.get("role", "unknown")
                if role in ["user", "assistant"]:
                    role_display = "👤 User" if role == "user" else "🤖 Assistant"
                    print(f"\n{role_display}: {msg.get('content', '')}")
    
    # Get existing messages and append new ones
    existing_messages = state.get("messages", [])
    new_messages = result["messages"]
    
    return {
        "agent_result": {
            "result": result,
            "current_prompt": current_prompt,
            "iteration": iteration
        },
        "current_iteration": state.get("current_iteration", 0) + 1,
        "messages": existing_messages + new_messages  # Accumulate all messages
    }

@vero_agent("Collecting Feedback")
async def get_human_feedback(state: OptimizerState) -> Dict[str, Any]:
    """Collect human feedback directly"""
    print("\n" + "="*50)
    feedback = input("\n💭 Enter feedback (or 'exit' to quit): ").strip()
    
    if feedback.lower() == 'exit':
        print("\n👋 Goodbye!")
        import sys
        sys.exit(0)
    
    return {"feedback": feedback}

@vero_agent("Optimizing Prompt")
async def optimize_and_store(state: OptimizerState) -> Dict[str, Any]:
    """Optimize the prompt based on feedback and store it"""
    agent_result = state.get("agent_result", {})
    feedback = state.get("feedback", "")
    task = state.get("task", "")
    
    # Safety check - if no feedback, return early
    if not feedback:
        print("⚠️ No feedback provided, skipping optimization")
        return {}
    
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()
    
    # Create optimizer with explicit instruction to incorporate feedback
    optimizer = create_prompt_optimizer(
        model,
        kind="gradient",
        config={
            "max_reflection_steps": 3,
            "min_reflection_steps": 1,
        }
    )
    
    # Get conversation from state - this now includes ALL historical messages
    conversation = state.get("messages", [])
    current_prompt = agent_result["current_prompt"]
    iteration = agent_result["iteration"]
    
    # Create enhanced feedback using Anthropic best practices
    enhanced_feedback_xml = f"""<feedback_analysis>
<user_feedback>{feedback or "Good response"}</user_feedback>
<task_context>The user repeatedly asks: "{task}"</task_context>
<current_prompt>{current_prompt}</current_prompt>

<revision_instructions>
1. REVISE the system prompt, do NOT just append new instructions
2. Identify conflicting or outdated parts of the current prompt
3. Replace or update specific sections based on the user feedback
4. Maintain core functionality while incorporating new requirements
5. Ensure the prompt remains concise and doesn't grow indefinitely
</revision_instructions>

<revision_approach>
<identify_conflicts>
Look for any part of the current prompt that contradicts the user's feedback.
For example, if the prompt says "sign with Radi" but user wants "sign with Idan", 
REPLACE the old instruction entirely.
</identify_conflicts>

<consolidate_instructions>
If similar instructions exist, merge them into a single, clear directive.
Avoid having multiple overlapping rules that could confuse the model.
</consolidate_instructions>

<maintain_clarity>
The revised prompt should be shorter or same length as current prompt.
Remove redundant instructions and consolidate related guidance.
</maintain_clarity>
</revision_approach>

<example_revision>
CURRENT: "Ensure the poem is signed with 'Radi:)' at the end."
USER FEEDBACK: "sign with Idan not Radi"
REVISED: "Ensure the poem is signed with 'Idan:)' at the end."
(Note: Replace the entire instruction, don't add "or Idan" to it)
</example_revision>
</feedback_analysis>"""
    
    enhanced_feedback = {
        "user_request": feedback or "Good response",
        "task_context": f"The user task is always: '{task}'",
        "instruction": enhanced_feedback_xml
    }
    
    # Create trajectory for optimization
    trajectories = [(conversation, enhanced_feedback)]
    
    # Build an explicit optimization prompt using best practices
    optimization_prompt = f"""<prompt_optimization_task>
<current_system_prompt>
{current_prompt}
</current_system_prompt>

<optimization_requirements>
<task>The user repeatedly performs this exact task: "{task}"</task>
<feedback>{feedback or 'Good response'}</feedback>

<revision_guidelines>
1. REVISE the prompt by replacing conflicting instructions, not accumulating new ones
2. If user feedback contradicts existing instructions, REPLACE the old with the new
3. Keep the prompt concise - it should not grow longer with each iteration
4. Consolidate related instructions into single, clear directives
5. Remove any outdated or superseded instructions
6. The revised prompt should directly address the user's feedback
</revision_guidelines>

<success_criteria>
- The new prompt directly incorporates the user's feedback
- Conflicting instructions are replaced, not accumulated
- The prompt remains focused and doesn't become verbose
- Similar instructions are consolidated into one clear directive
</success_criteria>
</optimization_requirements>
</prompt_optimization_task>"""
    
    # Optimize the prompt
    optimized_prompt = await optimizer.ainvoke({
        "trajectories": trajectories,
        "prompt": optimization_prompt
    })
    
    # Skip evaluation - let the main loop run the agent
    grade = 0
    reasoning = "Pending evaluation in next iteration"
    
    # Store the optimized prompt
    store.put(
        ("agent_prompt",),
        key="current",
        value={
            "prompt": optimized_prompt,
            "iteration": iteration + 1,
            "previous_prompt": current_prompt,
            "feedback": feedback or "Good response",
            "grade": grade,
            "reasoning": reasoning,
            "evaluation_details": {}
        }
    )
    
    return {
        "optimized_prompt": optimized_prompt
    }

@vero_agent("Evaluating Improvement")
async def evaluate_improvement_node(state: OptimizerState) -> Dict[str, Any]:
    """Evaluate the current agent response"""
    # Get the current agent result and prompt being used
    agent_result = state.get("agent_result", {})
    current_prompt = agent_result.get("current_prompt", INITIAL_PROMPT)
    feedback = state.get("feedback", "")  # Previous feedback if any
    task = state.get("task", "")
    eval_parameters = state.get("eval_parameters", EVAL_PARAMETERS)
    
    print("\n📊 Evaluating current agent response...")
    
    evaluation = await evaluate_prompt_improvement(agent_result, current_prompt, feedback, task, eval_parameters)
    
    # Update the stored prompt with evaluation
    try:
        current_item = store.get(("agent_prompt",), key="current")
        if current_item:
            current_item.value.update({
                "grade": evaluation.score,
                "reasoning": evaluation.reasoning,
                "evaluation_details": evaluation.model_dump()
            })
            store.put(("agent_prompt",), key="current", value=current_item.value)
    except:
        pass
    
    print(f"\n📊 Evaluation Results:")
    print(f"Score: {evaluation.score:.0f}/100")
    print(f"Reasoning: {evaluation.reasoning}")
    print(f"\n📋 Detailed Evaluation:")
    print(f"• Adherence to Prompt: {evaluation.adherence_to_prompt}")
    print(f"• Prompt Addresses Feedback: {evaluation.adherence_to_feedback}")
    print(f"• Parameter Compliance: {evaluation.adherence_to_parameters}")
    
    return {}

async def evaluate_prompt_improvement(agent_result: Dict[str, Any], new_prompt: str, feedback: str, task: str = None, eval_parameters: str = None) -> EvaluationResult:
    """Evaluate how well the agent output follows the system prompt and user feedback"""
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()
    
    # Bind the model with structured output
    structured_model = model.with_structured_output(EvaluationResult)
    
    # Get the agent's response from the conversation
    messages = agent_result["result"]["messages"]
    agent_response = ""
    for msg in messages:
        if hasattr(msg, 'type') and msg.type == "ai":
            agent_response = msg.content
            break
    
    # Get the task from the conversation if not provided
    if task is None:
        for msg in messages:
            if hasattr(msg, 'type') and msg.type == "human":
                task = msg.content
                break
    
    eval_message = f"""Grade the assistant's response based on:
1. How well it followed the OPTIMIZED system prompt (not the feedback)
2. Whether the optimized prompt correctly addressed the user's feedback intent
3. Adherence to these parameters:
{eval_parameters or EVAL_PARAMETERS}

IMPORTANT: The agent should follow the OPTIMIZED PROMPT, not the raw feedback.
The feedback was used to CREATE the optimized prompt, but the agent only sees the prompt.

Optimized System Prompt: {new_prompt}
User Task: {task}
Previous User Feedback (for context only): {feedback or 'No specific feedback'}
Agent's Response: {agent_response}

Evaluate if the agent correctly followed the optimized prompt. Do NOT penalize if the response doesn't directly match the feedback - only check if it matches the prompt."""
    
    try:
        # Get structured output directly
        result = await structured_model.ainvoke([HumanMessage(content=eval_message)])
        return result
    except Exception as e:
        print(f"Error in structured evaluation: {e}")
        # Fallback evaluation
        return EvaluationResult(
            score=50,
            reasoning=f"Error in evaluation: {str(e)}",
            adherence_to_prompt="Error occurred during evaluation",
            adherence_to_feedback="Error occurred during evaluation",
            adherence_to_parameters="Error occurred during evaluation"
        )

# Node functions for LangGraph
@vero_agent("Initialize Workflow")
async def initialize_workflow(state: OptimizerState) -> Dict[str, Any]:
    """Initialize the optimization workflow state"""
    print(f"🚀 Starting self-improving agent")
    print(f"📋 Task: {state['task']}")
    
    current_iteration = state.get("current_iteration", 0)
    
    return {
        "current_iteration": current_iteration,
        "eval_parameters": state.get("eval_parameters", EVAL_PARAMETERS)
    }




# Create the LangGraph workflow
def create_optimizer_graph(checkpointer: 'BaseCheckpointSaver' = None) -> StateGraph:
    """Create the state graph for prompt optimization workflow"""
    builder = StateGraph(OptimizerState)
    
    # Add nodes
    builder.add_node("initialize", initialize_workflow)
    builder.add_node("run_agent", run_agent_with_task)
    builder.add_node("evaluate", evaluate_improvement_node)
    builder.add_node("collect_feedback", get_human_feedback)
    builder.add_node("optimize_prompt", optimize_and_store)
    
    # Define edges - simple linear flow
    builder.add_edge(START, "initialize")
    builder.add_edge("initialize", "run_agent")
    builder.add_edge("run_agent", "evaluate")
    builder.add_edge("evaluate", "collect_feedback")
    builder.add_edge("collect_feedback", "optimize_prompt")
    builder.add_edge("optimize_prompt", "run_agent")
    
    # Compile without interrupts - feedback will be handled in the node
    return builder.compile(
        checkpointer=checkpointer
    )

# Helper to get graph with MongoDB persistence
async def get_optimizer_graph():
    """Get or create the optimizer graph with MongoDB checkpointer"""
    checkpointer = VeroMongoDBSaver()
    return create_optimizer_graph(checkpointer=checkpointer)


async def clean_session():
    """Clean the optimizer session from both in-memory store and MongoDB"""
    # Delete current prompt from in-memory store
    try:
        store.delete((("agent_prompt",), "current"))
    except:
        pass
    
    # Reset prompt to initial
    store.put(("agent_prompt",), key="current", value={"prompt": INITIAL_PROMPT, "iteration": 0})
    
    # Clean MongoDB checkpoints for optimizer-session
    try:
        from motor.motor_asyncio import AsyncIOMotorClient
        import os
        
        mongo_uri = os.getenv("MONGO_URI")
        if mongo_uri:
            client = AsyncIOMotorClient(mongo_uri)
            db = client["vero"]
            collection = db["checkpoints_aio"]
            
            # Delete all checkpoints for optimizer-session thread
            result = await collection.delete_many({
                "thread_id": "optimizer-session"
            })
            print(f"🗑️  Deleted {result.deleted_count} MongoDB checkpoints")
    except Exception as e:
        print(f"⚠️  Could not clean MongoDB checkpoints: {e}")
    
    print("✨ Session cleaned successfully!")

# Simple example usage
async def main():
    """Self-improving agent with unlimited iterations"""
    import sys
    
    # Check for --clean argument
    if "--clean" in sys.argv:
        await clean_session()
        sys.argv.remove("--clean")
        if not sys.argv[1:]:  # If no task provided after --clean, exit
            return
    
    # Get task from command line or use default
    task = " ".join(sys.argv[1:]) or "Write a short poem about coding"
    print(f"🎯 Task: {task}")
    
    # Initialize graph and config
    checkpointer = VeroMongoDBSaver()
    config = {"configurable": {"thread_id": "optimizer-session"}}
    graph = await get_optimizer_graph()
    
    # Check for existing state from checkpointer
    existing_state = None
    try:
        checkpoint = await checkpointer.aget(config)
        if checkpoint:
            # Get the channel values which contains the state
            existing_state = checkpoint['channel_values'] if isinstance(checkpoint, dict) else None
            if existing_state and isinstance(existing_state, dict):
                print(f"📚 Found existing session with {len(existing_state.get('messages', []))} messages")
                if existing_state.get('optimized_prompt'):
                    print(f"✅ Found optimized prompt in checkpoint!")
            else:
                print("⚠️  No valid state found in checkpoint")
    except Exception as e:
        print(f"⚠️  Could not load checkpoint: {e}")
    
    # Check for existing prompt - first from checkpoint state, then from store
    existing_prompt = INITIAL_PROMPT
    existing_iteration = 0
    
    # First try to get from checkpoint state
    if existing_state and isinstance(existing_state, dict):
        existing_prompt = existing_state.get("optimized_prompt", INITIAL_PROMPT)
        existing_iteration = existing_state.get("current_iteration", 0)
        if existing_prompt != INITIAL_PROMPT:
            print(f"📚 Found existing prompt from iteration {existing_iteration} in checkpoint")
    
    # Fall back to store if not in checkpoint
    if existing_prompt == INITIAL_PROMPT:
        try:
            current_item = store.get(("agent_prompt",), key="current")
            if current_item:
                existing_prompt = current_item.value['prompt']
                existing_iteration = current_item.value.get("iteration", 0)
                print(f"📚 Found existing prompt from iteration {existing_iteration} in store")
        except:
            print("🆕 Starting with initial prompt")
    
    # Create initial state - use existing messages if available
    if existing_state and isinstance(existing_state, dict) and existing_state.get("messages"):
        # Resume from existing state
        initial_state = existing_state
        initial_state["task"] = task  # Update task in case it changed
        print("📂 Resuming from previous session")
    else:
        # Start fresh
        initial_state = {
            "messages": [],
            "task": task,
            "current_iteration": existing_iteration,
            "agent_result": {},
            "feedback": "",
            "optimized_prompt": existing_prompt,
            "eval_parameters": EVAL_PARAMETERS
        }
        print("🆕 Starting new session")
    
    # Run the graph - it will loop infinitely with feedback collection
    await graph.ainvoke(initial_state, config)

if __name__ == "__main__":
    asyncio.run(main())

__all__ = ['get_optimizer_graph', 'OptimizerState', 'EvaluationResult']