"""Interactive optimizer with unlimited iterations and user feedback"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from ai.graph.optimizer_prompt import get_optimizer_graph, VeroMongoDBSaver, create_optimizer_graph

# Helper for async input
async def ainput(prompt: str = "") -> str:
    """Async version of input()"""
    return await asyncio.get_event_loop().run_in_executor(None, input, prompt)

async def interactive_optimizer(task: str = None):
    """Run interactive optimizer with unlimited iterations"""
    
    # Get or create the graph
    checkpointer = VeroMongoDBSaver()
    
    # Configuration with persistent thread ID
    config = {"configurable": {"thread_id": "optimizer-interactive"}}
    
    if not task:
        # Check command line args
        if len(sys.argv) > 1:
            task = " ".join(sys.argv[1:])
        else:
            task = "Write a short poem about coding"
    
    print(f"\n🎯 Task: {task}")
    print("💾 Using MongoDB for persistence")
    print("ℹ️  Type 'q' to quit at any feedback prompt\n")
    
    # Create initial state
    initial_state = {
        "messages": [],
        "task": task,
        "iterations": 999,  # Large number for unlimited iterations
        "current_iteration": 0,
        "agent_result": {},
        "feedback": "",
        "optimized_prompt": "You are a helpful assistant. Always be concise and clear.",
        "completed": False
    }
    
    # Create graph with proper workflow
    from langgraph.graph import StateGraph, START, END
    from ai.graph.optimizer_prompt import (
        OptimizerState, initialize_workflow, run_agent_with_task,
        optimize_and_store, evaluate_improvement_node, get_human_feedback
    )
    
    builder = StateGraph(OptimizerState)
    
    # Add nodes
    builder.add_node("initialize", initialize_workflow)
    builder.add_node("run_agent", run_agent_with_task)
    builder.add_node("collect_feedback", get_human_feedback)
    builder.add_node("optimize_prompt", optimize_and_store)
    builder.add_node("evaluate", evaluate_improvement_node)
    
    # Define edges
    builder.add_edge(START, "initialize")
    builder.add_edge("initialize", "run_agent")
    builder.add_edge("run_agent", END)  # End after first run
    
    # Compile with interrupt for feedback
    graph = builder.compile(
        checkpointer=checkpointer,
        interrupt_before=["collect_feedback"]
    )
    
    # Run first iteration
    print("🚀 Starting first iteration...\n")
    
    # Run initial workflow
    current_result = await graph.ainvoke(initial_state, config)
    
    iteration = 1
    
    # Create optimization workflow for iterations
    opt_builder = StateGraph(OptimizerState)
    opt_builder.add_node("collect_feedback", get_human_feedback)
    opt_builder.add_node("optimize_prompt", optimize_and_store)
    opt_builder.add_node("evaluate", evaluate_improvement_node)
    opt_builder.add_node("run_agent", run_agent_with_task)
    
    opt_builder.add_edge(START, "collect_feedback")
    opt_builder.add_edge("collect_feedback", "optimize_prompt")
    opt_builder.add_edge("optimize_prompt", "evaluate")
    opt_builder.add_edge("evaluate", "run_agent")
    opt_builder.add_edge("run_agent", END)
    
    opt_graph = opt_builder.compile(
        checkpointer=checkpointer,
        interrupt_before=["collect_feedback"]
    )
    
    # Interactive loop
    while True:
        print("\n" + "="*60)
        print(f"✅ Iteration {iteration} complete")
        
        # Get feedback
        feedback = await ainput("\n💭 Enter feedback (or 'q' to quit): ")
        feedback = feedback.strip()
        
        if feedback.lower() == 'q':
            print("\n👋 Exiting optimizer. Your progress is saved in MongoDB!")
            print(f"📌 Thread ID: {config['configurable']['thread_id']}")
            break
        
        if not feedback:
            print("⚠️  No feedback provided, using default...")
            feedback = "Good response"
        
        # Update state for next iteration
        iteration += 1
        
        # Update state with feedback
        await opt_graph.aupdate_state(config, {"feedback": feedback})
        
        print(f"\n🔄 Running iteration {iteration} with feedback: {feedback}\n")
        
        # Run optimization workflow
        current_result = await opt_graph.ainvoke(None, config)
    
    # Show final stats
    final_state = await graph.aget_state(config)
    if final_state and final_state.values:
        print(f"\n📊 Final Stats:")
        print(f"   Total iterations: {iteration}")
        print(f"   Final prompt: {final_state.values.get('optimized_prompt', 'N/A')}")

if __name__ == "__main__":
    asyncio.run(interactive_optimizer())