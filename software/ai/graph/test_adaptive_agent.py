import asyncio
from software.ai.graph.parallel_optimizer import create_adaptive_agent
from software.ai.llm.llm_connect import get_llm_connect

async def test_adaptive_agent():
    """Test the create_adaptive_agent wrapper"""
    
    # Get model
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()
    
    # Create adaptive agent
    agent = await create_adaptive_agent(
        model=model,
        task="how many words in this sentence, given we are living in 2025?",
        eval_parameters="""• Correctness: Must get exact answer. The answer is 11.
• Generalizable: Works for similar tasks
• No answer leakage: Prompt cannot contain the answer or test sentence""",
        score_cutoff=80
    )
    
    # Test the agent with recursion limit and thread_id config
    response = await agent.ainvoke(
        {
            "messages": [{"role": "user", "content": "how many words in this sentence, given we are living in 2025?"}]
        },
        {
            "configurable": {"thread_id": "test-word-count-1"},
            "recursion_limit": 3
        }
    )
    
    print("\n🤖 Final agent response:")
    for msg in response["messages"]:
        if hasattr(msg, 'type') and msg.type == "ai":
            print(msg.content)

if __name__ == "__main__":
    asyncio.run(test_adaptive_agent())