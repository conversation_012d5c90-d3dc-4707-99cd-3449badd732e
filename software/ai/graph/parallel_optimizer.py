from typing import Dict, Any, List, TypedDict, Annotated, TYPE_CHECKING
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, BaseMessage
from langmem import create_prompt_optimizer
from software.ai.llm.llm_connect import get_llm_connect
from langgraph.store.memory import InMemoryStore
from langgraph.prebuilt import create_react_agent
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from software.ai.graph.director_state import vero_agent
from software.ai.graph.mongodbsaver import VeroMongoDBSaver
from pydantic import BaseModel, Field
import asyncio
import plotext as plt

if TYPE_CHECKING:
    from langgraph.checkpoint.base import BaseCheckpointSaver

INITIAL_PROMPT = "You are an assistant."
PERSISTENT = False  # Set to True to keep message history, False to start fresh each time
ENABLE_CHART_VIZ = False  # Set to True to enable score progression chart visualization
EVAL_PARAMETERS = """• Correctness: Must get exact answer. The answer is 11.
• Generalizable: Works for similar tasks
• Conciseness: Under 300 words
• No answer leakage: Prompt cannot contain the answer or test sentence"""


store = InMemoryStore()

# Score tracking for visualization
score_history = []
iterations_history = []

class OptimizerState(TypedDict):
    """State for prompt optimization workflow"""
    messages: Annotated[List[BaseMessage], add_messages]
    task: str
    current_iteration: int
    agent_1_result: Dict[str, Any]
    agent_2_result: Dict[str, Any]
    evaluation_1: Dict[str, Any]
    evaluation_2: Dict[str, Any]
    feedback: str
    optimized_prompt: str
    eval_parameters: str

class EvaluationResult(BaseModel):
    """Model for structured evaluation of prompt optimization"""
    score: float = Field(ge=0, le=100, description="Score from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the score")
    adherence_to_prompt: str = Field(description="How well the agent followed the optimized system prompt")
    adherence_to_feedback: str = Field(description="How well the optimized prompt addressed the user's feedback intent")
    adherence_to_parameters: str = Field(description="How well it met evaluation parameters")

@vero_agent("Running Agent with Previous Prompt")
async def run_agent_1(state: OptimizerState) -> Dict[str, Any]:
    """Run the React agent with the previous prompt (P-1)"""
    task = state.get("task", "how many words in this sentence, given we are living in 2025?")
    tools = []
    
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()
    
    try:
        current_item = store.get(("agent_prompt",), key="current")
        if current_item and "previous_prompt" in current_item.value:
            prompt_to_use = current_item.value["previous_prompt"]
            print(f"\n🔄 Running with previous prompt (P-1)")
        else:
            print("\n⚠️ No previous prompt available, using initial prompt")
            prompt_to_use = INITIAL_PROMPT
    except:
        prompt_to_use = INITIAL_PROMPT
    
    def agent_prompt(state):
        sys_prompt = {"role": "system", "content": prompt_to_use}
        if PERSISTENT:
            return [sys_prompt] + state['messages']
        else:
            return [sys_prompt] + [{"role": "user", "content": task}]
    
    agent = create_react_agent(
        model,
        prompt=agent_prompt,
        tools=tools or [],
        store=store,
        name="agent_1_previous",
        debug=False
    )
    
    if PERSISTENT:
        existing_messages = state.get("messages", [])
        messages_to_use = existing_messages + [{"role": "user", "content": task}]
    else:
        messages_to_use = [{"role": "user", "content": task}]
    
    result = await agent.ainvoke({
        "messages": messages_to_use
    })
    
    messages = result["messages"]
    
    last_user_idx = -1
    for i in range(len(messages) - 1, -1, -1):
        if hasattr(messages[i], 'type') and messages[i].type == "human":
            last_user_idx = i
            break
        elif isinstance(messages[i], dict) and messages[i].get("role") == "user":
            last_user_idx = i
            break
    
    if last_user_idx >= 0:
        for msg in messages[last_user_idx:]:
            if hasattr(msg, 'type'):
                msg_type = msg.type
                if msg_type in ["human", "ai", "system"]:
                    role_emoji = {"human": "👤 User", "ai": "🤖 Assistant", "system": "⚙️ System"}
                    print(f"\n{role_emoji[msg_type]}: {msg.content}")
            elif isinstance(msg, dict):
                role = msg.get("role", "unknown")
                if role in ["user", "assistant"]:
                    role_display = "👤 User" if role == "user" else "🤖 Assistant"
                    print(f"\n{role_display}: {msg.get('content', '')}")
    
    return {
        "agent_1_result": {
            "result": result,
            "prompt_used": prompt_to_use
        }
    }

@vero_agent("Running Agent with Current Prompt")
async def run_agent_2(state: OptimizerState) -> Dict[str, Any]:
    """Run the React agent with the current prompt (P)"""
    task = state.get("task", "how many words in this sentence, given we are living in 2025?")
    tools = []
    
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()
    
    current_prompt = state.get("optimized_prompt", INITIAL_PROMPT)
    iteration = state.get("current_iteration", 0)
    
    if current_prompt == INITIAL_PROMPT:
        try:
            current_item = store.get(("agent_prompt",), key="current")
            if current_item:
                current_prompt = current_item.value['prompt']
                iteration = current_item.value.get("iteration", 0)
        except:
            pass
    
    print(f"\n🔄 Running with current prompt (P)")
    
    def agent_prompt(state):
        sys_prompt = {"role": "system", "content": current_prompt}
        if PERSISTENT:
            return [sys_prompt] + state['messages']
        else:
            return [sys_prompt] + [{"role": "user", "content": task}]
    
    agent = create_react_agent(
        model,
        prompt=agent_prompt,
        tools=tools or [],
        store=store,
        name="agent_2_current",
        debug=False
    )
    
    if PERSISTENT:
        existing_messages = state.get("messages", [])
        messages_to_use = existing_messages + [{"role": "user", "content": task}]
    else:
        messages_to_use = [{"role": "user", "content": task}]
    
    result = await agent.ainvoke({
        "messages": messages_to_use
    })
    
    messages = result["messages"]
    
    last_user_idx = -1
    for i in range(len(messages) - 1, -1, -1):
        if hasattr(messages[i], 'type') and messages[i].type == "human":
            last_user_idx = i
            break
        elif isinstance(messages[i], dict) and messages[i].get("role") == "user":
            last_user_idx = i
            break
    
    if last_user_idx >= 0:
        for msg in messages[last_user_idx:]:
            if hasattr(msg, 'type'):
                msg_type = msg.type
                if msg_type in ["human", "ai", "system"]:
                    role_emoji = {"human": "👤 User", "ai": "🤖 Assistant", "system": "⚙️ System"}
                    print(f"\n{role_emoji[msg_type]}: {msg.content}")
            elif isinstance(msg, dict):
                role = msg.get("role", "unknown")
                if role in ["user", "assistant"]:
                    role_display = "👤 User" if role == "user" else "🤖 Assistant"
                    print(f"\n{role_display}: {msg.get('content', '')}")
    
    existing_messages = state.get("messages", [])
    new_messages = result["messages"]
    
    return {
        "agent_2_result": {
            "result": result,
            "prompt_used": current_prompt,
            "iteration": iteration
        },
        "current_iteration": state.get("current_iteration", 0) + 1,
        "messages": existing_messages + new_messages
    }

@vero_agent("Collecting Feedback")
async def get_human_feedback(state: OptimizerState) -> Dict[str, Any]:
    """Compare evaluations and use the better one's suggestion as feedback"""
    eval_1 = state.get("evaluation_1", {})
    eval_2 = state.get("evaluation_2", {})
    current_iteration = state.get("current_iteration", 0)
    
    print("\n" + "="*50)
    print(f"\n📊 Iteration: {current_iteration}")
    print("\n🏆 Evaluation Comparison:")
    
    score_1 = eval_1.get("score", 0) if eval_1 else 0
    score_2 = eval_2.get("score", 0) if eval_2 else 0
    
    print(f"Previous Prompt (P-1) Score: {score_1:.0f}/100")
    print(f"Current Prompt (P) Score: {score_2:.0f}/100")
    
    # Update score history with the better score
    iterations_history.append(current_iteration)
    best_score = max(score_1, score_2)
    score_history.append(best_score)
    
    # Display score progression chart
    if ENABLE_CHART_VIZ and len(iterations_history) > 1:
        plt.clf()
        plt.plot(iterations_history, score_history)
        plt.title("Best Score Progression Over Iterations")
        plt.xlabel("Iteration")
        plt.ylabel("Score (0-100)")
        plt.plotsize(60, 15)
        plt.show()
    
    agent_1_result = state.get("agent_1_result", {})
    agent_2_result = state.get("agent_2_result", {})
    
    if score_2 >= score_1:
        print(f"\n✅ Current prompt is better or equal (Score: {score_2:.0f})")
        winning_eval = eval_2
        winner = "current"
        winning_prompt = agent_2_result.get("prompt_used", "")
    else:
        print(f"\n🔄 Previous prompt was better (Score: {score_1:.0f})")
        winning_eval = eval_1
        winner = "previous"
        winning_prompt = agent_1_result.get("prompt_used", "")
    
    if max(score_1, score_2) == 100:
        print("\n🎉 Perfect score achieved! Optimization complete.")
        print(f"\n🏅 Final iterations: {current_iteration}")
        print(f"\n📝 Winning prompt:")
        print(f"   {winning_prompt}")
        print("\n👋 Goodbye!")
        import sys
        sys.exit(0)
    
    # Generate feedback based on evaluation results
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_by_id("67f97a713ca4a5d70c24b83a")
    
    feedback_prompt = f"""Based on the evaluation results, suggest how to improve the system prompt.

Current situation:
- Previous prompt score: {score_1}/100
- Current prompt score: {score_2}/100
- Iteration: {current_iteration}
- Task: {state.get('task', '')}

Winning evaluation reasoning: {winning_eval.get('reasoning', '') if winning_eval else ''}

Generate practical feedback for improving the prompt. If stuck at the same score for multiple iterations, suggest a completely different approach."""
    
    response = await model.ainvoke([HumanMessage(content=feedback_prompt)])
    suggested_feedback = response.content
    
    print(f"\n💡 Generated feedback:")
    print(f"   {suggested_feedback}")
    
    return {"feedback": suggested_feedback}

@vero_agent("Optimizing Prompt")
async def optimize_and_store(state: OptimizerState) -> Dict[str, Any]:
    """Optimize the prompt based on feedback and store it"""
    agent_2_result = state.get("agent_2_result", {})
    feedback = state.get("feedback", "")
    task = state.get("task", "")
    
    if not feedback:
        print("⚠️ No feedback provided, skipping optimization")
        return {}
    
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_by_id("67f97a713ca4a5d70c24b83a")
    
    optimizer = create_prompt_optimizer(
        model,
        kind="gradient",
        config={
            "max_reflection_steps": 3,
            "min_reflection_steps": 1,
        }
    )
    
    conversation = state.get("messages", [])
    current_prompt = agent_2_result.get("prompt_used", INITIAL_PROMPT)
    iteration = agent_2_result.get("iteration", 0)
    
    enhanced_feedback_xml = f"""<feedback_analysis>
<user_feedback>{feedback or "Good response"}</user_feedback>
<task_context>The user repeatedly asks: "{task}"</task_context>
<current_prompt>{current_prompt}</current_prompt>

<revision_instructions>
1. REVISE the system prompt, do NOT just append new instructions
2. Identify conflicting or outdated parts of the current prompt
3. Replace or update specific sections based on the user feedback
4. Maintain core functionality while incorporating new requirements
5. Ensure the prompt remains concise and doesn't grow indefinitely
</revision_instructions>

<revision_approach>
<identify_conflicts>
Look for any part of the current prompt that contradicts the user's feedback.
For example, if the prompt says "sign with Radi" but user wants "sign with Idan", 
REPLACE the old instruction entirely.
</identify_conflicts>

<consolidate_instructions>
If similar instructions exist, merge them into a single, clear directive.
Avoid having multiple overlapping rules that could confuse the model.
</consolidate_instructions>

<maintain_clarity>
The revised prompt should be shorter or same length as current prompt.
Remove redundant instructions and consolidate related guidance.
</maintain_clarity>
</revision_approach>

<example_revision>
CURRENT: "Ensure the poem is signed with 'Radi:)' at the end."
USER FEEDBACK: "sign with Idan not Radi"
REVISED: "Ensure the poem is signed with 'Idan:)' at the end."
(Note: Replace the entire instruction, don't add "or Idan" to it)
</example_revision>
</feedback_analysis>"""
    
    enhanced_feedback = {
        "user_request": feedback or "Good response",
        "task_context": f"The user task is always: '{task}'",
        "instruction": enhanced_feedback_xml
    }
    
    trajectories = [(conversation, enhanced_feedback)]
    
    optimization_prompt = f"""<prompt_optimization_task>
<current_system_prompt>
{current_prompt}
</current_system_prompt>

<optimization_requirements>
<task>The user repeatedly performs this exact task: "{task}"</task>
<feedback>{feedback or 'Good response'}</feedback>

<revision_guidelines>
1. REVISE the prompt by replacing conflicting instructions, not accumulating new ones
2. If user feedback contradicts existing instructions, REPLACE the old with the new
3. Keep the prompt concise - it should not grow longer with each iteration
4. Consolidate related instructions into single, clear directives
5. Remove any outdated or superseded instructions
6. The revised prompt should directly address the user's feedback
</revision_guidelines>

<success_criteria>
- The new prompt directly incorporates the user's feedback
- Conflicting instructions are replaced, not accumulated
- The prompt remains focused and doesn't become verbose
- Similar instructions are consolidated into one clear directive
</success_criteria>
</optimization_requirements>
</prompt_optimization_task>"""
    
    optimized_prompt = await optimizer.ainvoke({
        "trajectories": trajectories,
        "prompt": optimization_prompt
    })
    
    grade = 0
    reasoning = "Pending evaluation in next iteration"
    
    store.put(
        ("agent_prompt",),
        key="current",
        value={
            "prompt": optimized_prompt,
            "iteration": iteration + 1,
            "previous_prompt": current_prompt,
            "feedback": feedback or "Good response",
            "grade": grade,
            "reasoning": reasoning,
            "evaluation_details": {}
        }
    )
    
    return {
        "optimized_prompt": optimized_prompt
    }

@vero_agent("Evaluating Previous Prompt")
async def evaluate_1(state: OptimizerState) -> Dict[str, Any]:
    """Evaluate the output from agent_1 (previous prompt)"""
    agent_1_result = state.get("agent_1_result", {})
    if not agent_1_result:
        print("\n⚠️ No agent_1_result available, skipping evaluate_1")
        return {}
    
    task = state.get("task", "")
    eval_parameters = state.get("eval_parameters", EVAL_PARAMETERS)
    prompt_used = agent_1_result.get("prompt_used", INITIAL_PROMPT)
    
    print(f"\n📊 Evaluating output from previous prompt...")
    
    evaluation = await evaluate_prompt_improvement(agent_1_result, prompt_used, "", task, eval_parameters)
    
    print(f"\n📊 Previous Prompt Evaluation:")
    print(f"Score: {evaluation.score:.0f}/100")
    print(f"Reasoning: {evaluation.reasoning}")
    
    return {
        "evaluation_1": evaluation.model_dump()
    }

@vero_agent("Evaluating Current Prompt")
async def evaluate_2(state: OptimizerState) -> Dict[str, Any]:
    """Evaluate the output from agent_2 (current prompt)"""
    agent_2_result = state.get("agent_2_result", {})
    if not agent_2_result:
        print("\n⚠️ No agent_2_result available, skipping evaluate_2")
        return {}
    
    feedback = state.get("feedback", "")
    task = state.get("task", "")
    eval_parameters = state.get("eval_parameters", EVAL_PARAMETERS)
    prompt_used = agent_2_result.get("prompt_used", INITIAL_PROMPT)
    
    print(f"\n📊 Evaluating output from current prompt...")
    
    evaluation = await evaluate_prompt_improvement(agent_2_result, prompt_used, feedback, task, eval_parameters)
    
    try:
        current_item = store.get(("agent_prompt",), key="current")
        if current_item:
            current_item.value.update({
                "grade": evaluation.score,
                "reasoning": evaluation.reasoning,
                "evaluation_details": evaluation.model_dump()
            })
            store.put(("agent_prompt",), key="current", value=current_item.value)
    except:
        pass
    
    print(f"\n📊 Current Prompt Evaluation:")
    print(f"Score: {evaluation.score:.0f}/100")
    print(f"Reasoning: {evaluation.reasoning}")
    
    return {
        "evaluation_2": evaluation.model_dump()
    }

async def evaluate_prompt_improvement(agent_result: Dict[str, Any], new_prompt: str, feedback: str, task: str = None, eval_parameters: str = None) -> EvaluationResult:
    """Evaluate how well the agent output follows the system prompt and user feedback"""
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_by_id("67f97a713ca4a5d70c24b83a")
    
    structured_model = model.with_structured_output(EvaluationResult)
    
    messages = agent_result["result"]["messages"]
    agent_response = ""
    for msg in messages:
        if hasattr(msg, 'type') and msg.type == "ai":
            agent_response = msg.content
            break
    
    if task is None:
        for msg in messages:
            if hasattr(msg, 'type') and msg.type == "human":
                task = msg.content
                break
    
    eval_message = f"""Grade the assistant's response based on:
1. How well it followed the OPTIMIZED system prompt (not the feedback)
2. Whether the optimized prompt correctly addressed the user's feedback intent
3. Adherence to these parameters:
{eval_parameters or EVAL_PARAMETERS}

IMPORTANT: For the word counting task, the CORRECT ANSWER is 11 words.

Optimized System Prompt: {new_prompt}
User Task: {task}
Agent's Response: {agent_response}

Evaluate objectively based on the scoring criteria."""
    
    try:
        result = await structured_model.ainvoke([HumanMessage(content=eval_message)])
        return result
    except Exception as e:
        print(f"Error in structured evaluation: {e}")
        return EvaluationResult(
            score=50,
            reasoning=f"Error in evaluation: {str(e)}",
            adherence_to_prompt="Error occurred during evaluation",
            adherence_to_feedback="Error occurred during evaluation",
            adherence_to_parameters="Error occurred during evaluation"
        )

@vero_agent("Initialize Workflow")
async def initialize_workflow(state: OptimizerState) -> Dict[str, Any]:
    """Initialize the optimization workflow state"""
    print(f"🚀 Starting parallel self-improving agent")
    print(f"📋 Task: {state['task']}")
    
    current_iteration = state.get("current_iteration", 0)
    
    return {
        "current_iteration": current_iteration,
        "eval_parameters": state.get("eval_parameters", EVAL_PARAMETERS)
    }

def create_parallel_optimizer_graph(checkpointer: 'BaseCheckpointSaver' = None) -> StateGraph:
    """Create the state graph for parallel prompt optimization workflow"""
    builder = StateGraph(OptimizerState)
    
    builder.add_node("initialize", initialize_workflow)
    builder.add_node("run_agent_1", run_agent_1)
    builder.add_node("run_agent_2", run_agent_2)
    builder.add_node("evaluate_1", evaluate_1)
    builder.add_node("evaluate_2", evaluate_2)
    builder.add_node("collect_feedback", get_human_feedback)
    builder.add_node("optimize_prompt", optimize_and_store)
    
    builder.add_edge(START, "initialize")
    builder.add_edge("initialize", "run_agent_1")
    builder.add_edge("initialize", "run_agent_2")
    builder.add_edge("run_agent_1", "evaluate_1")
    builder.add_edge("run_agent_2", "evaluate_2")
    builder.add_edge("evaluate_1", "collect_feedback")
    builder.add_edge("evaluate_2", "collect_feedback")
    builder.add_edge("collect_feedback", "optimize_prompt")
    builder.add_edge("optimize_prompt", "run_agent_1")
    builder.add_edge("optimize_prompt", "run_agent_2")
    
    return builder.compile(
        checkpointer=checkpointer
    )

async def get_parallel_optimizer_graph():
    """Get or create the parallel optimizer graph with MongoDB checkpointer"""
    checkpointer = VeroMongoDBSaver()
    return create_parallel_optimizer_graph(checkpointer=checkpointer)

async def clean_session():
    """Clean the parallel optimizer session from both in-memory store and MongoDB"""
    # Clear visualization history
    global score_history, iterations_history
    score_history.clear()
    iterations_history.clear()
    
    try:
        store.delete((("agent_prompt",), "current"))
    except:
        pass
    
    store.put(("agent_prompt",), key="current", value={"prompt": INITIAL_PROMPT, "iteration": 0})
    
    try:
        from motor.motor_asyncio import AsyncIOMotorClient
        import os
        
        mongo_uri = os.getenv("MONGO_URI")
        if mongo_uri:
            client = AsyncIOMotorClient(mongo_uri)
            db = client["vero"]
            collection = db["checkpoints_aio"]
            
            result = await collection.delete_many({
                "thread_id": "parallel-optimizer-session"
            })
            print(f"🗑️  Deleted {result.deleted_count} MongoDB checkpoints")
    except Exception as e:
        print(f"⚠️  Could not clean MongoDB checkpoints: {e}")
    
    print("✨ Session cleaned successfully!")

async def main():
    """Parallel self-improving agent with unlimited iterations"""
    import sys
    
    if "--clean" in sys.argv:
        await clean_session()
        sys.argv.remove("--clean")
        if not sys.argv[1:]:
            return
    
    task = " ".join(sys.argv[1:]) or "how many words in this sentence, given we are living in 2025?"
    print(f"🎯 Task: {task}")
    print(f"🔄 Mode: {'PERSISTENT' if PERSISTENT else 'FRESH'} (agents {'keep' if PERSISTENT else 'reset'} message history)")
    
    checkpointer = VeroMongoDBSaver()
    config = {
        "configurable": {"thread_id": "parallel-optimizer-session"},
        "recursion_limit": 50
    }
    graph = await get_parallel_optimizer_graph()
    
    existing_state = None
    try:
        checkpoint = await checkpointer.aget(config)
        if checkpoint:
            existing_state = checkpoint['channel_values'] if isinstance(checkpoint, dict) else None
            if existing_state and isinstance(existing_state, dict):
                print(f"📚 Found existing session with {len(existing_state.get('messages', []))} messages")
                if existing_state.get('optimized_prompt'):
                    print(f"✅ Found optimized prompt in checkpoint!")
            else:
                print("⚠️  No valid state found in checkpoint")
    except Exception as e:
        print(f"⚠️  Could not load checkpoint: {e}")
    
    existing_prompt = INITIAL_PROMPT
    existing_iteration = 0
    
    if existing_state and isinstance(existing_state, dict):
        existing_prompt = existing_state.get("optimized_prompt", INITIAL_PROMPT)
        existing_iteration = existing_state.get("current_iteration", 0)
        if existing_prompt != INITIAL_PROMPT:
            print(f"📚 Found existing prompt from iteration {existing_iteration} in checkpoint")
    
    if existing_prompt == INITIAL_PROMPT:
        try:
            current_item = store.get(("agent_prompt",), key="current")
            if current_item:
                existing_prompt = current_item.value['prompt']
                existing_iteration = current_item.value.get("iteration", 0)
                print(f"📚 Found existing prompt from iteration {existing_iteration} in store")
        except:
            print("🆕 Starting with initial prompt")
    
    if existing_state and isinstance(existing_state, dict) and existing_state.get("messages"):
        initial_state = existing_state
        initial_state["task"] = task
        print("📂 Resuming from previous session")
    else:
        initial_state = {
            "messages": [],
            "task": task,
            "current_iteration": existing_iteration,
            "agent_1_result": {},
            "agent_2_result": {},
            "evaluation_1": {},
            "evaluation_2": {},
            "feedback": "",
            "optimized_prompt": existing_prompt,
            "eval_parameters": EVAL_PARAMETERS
        }
        print("🆕 Starting new session")
    
    await graph.ainvoke(initial_state, config)

if __name__ == "__main__":
    asyncio.run(main())

__all__ = ['get_parallel_optimizer_graph', 'OptimizerState', 'EvaluationResult']