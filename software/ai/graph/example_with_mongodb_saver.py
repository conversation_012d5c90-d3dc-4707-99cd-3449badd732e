"""Example of using MongoDB saver with LangGraph for persistent state"""

from langgraph.graph import StateGraph, MessagesState
from langchain_core.messages import HumanMessage, AIMessage
from mongodbsaver import VeroMongoDBSaver
import asyncio
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def example_persistent_graph():
    """Example showing how to use MongoDB saver for persistent graph state"""
    
    # Create MongoDB saver
    saver = VeroMongoDBSaver()
    
    # Define a simple agent that remembers previous conversations
    def chat_agent(state: MessagesState):
        messages = state["messages"]
        
        # Count previous messages to show persistence
        user_message_count = sum(1 for m in messages if isinstance(m, HumanMessage))
        
        response = f"This is message #{user_message_count} in our conversation. I remember all {len(messages)} messages!"
        
        return {"messages": [AIMessage(content=response)]}
    
    # Build graph
    graph = StateGraph(MessagesState)
    graph.add_node("agent", chat_agent)
    graph.set_entry_point("agent")
    graph.set_finish_point("agent")
    
    # Compile with MongoDB checkpointer
    app = graph.compile(checkpointer=saver)
    
    # Configuration with thread ID for persistence
    config = {
        "configurable": {
            "thread_id": "user_conversation_123"
        }
    }
    
    # First message
    logger.info("=== First interaction ===")
    result1 = await app.ainvoke(
        {"messages": [HumanMessage(content="Hello!")]},
        config=config
    )
    logger.info(f"Response: {result1['messages'][-1].content}")
    
    # Second message - should remember the first
    logger.info("\n=== Second interaction ===")
    result2 = await app.ainvoke(
        {"messages": [HumanMessage(content="How are you?")]},
        config=config
    )
    logger.info(f"Response: {result2['messages'][-1].content}")
    
    # Third message - should remember both previous
    logger.info("\n=== Third interaction ===")
    result3 = await app.ainvoke(
        {"messages": [HumanMessage(content="What's the weather?")]},
        config=config
    )
    logger.info(f"Response: {result3['messages'][-1].content}")
    
    # Show state history
    logger.info("\n=== State History ===")
    states = []
    async for state in app.aget_state_history(config):
        states.append(state)
    logger.info(f"Total states in history: {len(states)}")
    
    # Demonstrate persistence by creating a new app instance
    logger.info("\n=== Creating new app instance ===")
    new_app = graph.compile(checkpointer=saver)
    
    # Get current state from the new instance
    current_state = await new_app.aget_state(config)
    if current_state and current_state.values:
        logger.info(f"Messages recovered from database: {len(current_state.values.get('messages', []))}")
        logger.info("✓ State successfully persisted and recovered!")
    
    return app


async def example_multiple_threads():
    """Example showing multiple conversation threads"""
    
    saver = VeroMongoDBSaver()
    
    def simple_agent(state: MessagesState):
        thread_id = state.get("configurable", {}).get("thread_id", "unknown")
        return {"messages": [AIMessage(content=f"Response from thread: {thread_id}")]}
    
    # Build graph
    graph = StateGraph(MessagesState)
    graph.add_node("agent", simple_agent)
    graph.set_entry_point("agent")
    graph.set_finish_point("agent")
    
    app = graph.compile(checkpointer=saver)
    
    # Create multiple threads
    for i in range(3):
        config = {"configurable": {"thread_id": f"thread_{i}"}}
        
        result = await app.ainvoke(
            {"messages": [HumanMessage(content=f"Message for thread {i}")]},
            config=config
        )
        
        logger.info(f"Thread {i}: {result['messages'][-1].content}")


if __name__ == "__main__":
    logger.info("Running MongoDB Saver Examples...")
    
    # Run persistent conversation example
    asyncio.run(example_persistent_graph())
    
    # Run multiple threads example
    logger.info("\n" + "="*50 + "\n")
    asyncio.run(example_multiple_threads())