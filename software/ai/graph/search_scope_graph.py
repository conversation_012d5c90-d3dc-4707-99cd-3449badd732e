from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage,
    ToolMessage
)
from software.ai.graph.director_state import DirectorState, print_step, print_debug, print_pretty, vero_agent, print_markdown
from software.ai.llm.llm_connect import get_llm_connect
from langgraph.prebuilt import create_react_agent
from langchain_community.tools import WikipediaQueryRun, DuckDuckGoSearchResults
from langchain_community.utilities import WikipediaAPIWrapper
import random
import asyncio


from software.db.research_repository import ResearchRepository
from software.db.research_director_repository import ResearchDirectorRepository
from datetime import datetime, timedelta
import re

research_repo = ResearchRepository()
research_director_repo = ResearchDirectorRepository()

def calculate_business_day_horizon(perspective: int) -> tuple[int, str]:
    """
    Calculate business day horizon ensuring target falls on a trading day.

    Args:
        perspective: Number of days to look ahead

    Returns:
        tuple: (days_ahead, target_day_name)
    """
    from datetime import datetime, timedelta
    import holidays
    
    # Get US market holidays
    us_holidays = holidays.US()
    
    # Start from today
    current_date = datetime.now()
    target_date = current_date + timedelta(days=perspective)
    
    # Adjust if target falls on weekend or holiday
    while target_date.weekday() >= 5 or target_date.date() in us_holidays:
        target_date += timedelta(days=1)
    
    # Calculate actual days ahead
    days_ahead = (target_date - current_date).days
    target_day_name = target_date.strftime("%A")
    
    return days_ahead, target_day_name

def extract_business_question(response_text: str) -> str:
    """
    Extract clean business question from potentially verbose LLM response.
    
    Args:
        response_text: Raw response from LLM that may contain extra context
        
    Returns:
        Clean business question in format: "Should we invest in Company (TICKER) in the next X days?"
    """
    if not response_text:
        return "Invalid business question generated"
    
    # Pattern to match the exact format we want
    pattern = r'Should we invest in ([^(]+)\(([A-Z]{1,5})\) in the next (\d+) days\?'
    
    # Search for the pattern in the response
    match = re.search(pattern, response_text)
    
    if match:
        company_name = match.group(1).strip()
        ticker = match.group(2).strip()
        days = match.group(3).strip()
        return f"Should we invest in {company_name} ({ticker}) in the next {days} days?"
    
    # Fallback: try to find any question with "Should we invest"
    fallback_pattern = r'Should we invest in [^?]+\?'
    fallback_match = re.search(fallback_pattern, response_text)
    
    if fallback_match:
        return fallback_match.group(0)
    
    # Last resort: return first line that looks like a question
    lines = response_text.split('\n')
    for line in lines:
        line = line.strip()
        if line.endswith('?') and 'invest' in line.lower():
            return line
    
    # If all else fails, return a cleaned version of the response
    return response_text.split('\n')[0].strip()[:200] + "..."

@vero_agent("Pulling")
async def pull_director_profile(state: DirectorState) -> DirectorState:
    """Pull director profile from database"""

    director_id = state["director_id"]
    
    full_profile = await research_director_repo.get_director(director_id)

    if full_profile:
        print(f"Found director: {full_profile.get('name', 'Unknown')}", "Director Info")
        print(f"Director profile loaded: {full_profile.get('name')} - {full_profile.get('title')}", "Profile Debug")

        # Extract only the needed fields
        director_profile = {
            "name": full_profile.get("name", ""),
            "title": full_profile.get("title", ""),
            "experience_years": full_profile.get("experience_years", 0),
            "expertise": full_profile.get("expertise", []),
            "analysis_style": full_profile.get("analysis_style", ""),
            "background": full_profile.get("background", ""),
            "personality": full_profile.get("personality", "")
        }

        state["director_profile"] = director_profile

    return state

@vero_agent("Thinking")
async def critical_thinking(state: DirectorState) -> DirectorState:
    """
    Analyzes the business question and provides strategic insights before search.
    Helps focus the search on relevant sectors and companies based on director expertise.
    """

    director_id = state["director_id"]
    director_profile = state.get("director_profile", {})

    # Get LLM for critical thinking
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "critical_thinking")

    # Calculate business day horizon with 5-day perspective
    days_ahead, target_day = calculate_business_day_horizon(27)
    print(f"Horizon: {days_ahead} days to {target_day}", "Investment Horizon")

    # Create system message for critical thinking
    system_message = f"""<role>
You are {director_profile.get('name', '')}, {director_profile.get('title', '')} with {director_profile.get('experience_years', 0)} years of experience.

Your investment approach:
- Expertise in {', '.join(director_profile.get('expertise', []))}
- Analysis style: {director_profile.get('analysis_style', '')}
- Background: {director_profile.get('background', '')}
- Approach: {director_profile.get('personality', '')}
</role>

<task>
Analyze current market conditions through the lens of your unique investment philosophy.
Identify opportunities for a {days_ahead}-day investment ending {target_day} that align with your expertise and approach.
</task>

<context>
This analysis will guide an autonomous search agent to find specific companies.
The agent will use your strategic insights to identify candidates matching your criteria.
</context>

<reasoning_framework>
Structure your analysis using your investment philosophy:
1. Market Assessment: What conditions or inefficiencies do you see through your lens?
2. Opportunity Identification: Where does your approach suggest looking for value?
3. Selection Criteria: What specific, measurable characteristics should companies have?
4. Search Strategy: What queries and filters would find these opportunities?
</reasoning_framework>

<output_requirements>
Provide strategic guidance including:
1. Market perspective from your unique viewpoint
2. 2-3 opportunity areas aligned with your philosophy
3. Specific, measurable criteria for company selection
4. Concrete search terms and filters
5. Key thresholds or metrics for evaluation

Focus on HOW to find opportunities, not specific company names.
</output_requirements>

<guidelines>
- Let your expertise guide the analysis - don't force generic recommendations
- Provide searchable, quantifiable criteria
- Consider your typical investment timeframe and risk tolerance
- Include both what to look for AND what to avoid
- Balance specificity with flexibility for the search agent
</guidelines>

<success_criteria>
A successful analysis will:
- Identify sectors that align with your expertise
- Provide specific, actionable search terms
- Include measurable metrics for evaluation
- Consider the {days_ahead}-day investment horizon
- Balance risk and opportunity appropriate to your investment style
</success_criteria>
"""

    # Create human message with the task
    human_message = HumanMessage(content=f"""
Apply your investment philosophy to identify opportunities for a {days_ahead}-day investment ending {target_day}.

Provide strategic guidance that reflects:
1. Your unique market perspective and analytical approach
2. Specific, measurable criteria for finding opportunities
3. Concrete search strategies an agent can execute
4. Clear thresholds and filters for evaluation

Remember: Guide the search process, don't name specific companies.
""")

    # Get critical analysis from LLM
    response = await model.ainvoke([
        SystemMessage(content=system_message),
        human_message
    ])

    # Store the critical analysis in state
    state["critical_thinking_search"] = response.content
    print_markdown(response.content, "Strategic Investment Analysis")

    return state

@vero_agent("Searching")
async def search_scope(state: DirectorState) -> DirectorState:
    """Search for a company to analyze, using the critical thinking analysis to guide the search"""

    wiki_wrapper = WikipediaAPIWrapper(
        doc_content_chars_max=4000,
        top_k_results=2,
        lang="en",
        load_all_available_meta=True
    )

    tools = [
        WikipediaQueryRun(
            api_wrapper=wiki_wrapper,
            return_direct=False
        ),
        DuckDuckGoSearchResults(
            backend="text",
            max_results=5
        ),
        DuckDuckGoSearchResults(
            backend="news",
            max_results=5
        )
    ]

    director_id = state["director_id"]

    director_profile = state.get("director_profile", {})
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "search_scope")
    # Get critical analysis from state
    critical_thinking_search = state.get("critical_thinking_search", "")

    system_message = f"""<role>
You are {director_profile.get('name', '')}, {director_profile.get('title', '')} with {director_profile.get('experience_years', 0)} years of experience.

Your investment approach:
- Expertise in {', '.join(director_profile.get('expertise', []))}
- Analysis style: {director_profile.get('analysis_style', '')}
- Background: {director_profile.get('background', '')}
- Approach: {director_profile.get('personality', '')}
</role>

<context>
Investment horizon: should be taken from the strategic analysis.
Select exactly ONE publicly traded company that aligns with your expertise and investment philosophy.
</context>

<strategic_analysis>
{critical_thinking_search}
</strategic_analysis>

<research_guidelines>
Use available tools efficiently to research companies:
- Start with news search to find recent developments in your expertise areas
- Use text search for financial metrics and analyst opinions
- Use Wikipedia for company verification and business model understanding
- Aim for 3-5 total tool uses - don't over-research
- Focus on companies that match your investment expertise and style
</research_guidelines>

<selection_criteria>
Choose a company that:
1. Operates in sectors aligned with your expertise: {', '.join(director_profile.get('expertise', []))}
2. Has recent positive catalysts or developments within the investment horizon
3. Matches your analysis style: {director_profile.get('analysis_style', '')}
4. Fits your investment approach: {director_profile.get('personality', '')}
5. Is publicly traded with a valid ticker symbol
6. Shows promise for the investment horizon
</selection_criteria>

<efficiency_focus>
Work efficiently within your expertise:
- If you find a company in your expertise area with good recent news, that's sufficient
- Don't search endlessly - your experience guides quick decisions
- Trust your investment instincts based on available information
- Make a decision once you have reasonable confidence
</efficiency_focus>

<output_requirement>
Output EXACTLY this format with NO additional text:

Should we invest in [COMPANY NAME] ([TICKER]) in the next [INVESTMENT HORIZON] days?

Replace [COMPANY NAME] with actual company name, [TICKER] with ticker symbol.
Output nothing else - no explanations, analysis, or additional text."""

    langgraph_agent_executor = create_react_agent(
        model,
        tools,
        prompt=system_message,
        version="v1",
    )

    try:
        human_message = f"""Find a promising company to analyze for a [INVESTMENT HORIZON] days investment ending [TARGET DAY].

Focus on:
1. Companies in the promising sectors identified in the strategic analysis
2. Companies with recent positive developments or upcoming catalysts
3. Companies that align with my investment approach and expertise
4. Publicly traded companies with valid ticker symbols

Use the search terms and metrics from the strategic analysis to guide your search.
"""
        response = await langgraph_agent_executor.ainvoke({"messages": [("user", human_message)]}, config={"thread_id": state["thread_id"], "recursion_limit": 25})
        # The response contains all messages from the ReAct agent's conversation
        all_messages = response.get('messages', [])
        raw_response = all_messages[-1].content if all_messages else "N/A"
        
        # Extract clean business question using regex
        clean_business_question = extract_business_question(raw_response)
        state["business_question"] = clean_business_question
        
        # Store message count in result for access later
        state["_react_message_count"] = len(all_messages)


        print("[bold blue]Business Question:[/bold blue] ", state["business_question"])

        return state
    except Exception as e:
        print_debug(f"Error in search scope: {str(e)}")

        # Mark appropriate stages as failed only if we have a valid thread_id
        if state.get("thread_id"):
            await research_repo.update_workflow_stage(state["thread_id"], "search_scope", "failed")
            # Set the overall task as failed
            await research_repo.update_task(state["thread_id"], {"status": "failed", "error": str(e)})

        # Re-raise the exception to stop the graph execution
        raise


def create_search_scope_subgraph() -> StateGraph:
    """Subgraph for search scope operations"""
    subgraph = StateGraph(DirectorState)

    # Add all nodes to the graph
    subgraph.add_node("pull_director_profile", pull_director_profile)
    subgraph.add_node("critical_thinking", critical_thinking)
    subgraph.add_node("search_scope", search_scope)

    # Define the flow of the graph
    subgraph.add_edge(START, "pull_director_profile")
    subgraph.add_edge("pull_director_profile", "critical_thinking")
    subgraph.add_edge("critical_thinking", "search_scope")
    subgraph.add_edge("search_scope", END)
    return subgraph.compile()

graph = create_search_scope_subgraph()

async def run_single_analysis(director_id: str, run_number: int):
    """Run a single analysis for a director"""
    initial_state = {
        "director_id": director_id,
        "thread_id": ""
    }
    
    try:
        result = await graph.ainvoke(initial_state)
        business_question = result.get('business_question', 'N/A')
        director_profile = result.get('director_profile', {})
        # Get message count from the last ReAct agent invocation
        message_count = getattr(result, '_react_message_count', 0)
        return (business_question, message_count, director_profile)
    except Exception as e:
        print(f"\n❌ Run #{run_number} failed: {str(e)}")
        return None

async def run_multiple_analyses():
    """Run search analysis for each director in parallel"""
    directors = [
        "67daf61442ee7bea5234c067",  # Dr. Michael Burry
        "6813fa5983464ac1b91ee968",   # Dr. Evelyn Reed
        "6813fb0083464ac1b91ee969",
        "6813fb2b83464ac1b91ee96a",
        "6813fb5083464ac1b91ee96b",
        "6813fb7383464ac1b91ee96c",
        "6813fb9183464ac1b91ee96d",
    ]
    
    print("🚀 Starting parallel search analysis for all directors...")
    print("=" * 60)
    
    tasks = [run_single_analysis(director_id, i + 1) for i, director_id in enumerate(directors)]
    results = await asyncio.gather(*tasks)
    
    print("\n" + "=" * 60)
    print("📊 ALL RESULTS")
    print("=" * 60)
    for i, result in enumerate(results):
        if result:
            business_question, message_count, director_profile = result
            director_name = director_profile.get('name', 'Unknown Director')
            director_title = director_profile.get('title', 'Unknown Title')
            print(f"  Director {i+1}: {director_name} ({director_title})")
            print(f"    Question: {business_question} ({message_count} messages)")
            print()
        else:
            print(f"  Director {i+1}: Analysis failed")
            print()

if __name__ == "__main__":
    asyncio.run(run_multiple_analyses())

__all__ = ['graph']