from langgraph.checkpoint.mongodb.aio import AsyncMongoDBSaver
from typing import Optional
import os
from motor.motor_asyncio import AsyncIOMotorClient
from datetime import datetime
import logging
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)


class VeroMongoDBSaver(AsyncMongoDBSaver):
    """Custom MongoDB saver for Vero that uses existing database connection
    
    Note: AsyncMongoDBSaver creates a collection named 'checkpoints_aio' 
    to store graph checkpoints, which allows persistence of graph state
    across executions.
    """
    
    def __init__(self, client: Optional[AsyncIOMotorClient] = None):
        """Initialize MongoDB saver with existing client or create new one
        
        Args:
            client: Optional AsyncIOMotorClient instance. If not provided,
                   creates a new connection using MONGO_URI environment variable.
        """
        if client is None:
            # Get MongoDB URI from environment
            mongo_uri = os.getenv("MONGO_URI")
            if not mongo_uri:
                raise ValueError("MONGO_URI environment variable is not set")
            client = AsyncIOMotorClient(mongo_uri)
        
        # Initialize with vero database and checkpoints collection
        # Note: This creates 'checkpoints_aio' collection in MongoDB
        super().__init__(
            client=client,
            db_name="vero",
            collection_name="checkpoints"
        )
        
        logger.info("Initialized VeroMongoDBSaver with database: vero, collection: checkpoints_aio")
    
    @classmethod
    async def create_with_existing_connection(cls):
        """Create saver using existing database connection from api.database"""
        try:
            from api.database import client
            return cls(client=client)
        except ImportError:
            logger.warning("Could not import existing database client, creating new connection")
            return cls()


async def test_mongodb_saver():
    """Test function to verify MongoDB saver works correctly"""
    logger.info("Starting MongoDB saver test...")
    
    try:
        # Create saver instance
        saver = VeroMongoDBSaver()
        logger.info("✓ Created VeroMongoDBSaver instance")
        
        # Create a simple test with LangGraph
        from langgraph.graph import StateGraph, MessagesState
        from langchain_core.messages import HumanMessage
        
        # Build a simple graph
        def agent_node(state: MessagesState):
            messages = state["messages"]
            response = f"Received {len(messages)} message(s)"
            from langchain_core.messages import AIMessage
            return {"messages": [AIMessage(content=response)]}
        
        # Create graph
        graph = StateGraph(MessagesState)
        graph.add_node("agent", agent_node)
        graph.set_entry_point("agent")
        graph.set_finish_point("agent")
        
        # Compile with our MongoDB saver
        app = graph.compile(checkpointer=saver)
        logger.info("✓ Compiled graph with MongoDB checkpointer")
        
        # Test configuration
        config = {
            "configurable": {
                "thread_id": "test_thread_123"
            }
        }
        
        # Run the graph
        from langchain_core.messages import HumanMessage
        result = await app.ainvoke(
            {"messages": [HumanMessage(content="Hello MongoDB!")]},
            config=config
        )
        logger.info("✓ Successfully ran graph with checkpointing")
        logger.info(f"  - Result: {result['messages'][-1].content}")
        
        # Get state history
        states = []
        async for state in app.aget_state_history(config):
            states.append(state)
        
        logger.info(f"✓ Found {len(states)} state(s) in history")
        
        # Check MongoDB directly
        mongo_uri = os.getenv("MONGO_URI")
        client = AsyncIOMotorClient(mongo_uri)
        db = client.vero
        
        # Check which checkpoint collection exists
        collections = await db.list_collection_names()
        checkpoint_collection_name = None
        
        if "checkpoints_aio" in collections:
            checkpoint_collection_name = "checkpoints_aio"
        elif "checkpoints" in collections:
            checkpoint_collection_name = "checkpoints"
        
        if checkpoint_collection_name:
            collection = db[checkpoint_collection_name]
            logger.info(f"✓ Found checkpoint collection: {checkpoint_collection_name}")
            
            # Count documents
            count = await collection.count_documents({})
            logger.info(f"✓ Collection contains {count} checkpoint(s)")
            
            # Find our test thread
            test_docs = await collection.count_documents({"thread_id": "test_thread_123"})
            logger.info(f"✓ Found {test_docs} checkpoint(s) for test thread")
        else:
            logger.error("✗ No checkpoint collection found")
            logger.info(f"  Available collections: {collections}")
            return False
        
        logger.info("\n✅ MongoDB saver test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"✗ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    import asyncio
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run test
    asyncio.run(test_mongodb_saver())
