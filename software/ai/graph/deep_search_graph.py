from langgraph.graph import StateGraph, START, END
from software.ai.graph.director_state import AnalystState, AnalystOutput, AnalysisReport, InitialPrediction, print_step, print_debug, print_pretty, print_markdown, vero_agent
from software.ai.graph.deep_documentor_graph import graph as deep_documentor_graph
from langgraph.prebuilt import create_react_agent
from software.ai.llm.llm_connect import get_llm_connect
from software.ai.tools import registry
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_core.messages.utils import count_tokens_approximately
from software.db.research_repository import ResearchRepository
from typing import Dict, Any
import datetime
import uuid
import os

research_repo = ResearchRepository()
MAX_OPERATIONS = 1
# Target exploration/exploitation ratio (exploration % / exploitation %)
# Higher exploration ratio (e.g., 70/30) favors discovering new avenues
# Higher exploitation ratio (e.g., 30/70) favors deepening existing findings
TARGET_EXPLORE_EXPLOIT_RATIO = 90/10

def create_research_folder(analyst_name: str, thread_id: str) -> str:
    """Create subfolder for research outputs"""
    folder_name = f"{analyst_name}_{thread_id[:12]}"
    base_path = "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/Vero/software/library/research"
    folder_path = os.path.join(base_path, folder_name)
    
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
    
    return folder_path

def save_tool_output(folder_path: str, tool_name: str, output: str) -> str:
    """Save tool output as markdown file"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    filename = f"{tool_name}_{timestamp}_{unique_id}.md"
    file_path = os.path.join(folder_path, filename)
    
    with open(file_path, 'w') as f:
        f.write(f"# {tool_name} Output\n\n")
        f.write(f"Generated: {datetime.datetime.now().isoformat()}\n\n")
        f.write("---\n\n")
        f.write(output)
    
    return filename

def update_files_metadata(folder_path: str, filename: str, status: str = "ready"):
    """Update files.json with metadata"""
    import json
    
    metadata_path = os.path.join(folder_path, "files.json")
    
    if os.path.exists(metadata_path):
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)
    else:
        metadata = {"files": []}
    
    file_entry = {
        "filename": filename,
        "datetime": datetime.datetime.now().isoformat(),
        "status": status
    }
    
    metadata["files"].append(file_entry)
    
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)

@vero_agent("Extracting")
async def extract_initial_prediction(state: AnalystState) -> Dict[str, Any]:
    """Extract initial prediction from successful tool outputs"""
    if not hasattr(state, 'tool_logs') or not state.tool_logs:
        return {"initial_prediction_done": False}
    
    # Collect successful tool content from all iterations
    successful_content = []
    for iteration_logs in state.tool_logs:
        for log in iteration_logs:
            if log['status'] != 'error' and log['tool_output_content']:
                successful_content.append(log['tool_output_content'])
    
    if not successful_content:
        return {"initial_prediction_done": False}
    
    try:
        director_id = getattr(state, 'director_id', '')
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm_for_stage(director_id, "extract_prediction")
        structured_model = model.with_structured_output(InitialPrediction)
        
        combined_content = "\n\n".join(successful_content)
        extraction_message = f"""Extract financial data from the following tool outputs:

{combined_content}

Extract the latest closing price, date, and make an initial prediction based on the data."""

        extracted_data = await structured_model.ainvoke([
            SystemMessage(content="Extract financial data for initial prediction."),
            HumanMessage(content=extraction_message)
        ])
        
        print(f"{state.name_analyst} prediction established: Current=${extracted_data.latest_closing_price}, Predicted=${extracted_data.initial_prediction}")
        print(f"{state.name_analyst} prediction reasoning: {extracted_data.prediction_reasoning}")
        
        return {
            "initial_prediction_done": True,
            "current_target_value": extracted_data.latest_closing_price,
            "predicted_target_value": extracted_data.initial_prediction
        }
        
    except Exception as e:
        print(f"Error extracting initial prediction: {str(e)}")
        return {"initial_prediction_done": False}

@vero_agent("Initializing")
async def initialize_deep_search(state: AnalystState) -> Dict[str, Any]:
    """Initialize the deep search workflow"""

    # Initialize deep search fields - ALWAYS start with empty tool_logs
    return {
        "messages": [],  # Start with empty messages for react agent
        "operations": 0,
        "max_operations": MAX_OPERATIONS,
        "summarized_tasks": "Starting deep search analysis",
        "summarized_results": "No results yet",
        "tasks": [state.business_question] if hasattr(state, 'business_question') else ["Should we invest in Nvidia (NVDA) in the next 30 days?"],
        "results": [],
        "tool_logs": [],  # Always start fresh
        "initial_prediction_done": False,
        "current_target_value": None,
        "predicted_target_value": None
    }

@vero_agent("Researching")
async def deep_supervisor(state: AnalystState) -> Dict[str, Any]:
    """Supervisor that performs iterative deep research"""
    print(f"Step {getattr(state, 'operations', 0)}/{getattr(state, 'max_operations', MAX_OPERATIONS)}")

    # Initialize tasks and results if they don't exist
    if not hasattr(state, 'tasks') or not state.tasks:
        state.tasks = [state.business_question]
    if not hasattr(state, 'results') or state.results is None:
        state.results = []

    task_description = state.tasks[-1]

    tools = await registry.get_langchain_tools_async()
    tool_prompt = await registry.generate_system_message_async()
    director_id = getattr(state, 'director_id', '')
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "supervisor")

    # Get current state values
    initial_prediction_done = getattr(state, 'initial_prediction_done', False)
    current_target_value = getattr(state, 'current_target_value', None)
    predicted_target_value = getattr(state, 'predicted_target_value', None)
    
    # Clear messages before starting react agent to keep it simple
    state.messages = []

    supervisor = create_react_agent(
        model=model,
        tools=tools,
        name="supervisor",
        prompt=f"""<role>
You are a financial research supervisor specializing in analyzing investment opportunities and market trends. Your expertise is in using specialized tools to gather and synthesize financial data.
</role>

<context>
The business question requires comprehensive research to find the best answer. You have access to multiple specialized tools that can help you gather the necessary information.

CURRENT STATE:
- Initial prediction established: {initial_prediction_done}
- Current target value: {current_target_value if current_target_value else "Not established"}
- Predicted target value: {predicted_target_value if predicted_target_value else "Not established"}

AVAILABLE TOOLS:
{tool_prompt}
</context>

<task>
Your goal is to find the best answer to: {task_description}
</task>

<tool_usage_guidance>
Use tools strategically to gather the information needed:
1. You may use multiple tools if it helps answer the business question more completely
2. Select tools that best address different aspects of the question
3. If initial tool results are insufficient, try other tools or approaches
4. Focus on finding useful information rather than strict tool limits
</tool_usage_guidance>

<reasoning_steps>
When analyzing:
1. First, check latest closing price and make initial prediction if not done yet
2. Second, understand what information is needed to answer the business question
3. Third, identify which tools can provide this information
4. Fourth, use the tools effectively to gather comprehensive data
5. Fifth, synthesize the findings into a clear answer
6. Sixth, format your response according to the output requirements
</reasoning_steps>

<tool_output_handling>
When you receive output from the tool:
1. Extract the key information from the tool's response
2. Reformat this information according to the required output format
3. NEVER return raw tool output without reformatting it
4. Ensure your final response follows the exact structure specified in the output format
5. Include all relevant information from the tool in your reformatted response
</tool_output_handling>

<error_handling>
If tools fail or cannot provide sufficient information:
1. Try alternative tools or adjusted parameters
2. If unable to find sufficient data, clearly state what information is missing
3. Provide the best possible analysis with available data
4. Always ensure your final response follows the required output format
</error_handling>

<output_format>
Your FINAL response must be a markdown-formatted report with these sections:
## Business question
[Restate the business question here]

## Conclusion
[Provide a clear, direct answer to the business question based on your findings]

## Reasoning
[Explain the reasoning behind your conclusion, including key data points]

## Tool Optimization
[Suggest ways to improve tool usage or parameters for better results]

Format your response exactly as shown above.
</output_format>""",
    )

    human_message = HumanMessage(content=f"Analyze this business question: {task_description} and provide a markdown-formatted report.")

    try:
        # Include thread_id in config for checkpointer
        config = {"configurable": {"thread_id": state.thread_id or str(uuid.uuid4())}}
        
        # Just pass the human message since we cleared messages
        supervisor_input = {
            "messages": [human_message]
        }
        
        response = await supervisor.ainvoke(supervisor_input, config=config)
    except Exception as e:
        print(f"Error in supervisor: {str(e)}")
        state.results.append("NULL")
        return {"messages": [], "results": state.results}

    # Store status in results
    state.results.append(f"Research completed")

    return {
        "messages": response.get("messages", []),
        "results": state.results
    }

@vero_agent("Processing")
async def create_tool_logs(state: AnalystState) -> Dict[str, Any]:
    """Process messages to create tool logs from supervisor output"""
    messages = getattr(state, 'messages', [])
    tool_logs = []
    
    # Process all messages to find tool messages
    for i, msg in enumerate(messages):
        # Check if it's a tool message
        if hasattr(msg, "content") and hasattr(msg, "name") and hasattr(msg, "tool_call_id"):
            status = getattr(msg, "status", None)

            # Add to tool logs
            tool_log_entry = {
                "tool_name": msg.name,
                "status": status if status else "unknown",
                "tool_output_content": msg.content
            }
            tool_logs.append(tool_log_entry)
    
    # Initialize tool_logs list if it doesn't exist
    if not hasattr(state, 'tool_logs') or state.tool_logs is None:
        state.tool_logs = []

    # Add current iteration's tool logs to the tool_logs list
    if tool_logs:
        state.tool_logs.append(tool_logs)
        print(f"✓ Processed {len(tool_logs)} tool messages")
    
    return {
        "tool_logs": state.tool_logs
    }

@vero_agent("Saving")
async def save_tools_output(state: AnalystState) -> Dict[str, Any]:
    """Save successful tool outputs to markdown files"""
    # Get the latest tool logs from the current iteration
    if not hasattr(state, 'tool_logs') or not state.tool_logs:
        return {}
    
    # Get the latest iteration's tool logs
    latest_logs = state.tool_logs[-1] if state.tool_logs else []
    
    if not latest_logs:
        return {}
    
    # Create research folder for this analyst
    analyst_name = getattr(state, 'name_analyst', 'Unknown')
    thread_id = getattr(state, 'thread_id', str(uuid.uuid4()))
    research_folder = create_research_folder(analyst_name, thread_id)
    
    saved_count = 0
    for tool_log in latest_logs:
        if tool_log.get('status') != 'error' and tool_log.get('tool_output_content'):
            # Save successful tool output to markdown file
            filename = save_tool_output(
                research_folder, 
                tool_log['tool_name'], 
                tool_log['tool_output_content']
            )
            update_files_metadata(research_folder, filename, "ready")
            saved_count += 1
    
    if saved_count > 0:
        print(f"✓ Saved {saved_count} tool outputs to {research_folder}")
    
    return {}

async def collect_reports(state: AnalystState) -> Dict[str, Any]:
    """Collects all reports and synthesizes them into a final report"""
    # await research_repo.update_workflow_stage(state.thread_id, "collect_reports", "in_progress")
    
    # Debug: Print what we received from deep_supervisor
    print_debug(f"collect_reports received state with operations: {getattr(state, 'operations', 0)}", "collect_reports")
    print_debug(f"Results length: {len(getattr(state, 'results', []))}", "collect_reports")
    if hasattr(state, 'results') and state.results:
        print_debug(f"Latest result: {state.results[-1]}", "collect_reports")
    print_debug(f"Initial prediction done: {getattr(state, 'initial_prediction_done', False)}", "collect_reports")
    print_debug(f"Current target value: {getattr(state, 'current_target_value', None)}", "collect_reports")
    print_debug(f"Predicted target value: {getattr(state, 'predicted_target_value', None)}", "collect_reports")
    
    # Debug: Print tool_logs state
    print_debug(f"Tool logs length in collect_reports: {len(getattr(state, 'tool_logs', []))}", "collect_reports")
    
    director_id = getattr(state, 'director_id', '')
    last_tasks = state.tasks[-1] if hasattr(state, 'tasks') and state.tasks else state.business_question
    last_results = state.results[-1] if hasattr(state, 'results') and state.results else ""

    summarized_tasks = getattr(state, 'summarized_tasks', "No tasks yet")
    summarized_results = getattr(state, 'summarized_results', "No results yet")
    operations = getattr(state, 'operations', 0)
    max_operations = getattr(state, 'max_operations', MAX_OPERATIONS)

    # Get target exploration/exploitation ratio
    target_ratio = TARGET_EXPLORE_EXPLOIT_RATIO
    target_explore_percentage = int((target_ratio.real / (target_ratio.real + target_ratio.imag)) * 100)
    target_exploit_percentage = 100 - target_explore_percentage

    print_step(f"Processing Iteration #{operations+1}/{max_operations}", "Collecting Reports", "medium_purple", verbose=True)

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "collect_reports")

    # System message for tasks summarization
    tasks_system_message = SystemMessage(content=f"""<role>
You are a financial research task manager responsible for tracking all research questions and avenues of investigation.
</role>

<task>
Create a comprehensive summary of all research tasks, maintaining a complete record of the research journey while evaluating task effectiveness and balancing exploration vs. exploitation.
</task>

<context>
You are summarizing research tasks for iteration #{operations+1} of {max_operations}. This summary will be used to track the research path, evaluate task effectiveness, and inform future directions.

RESEARCH PARAMETERS:
- Current iteration: {operations+1} of {max_operations} total iterations
- Target exploration/exploitation ratio: 🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%
- Research completion: {int((operations/max_operations)*100)}% complete
</context>

<summarization_principles>
1. COMPLETENESS: Maintain a record of all tasks in chronological order
2. ORGANIZATION: Group similar tasks together when possible
3. CLARITY: Ensure all specific metrics, dates, and parameters are preserved
4. FORMATTING: Use consistent bullet points for readability
5. EVALUATION: Assess each task's effectiveness based on the insights it generated
6. BALANCE: Track the balance between exploration (NEW ROUTE) and exploitation (DEEP DIVE)
</summarization_principles>

<task_evaluation_criteria>
For each completed task, evaluate its effectiveness based on available information:
1. RELEVANCE: How directly it addresses the core business question
2. INSIGHT QUALITY: The value and uniqueness of information it provided (based on the task description and summarized results)
3. IMPACT: How significantly it influenced the overall research direction
4. TOOL UTILIZATION: How effectively it leveraged the selected tool (based on tool name and status)
5. STRATEGIC VALUE: How well the task contributes to achieving the target exploration/exploitation ratio (🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%) by the end of all {max_operations} iterations

Note: Your evaluation should be based on the summarized information available in the current task summary. For the most recent task, you have access to the latest result summary and tool usage, but for older tasks, rely on your existing summary and the task descriptions.
</task_evaluation_criteria>

<output_format>
Structure your summary with:
- **Updated Summary**:
  - **Task Updates**: List all tasks with their specific parameters and effectiveness ratings
    - **Task #N**: *Task description* [Effectiveness: ⭐⭐⭐⭐⭐/⭐⭐⭐/⭐]
      - **Parameters**: List all relevant parameters
      - **Tool Used**: Tool name and whether it was 🔍 DEEP DIVE or 🧭 NEW ROUTE
      - **Key Insights**: Brief mention of most valuable findings (if completed)
      - **Status**: Current status (🔄 Open/✅ Completed)
  - **Research Balance**: Current ratio of exploration vs. exploitation tasks (e.g., 🧭 40% / 🔍 60%)
  - **Status**: Overall status of the research journey

Use visual indicators to quickly convey status and quality:
- Task effectiveness: ⭐⭐⭐⭐⭐ (Very High), ⭐⭐⭐⭐ (High), ⭐⭐⭐ (Medium), ⭐⭐ (Low), ⭐ (Very Low)
- Task type: 🔍 for exploitation (DEEP DIVE), 🧭 for exploration (NEW ROUTE)
- Status: 🔄 for in-progress tasks, ✅ for completed tasks, ⚠️ for tasks with issues

Use bullet points for clarity and organization. When mentioning dates, ALWAYS use yyyy-mm-dd format (e.g., 2025-05-09) for consistency and proper timeline reference.
</output_format>""")

    # System message for results summarization
    # Determine preservation tier based on iteration number
    current_iteration = operations + 1
    if current_iteration <= 5:
        preservation_percentage = 100
        preservation_phase = "Foundation Phase"
    elif current_iteration <= 15:
        preservation_percentage = 95
        preservation_phase = "Exploration Phase"
    else:
        preservation_percentage = 90
        preservation_phase = "Exploitation Phase"
    
    results_system_message = SystemMessage(content=f"""<role>
You are a financial research archivist responsible for maintaining a comprehensive historical record of investment research.
</role>

<task>
Create a balanced summary that preserves historical context while integrating new information. Your summary must maintain a complete record of all key findings across all reports.
</task>

<context>
You are summarizing research results for iteration #{current_iteration} of {max_operations}.
Current Phase: {preservation_phase} (Preservation: {preservation_percentage}%)
This summary will be used for memory retrieval and to inform future research directions.
</context>

<tiered_preservation_strategy>
- Iterations 1-5 (Foundation): 100% preservation - Keep EVERYTHING
- Iterations 6-15 (Exploration): 95% preservation - Light compression of redundancies
- Iterations 16-25 (Exploitation): 90% preservation - Smart compression while preserving breakthroughs
Current Iteration: {current_iteration} → {preservation_percentage}% preservation
</tiered_preservation_strategy>

<summarization_principles>
1. TIERED PRESERVATION: Maintain {preservation_percentage}% of historical information based on current phase
2. METRIC VAULT: The following MUST be preserved at 100% regardless of phase:
   - All numerical values (prices, percentages, confidence levels)
   - All dates and timeframes
   - Tool performance scores and effectiveness ratings
   - Breakthrough findings that changed predictions
   - Contradictions between iterations
3. SEMANTIC DEDUPLICATION: Instead of removing duplicates, merge them:
   - "AAPL resistance at $150 (iterations 3, 7, 12)" preserves multiple confirmations
4. SMART COMPRESSION (for later phases):
   - Never compress: Numbers, dates, tool names, contradictions, breakthroughs
   - Lightly compress: Supporting evidence chains (95% phase)
   - Can compress: Redundant narrative, failed tool attempts (90% phase)
5. CHRONOLOGICAL AWARENESS: Clearly indicate which findings came from which iteration
6. CROSS-REFERENCING: Highlight connections between findings across different reports
</summarization_principles>

<output_format>
Structure your summary with these sections:

## Metric Vault (Never Compressed)
- **Price Data**: All current/predicted prices with dates and iterations
- **Key Metrics**: RSI, volume, moving averages, etc. with iteration references
- **Tool Performance**: Effectiveness ratings and breakthrough tools
- **Confidence Evolution**: How confidence changed across iterations
- **Critical Dates**: All mentioned dates and timeframes

## Phase Summary: {preservation_phase}
- Current Iteration: {current_iteration}/{max_operations}
- Preservation Level: {preservation_percentage}%
- Exploration/Exploitation Balance: Current vs Target (90/10)

## Research Journey (Iteration {current_iteration})
1. **Business Question** (from current report)
2. **Conclusion** (from current report with historical context)
3. **Reasoning** (merged from all reports, noting iteration sources)
4. **Tool Optimization** (all iterations, sorted by effectiveness)
5. **Missing Data** (gaps identified across all iterations)
6. **Recommendations** (evolution across iterations)
7. **Breakthrough Moments** (iterations that significantly changed analysis)

Use bullet points for clarity. Format: "Finding (iteration X, Y, Z)" to track sources.
When mentioning dates, ALWAYS use yyyy-mm-dd format (e.g., 2025-05-09).
</output_format>""")

    try:
        tasks_message_content = f"""Update the tasks summary by adding the latest research task while maintaining a comprehensive record and evaluating task effectiveness.

RESEARCH PARAMETERS:
- Current iteration: {operations+1} of {max_operations} total iterations
- Target exploration/exploitation ratio: 🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%
- Research completion: {int((operations/max_operations)*100)}% complete
- Remaining iterations: {max_operations - operations}

CURRENT TASKS SUMMARY:
{summarized_tasks}

NEW TASK:
{last_tasks}

PREVIOUS RESULTS:
{["Result #" + str(len(getattr(state, "results", [])) - 1) + " (Latest): " + (getattr(state, "results", ["No results yet"])[-2][:150] + "..." if len(getattr(state, "results", [""])[:-1]) > 0 and len(getattr(state, "results", [""])[-2]) > 150 else getattr(state, "results", ["No previous results yet"])[-2] if len(getattr(state, "results", [])) > 1 else "No previous results yet")] if len(getattr(state, "results", [])) > 1 else ["No previous results yet"]}

TOOL USAGE:
{[{"tool_name": tool.get("tool_name", "unknown"), "status": tool.get("status", "unknown")} for tool in getattr(state, "tool_logs", [])[-1]] if hasattr(state, 'tool_logs') and getattr(state, 'tool_logs') and len(getattr(state, 'tool_logs', [])) > 0 else "No tool usage data available"}

INSTRUCTIONS:
1. Maintain all historical tasks in chronological order
2. Add the new task with appropriate formatting
3. Evaluate each task's effectiveness using star ratings (⭐⭐⭐⭐⭐ to ⭐) based on:
   - Relevance to the business question
   - Quality of insights generated
   - Impact on research direction
   - Effective tool utilization
4. Identify whether each task was exploration (🧭 NEW ROUTE) or exploitation (🔍 DEEP DIVE)
5. Calculate the current exploration/exploitation ratio with icons (e.g., 🧭 40% / 🔍 60%) and compare it to the target ratio (🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%)
6. Mark task status with appropriate icons (🔄 Open, ✅ Completed, ⚠️ Issues)
7. Group similar tasks together when possible
8. Ensure all specific metrics, dates, and parameters are preserved
9. Format consistently with bullet points for readability

The tasks summary should provide a complete picture of the research journey with effectiveness evaluation and exploration/exploitation balance. Consider how the current balance compares to the target ratio (🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%) and how many iterations remain ({max_operations - operations}) to achieve this target. For older tasks, rely on your existing summary; for the newest task, use the latest result and tool usage information provided.
"""
        response_tasks = await model.ainvoke([tasks_system_message, HumanMessage(content=tasks_message_content)])
        if "</think>" in response_tasks.content:
            response_tasks.content = response_tasks.content.split("</think>", 1)[1].strip()
        
        new_summarized_tasks = response_tasks.content
        print_pretty(new_summarized_tasks)

    except Exception as e:
        print_debug(f"Error summarizing tasks: {str(e)}", "collect_reports")
        new_summarized_tasks = summarized_tasks
        # await research_repo.update_workflow_stage(state.thread_id, "collect_reports", "failed")
    
    try:
        results_message_content = f"""Update the research summary by integrating new findings from Iteration #{operations+1} while preserving historical context.

CURRENT SUMMARY:
{summarized_results}

NEW FINDINGS FROM ITERATION #{operations+1}:
{last_results}

INSTRUCTIONS:
1. Maintain 70-80% of the historical information from previous iterations
2. Add 20-30% of the most important new information from Iteration #{operations+1}
3. Clearly label which findings came from which iteration number
4. Remove truly redundant information but preserve similar findings with different nuances
5. Highlight connections between findings across different iterations
6. Ensure all key metrics, dates, and numerical values are preserved
7. Update the "All Reports Summary" section to reflect the complete research journey
8. PRESERVE ALL CRITICAL DATA POINTS including:
   - Ticker symbols
   - Current and forecasted prices with dates
   - Recommendations (BUY/SELL/HOLD)
   - Confidence levels

Remember to follow the structured output format with all required sections.
"""
        response_results = await model.ainvoke([results_system_message, HumanMessage(content=results_message_content)])
        if "</think>" in response_results.content:
            response_results.content = response_results.content.split("</think>", 1)[1].strip()

        # Store the summarized results
        new_summarized_results = response_results.content
        print_pretty(new_summarized_results)
    except Exception as e:
        print_debug(f"Error summarizing results: {str(e)}", "collect_reports")
        new_summarized_results = summarized_results
        # await research_repo.update_workflow_stage(state.thread_id, "collect_reports", "failed")

    new_operations = operations + 1
    # await research_repo.update_workflow_stage(state.thread_id, "collect_reports", "completed")

    should_continue = new_operations < max_operations
    
    # Debug: Print what we're returning
    print_debug(f"collect_reports returning: operations={new_operations}, should_continue={should_continue}", "collect_reports")
    print_debug(f"Current target value being returned: {getattr(state, 'current_target_value', None)}", "collect_reports")
    print_debug(f"Predicted target value being returned: {getattr(state, 'predicted_target_value', None)}", "collect_reports")
    
    return {
        "operations": new_operations,
        "max_operations": max_operations,
        "summarized_tasks": new_summarized_tasks,
        "summarized_results": new_summarized_results,
        "should_continue": should_continue,
        "context": getattr(state, 'context', {}),  # Pass context through
        "current_target_value": getattr(state, 'current_target_value', None),
        "predicted_target_value": getattr(state, 'predicted_target_value', None),
        "initial_prediction_done": getattr(state, 'initial_prediction_done', False),
        "tool_logs": state.tool_logs  # Pass tool_logs through
    }

def should_continue_deep_search(state: AnalystState) -> str:
    """Determine if deep search should continue or finish"""
    if getattr(state, 'should_continue', True) and getattr(state, 'operations', 0) < getattr(state, 'max_operations', MAX_OPERATIONS):
        return "continue_deep_search"
    else:
        return "finish_deep_search"

def route_after_supervisor(state: AnalystState) -> str:
    """Route to extract initial prediction if not done, otherwise to collect reports"""
    if not getattr(state, 'initial_prediction_done', False) and hasattr(state, 'tool_logs') and state.tool_logs:
        return "extract_initial_prediction"
    else:
        return "collect_reports"

async def create_plan(state: AnalystState) -> Dict[str, Any]:
    """Creates a research plan with tasks, with optional supervisor evaluation"""
    # await research_repo.update_workflow_stage(state.thread_id, "create_plan", "in_progress")
    print_debug("Creating research plan", "create_plan")
    director_id = getattr(state, 'director_id', '')
    summarized_tasks = getattr(state, 'summarized_tasks', "No tasks yet")
    summarized_results = getattr(state, 'summarized_results', "No results yet")
    
    # Initialize tasks list if it doesn't exist
    if not hasattr(state, 'tasks') or state.tasks is None:
        state.tasks = [state.business_question]
    
    last_three_tasks = state.tasks[-3:] if len(state.tasks) >= 3 else state.tasks
    analyst_name = getattr(state, 'name_analyst', 'Unknown Analyst')
    print_markdown(summarized_tasks, f"Summarized Tasks - {analyst_name}")
    print_markdown(summarized_results, f"Summarized Results - {analyst_name}")

    tool_prompt = await registry.generate_system_message_async()

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "create_plan")
    operations = getattr(state, 'operations', 0)
    max_operations = getattr(state, 'max_operations', MAX_OPERATIONS)
    target_ratio = TARGET_EXPLORE_EXPLOIT_RATIO

    # Calculate exploration percentage from target ratio
    target_explore_percentage = int((target_ratio.real / (target_ratio.real + target_ratio.imag)) * 100)
    target_exploit_percentage = 100 - target_explore_percentage

    system_message = SystemMessage(content=f"""<role>
You are a senior hedge fund research analyst with 15+ years of experience creating precise, focused business questions for investment analysis. You excel at strategic research planning, balancing exploration of new avenues with exploitation of promising leads to maximize investment insights.
</role>

<context>
The business question requires targeted research that will be executed by specialized research agents. Each question should focus on a single, specific aspect of the analysis.

RESEARCH PARAMETERS:
- Current iteration: {operations+1} of {max_operations} total iterations
- Target exploration/exploitation ratio: 🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%
- Research completion: {int((operations/max_operations)*100)}% complete

AVAILABLE TOOLS:
{tool_prompt}

LAST THREE TASKS:
{"\n".join(last_three_tasks)}
</context>

<task>
Create one highly focused business question that either explores a new avenue of investigation OR deepens understanding of a promising insight from previous research, strategically balancing exploration and exploitation based on:
1. Current progress through the research timeline ({int((operations/max_operations)*100)}% complete)
2. Target exploration/exploitation ratio (🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%)
3. Current task effectiveness patterns
</task>

<research_strategy>
Make a strategic choice between two approaches based on the current research state:

OPTION 1: DEEPEN AN EXISTING BRANCH (🔍 EXPLOITATION)
- Follow a promising lead from a previous high-effectiveness task (⭐⭐⭐⭐+)
- Drill deeper into a specific metric or finding that showed potential
- Use the keyword "🔍 DEEP DIVE" at the beginning of your question
- Prioritize this when:
  - Previous exploitation tasks yielded high-value insights
  - You're in later research stages (>50% of total iterations)
  - Current exploration percentage exceeds the target ({target_explore_percentage}%)

OR

OPTION 2: CREATE A NEW AVENUE (🧭 EXPLORATION)
- Explore a completely different aspect not yet investigated
- Use tools or analysis methods not yet applied to this company
- Use the keyword "🧭 NEW ROUTE" at the beginning of your question
- Prioritize this when:
  - Previous exploration revealed promising new directions
  - Exploitation tasks are showing diminishing returns (decreasing ⭐ ratings)
  - Current exploration percentage is below the target ({target_explore_percentage}%)
  - You're in early research stages (<50% of total iterations)
</research_strategy>

<adaptive_balancing>
Consider these factors when deciding between exploration and exploitation:
1. RESEARCH PROGRESS: Current iteration ({operations+1}) out of total iterations ({max_operations})
   - Early stage (0-33%): Favor exploration to discover promising avenues
   - Middle stage (34-66%): Balance exploration and exploitation based on target ratio
   - Late stage (67-100%): Favor exploitation of the most promising avenues

2. TARGET RATIO: Aim for 🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}% by the end of all iterations
   - If current exploration % < {target_explore_percentage}%: Favor exploration
   - If current exploration % > {target_explore_percentage}%: Favor exploitation
   - As you approach the final iterations, prioritize meeting the target ratio

3. EFFECTIVENESS PATTERNS: Analyze which approach has been more effective
   - If exploration tasks average higher star ratings: Favor exploration
   - If exploitation tasks average higher star ratings: Favor exploitation
   - If a specific avenue shows diminishing returns: Switch approaches

4. REMAINING ITERATIONS: Consider how many iterations remain ({max_operations - operations})
   - With many iterations left: You can afford to explore more
   - With few iterations left: Focus on exploiting the most promising leads
</adaptive_balancing>

<output_requirements>
1. Format: "Using [Tool Name] 🔍 DEEP DIVE: [specific question]?" or "Using [Tool Name] 🧭 NEW ROUTE: [specific question]?"
2. Include informative context in [brackets] after the question
3. Context must explain:
   - Which specific finding you're following up on (if 🔍 deepening)
   - What key information the supervisor needs to understand this task
   - Why you chose 🧭 exploration or 🔍 exploitation based on current research state
4. Maximum length: 25 words for the question + 25 words for the context
5. Must include:
   - Exact ticker symbol
   - Specific metrics/indicators with parameters
   - Precise time periods
   - Numerical values from previous results (if applicable)
</output_requirements>

<examples>
<example>
Previous Tasks Summary:
- **Task #1**: Using FinancialNewsTool 🧭 NEW ROUTE: What recent news might impact AAPL stock price in the next week? [Effectiveness: ⭐⭐⭐]
- **Task #2**: Using StockPriceTool 🧭 NEW ROUTE: What is AAPL's current price and 50-day moving average? [Effectiveness: ⭐⭐⭐⭐⭐]

Good Output (Exploitation):
Using TechnicalAnalysisTool 🔍 DEEP DIVE: How has AAPL's price performed relative to its 50-day MA during market corrections? [Following up on Task #2's finding that AAPL is trading 5% above its 50-day MA; exploitation optimal at iteration 3/10 with current 67% exploration]

Good Output (Exploration):
Using CompetitorAnalysisTool 🧭 NEW ROUTE: How does AAPL's P/E ratio compare to MSFT, GOOG in the past 30 days? [Exploring valuation metrics not yet investigated; exploration needed at iteration 3/10 to reach target 90% exploration ratio]
</example>

<example>
Previous Tasks Summary:
- **Task #1**: Using EconomicIndicatorTool 🧭 NEW ROUTE: How do rising interest rates correlate with JPM stock performance? [Effectiveness: ⭐⭐⭐⭐]
- **Task #2**: Using FinancialStatementTool 🔍 DEEP DIVE: What is JPM's net interest margin trend over the past 4 quarters? [Effectiveness: ⭐⭐]

Good Output (Exploitation):
Using EconomicIndicatorTool 🔍 DEEP DIVE: How did JPM perform during the last 3 Fed rate hike cycles from 2015-2018? [Following Task #1's finding of 0.72 correlation between rates and JPM; exploitation needed at iteration 3/5 with only 40% exploitation]

Good Output (Exploration):
Using SentimentAnalysisTool 🧭 NEW ROUTE: What is the current analyst sentiment on JPM compared to other major banks? [Exploring sentiment dimension not yet investigated; exploration optimal at iteration 2/10 with current 50% exploration vs. target 70%]
</example>
</examples>

<error_handling>
If you encounter challenges:
1. If previous tasks show no clear high-value leads, create a question that explores a fundamentally different aspect of the company
2. If the business question is vague, focus on the most financially relevant metrics for the ticker
3. If previous tasks have conflicting effectiveness ratings, prioritize the most recent high-rated task
4. If no tool seems perfectly suited, select the tool that can provide the closest relevant information
5. If you're unsure about specific parameters, use industry-standard defaults (e.g., 14-day RSI, 50/200-day MAs)
</error_handling>

<output_format>
Using [Tool Name] 🔍 DEEP DIVE: [concise question with specific metrics for TICKER]? [Brief context explaining which finding is being followed up on and why exploitation is optimal at iteration {operations+1}/{max_operations} with current exploration/exploitation balance]

OR

Using [Tool Name] 🧭 NEW ROUTE: [concise question with specific metrics for TICKER]? [Brief context explaining why this avenue is valuable and why exploration is optimal at iteration {operations+1}/{max_operations} with current exploration/exploitation balance]
</output_format>""")
    
    human_message = HumanMessage(content=f"""Create a focused business question based on previous research, task effectiveness, and strategic exploration/exploitation balance.

RESEARCH PARAMETERS:
- Current iteration: {operations+1} of {max_operations} total iterations
- Target exploration/exploitation ratio: 🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%
- Research completion: {int((operations/max_operations)*100)}% complete
- Remaining iterations: {max_operations - operations}

Previous tasks summary:
{summarized_tasks}

Previous results summary:
{summarized_results}

INSTRUCTIONS:
1. Analyze the current exploration/exploitation ratio (🧭/🔍) in the task summary compared to the target ratio (🧭 {target_explore_percentage}% / 🔍 {target_exploit_percentage}%)
   - Calculate the exact current ratio from the task summary
   - Determine how far the current ratio is from the target ratio
   - Identify which approach (exploration or exploitation) needs to be prioritized to reach the target

2. Consider your position in the research timeline:
   - Early stage (0-33% complete): Favor exploration to discover promising avenues
   - Middle stage (34-66% complete): Balance according to target ratio and effectiveness
   - Late stage (67-100% complete): Favor exploitation of the most promising avenues

3. Evaluate task effectiveness patterns:
   - Calculate the average star rating for exploration tasks vs. exploitation tasks
   - Identify which approach has yielded the highest-rated tasks (⭐⭐⭐⭐+)
   - Check if any approach shows diminishing returns (decreasing star ratings over time)
   - Identify the most promising leads from high-rated tasks (⭐⭐⭐⭐+)

4. Make a strategic decision based on:
   - Current research stage ({int((operations/max_operations)*100)}% complete)
   - Gap between current and target exploration/exploitation ratio
   - Effectiveness patterns from previous tasks
   - Number of remaining iterations ({max_operations - operations})
   - Presence of high-potential leads from recent tasks

5. Format your response using the appropriate keyword and icon:
   - For exploitation: "Using [Tool Name] 🔍 DEEP DIVE: [specific question]?"
   - For exploration: "Using [Tool Name] 🧭 NEW ROUTE: [specific question]?"

6. Add brief context in [brackets] after the question that explains:
   - Which specific finding you're following up on (if DEEP DIVE)
   - Why this avenue is valuable to explore (if NEW ROUTE)
   - Why you chose exploration or exploitation at this point in the research
   - Any key information needed to understand this task's purpose

7. Ensure your question includes:
   - Exact ticker symbol
   - Specific metrics/indicators with precise parameters
   - Exact time periods (e.g., "past 30 days" not "recent")
   - Numerical values from previous results when applicable
   - Maximum 25 words for the question + 25 words for the context

8. Select the most appropriate tool based on:
   - The specific information needed for your question
   - The tool's capabilities as described in the available tools section
   - Previous successful tool usage patterns

9. Make the context informative for a supervisor who hasn't seen previous research by:
   - Referencing specific task numbers and their key findings
   - Including exact metrics and values from previous results
   - Explaining your strategic reasoning clearly and concisely""")

    try:
        # Store the system message and human message for reuse in supervisor evaluation
        system_msg_for_eval = system_message
        human_msg_for_eval = human_message
        
        response = await model.ainvoke([system_message, human_message])
        
        if "</think>" in response.content:
            response.content = response.content.split("</think>", 1)[1].strip()
            
        production_task = response.content
        print_step(production_task, "Created Plan (Production)", "random", verbose=True)

        # Strategic Context Keeper drift evaluation is handled in collect_reports and summary nodes

        state.tasks.append(production_task)
        # await research_repo.update_workflow_stage(state.thread_id, "create_plan", "completed")
        return {
            "tasks": state.tasks,
            "business_question": production_task  # Update business_question for the next iteration
        }

    except Exception as e:
        print_debug(f"Error creating plan: {str(e)}", "create_plan")
        # await research_repo.update_workflow_stage(state.thread_id, "create_plan", "failed")
        return {}

async def finish_deep_search(state: AnalystState) -> Dict[str, Any]:
    """Finalize deep search by returning the summarized results"""
    print_step("FINISH DEEP SEARCH", "Finalizing deep search results")
    
    # Get the final summarized results
    summarized_results = getattr(state, 'summarized_results', '')
    
    # Get metadata for debugging
    operations = getattr(state, 'operations', 0)
    business_question = getattr(state, 'business_question', '')
    raw_tool_logs = getattr(state, 'tool_logs', [])
    
    # Clean tool_logs - only keep the current run's data
    tool_logs = []
    if isinstance(raw_tool_logs, list):
        # Only keep list items that contain actual tool data (dicts)
        for item in raw_tool_logs:
            if isinstance(item, list) and len(item) > 0:
                # Each item should be a list of tool dicts for one iteration
                valid_tools = [t for t in item if isinstance(t, dict) and 'tool_name' in t]
                if valid_tools:
                    tool_logs.append(valid_tools)
    
    print_debug(f"Returning summarized results from {operations} iterations", "finish_deep_search")
    print_debug(f"Business question: {business_question}", "finish_deep_search")
    print_debug(f"Results length: {len(summarized_results)} characters", "finish_deep_search")
    print_debug(f"Clean tool logs: {len(tool_logs)} iterations tracked", "finish_deep_search")
    
    # Get task history for each iteration
    tasks = getattr(state, 'tasks', [])
    
    # Transform tool_logs to the new DirectorState format
    transformed_tool_logs = {
        "analyst_name": getattr(state, 'name_analyst', 'Unknown'),
        "analyst_title": getattr(state, 'title_analyst', 'Unknown'),
        "iterations": [
            {
                "iteration_number": i + 1,
                "business_question": tasks[i] if i < len(tasks) else business_question,
                "tools": iteration_tools
            }
            for i, iteration_tools in enumerate(tool_logs)
        ]
    }
    
    
    # Return the summarized results as a string in completed_reports
    return {
        "completed_reports": [
            HumanMessage(content=state.tasks[0] if hasattr(state, 'tasks') and state.tasks else business_question),
            AIMessage(content=summarized_results)
        ] if summarized_results else [],
        "tool_logs": transformed_tool_logs
    }

def create_simple_agent_tool_graph() -> StateGraph:
    """Create the deep search graph that replaces simple_agent_tool_graph"""
    subgraph = StateGraph(AnalystState, output=AnalystOutput)
    
    # Add all nodes
    subgraph.add_node("initialize_deep_search", initialize_deep_search)
    subgraph.add_node("deep_supervisor", deep_supervisor)
    subgraph.add_node("create_tool_logs", create_tool_logs)
    subgraph.add_node("save_tools_output", save_tools_output)
    subgraph.add_node("extract_initial_prediction", extract_initial_prediction)
    subgraph.add_node("deep_documentor", deep_documentor_graph)
    subgraph.add_node("collect_reports", collect_reports)
    subgraph.add_node("create_plan", create_plan)
    subgraph.add_node("finish_deep_search", finish_deep_search)
    
    # Connect nodes with edges
    subgraph.add_edge(START, "initialize_deep_search")
    subgraph.add_edge("initialize_deep_search", "deep_supervisor")
    subgraph.add_edge("deep_supervisor", "create_tool_logs")
    subgraph.add_edge("create_tool_logs", "save_tools_output")
    
    # Route after saving tools to extract prediction or deep_documentor
    subgraph.add_conditional_edges(
        "save_tools_output",
        route_after_supervisor,
        {
            "extract_initial_prediction": "extract_initial_prediction",
            "collect_reports": "deep_documentor"
        }
    )
    
    subgraph.add_edge("extract_initial_prediction", "deep_documentor")
    subgraph.add_edge("deep_documentor", "collect_reports")
    
    # Conditional edge to continue or finish
    subgraph.add_conditional_edges(
        "collect_reports",
        should_continue_deep_search,
        {
            "continue_deep_search": "create_plan",
            "finish_deep_search": "finish_deep_search"
        }
    )
    
    subgraph.add_edge("create_plan", "deep_supervisor")
    subgraph.add_edge("finish_deep_search", END)
    
    return subgraph.compile()

# Export the graph that replaces simple_agent_tool_graph
graph = create_simple_agent_tool_graph()

__all__ = ['graph']