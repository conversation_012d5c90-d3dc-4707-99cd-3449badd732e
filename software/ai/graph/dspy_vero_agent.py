import sys
import os
import asyncio
from typing import Dict, Any, List, TypedDict, Literal
import dspy
import json
from langchain_core.tools import tool
from langgraph.graph import StateGraph, START, END

# Adjusting sys.path imports as per the original structure, assuming necessary dependencies exist
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

# Assuming these imports work in the user's environment
try:
    from software.ai.graph.mongodbsaver import VeroMongoDBSaver
    from software.ai.llm.dspy_connect import get_dspy_llm
    from software.ai.graph.director_state import vero_agent
except ImportError:
    # Fallback definitions if imports fail, for runnable example
    print("Warning: Using fallback definitions for Vero dependencies.")
    VeroMongoDBSaver = lambda: None
    
    async def get_dspy_llm():
        # Use a default available model if specific connector fails
        try:
            import openai
            # Ensure you have OPENAI_API_KEY set in your environment variables
            return dspy.OpenAI(model="gpt-4o-mini", max_tokens=1000)
        except ImportError:
            raise ImportError("Please install openai: pip install openai")
        except Exception as e:
            raise RuntimeError(f"Could not initialize default DSPy LLM: {e}")

    def vero_agent(name):
        def decorator(func):
            return func
        return decorator


# --- DSPy Agent Implementation ---

class DspyVeroAgent:
    """
    Self-tuning ReAct agent using DSPy and LangGraph to discover and adapt to 
    unknown ground truths ("different universes") via hypothesis testing.
    """

    class OptimizerState(TypedDict):
        """State for prompt optimization workflow"""
        prompts: List[str]
        task: str
        conversation: List[Dict[str, Any]] # Stores prompt, response, trace, and evaluation for each attempt
        scores: List[float]
        feedback: str
        eval_parameters: str # The definition of "ground truth" for this universe
        
        # Optimization Control
        optimized: bool
        score_threshold: int
        max_attempts: int
        attempts: int
        
        # Hypothesis Discovery
        discovered_rules: List[str] # Rules that have been tested and validated
        hypotheses_tested: List[Dict[str, Any]] # Log of all hypotheses tried
        current_hypothesis: str # The hypothesis currently being tested
        hypothesis_status: Literal["pending", "validated", "rejected", None]

    # --- DSPy Signatures for Discovery and Optimization ---

    class EvaluatePerformance(dspy.Signature):
        """Evaluate if agent met ALL requirements strictly. Be ruthless. Identify discrepancies between output and ground truth."""
        task: str = dspy.InputField(desc="The task given to agent")
        agent_response: str = dspy.InputField(desc="Agent's final answer")
        agent_trace: str = dspy.InputField(desc="The full trace of agent thoughts and tool use.")
        ground_truth_requirements: str = dspy.InputField(desc="The strict requirements defining success in this universe.")
        
        score: int = dspy.OutputField(desc="Score 0-100. 100 means ALL requirements met exactly.")
        discrepancy_analysis: str = dspy.OutputField(desc="Detailed analysis of why the score is not 100. Pinpoint exactly what the agent did wrong compared to the ground truth.")

    class GenerateHypothesis(dspy.Signature):
        """Analyze the discrepancy between the agent's output and the evaluator's feedback to propose a concrete, generalized rule that might explain the difference in this 'universe'."""
        task: str = dspy.InputField()
        agent_output: str = dspy.InputField()
        discrepancy_analysis: str = dspy.InputField(desc="Feedback from the evaluator explaining the mismatch with ground truth.")
        
        proposed_rule: str = dspy.OutputField(desc="A specific, generalized hypothesis about a rule (e.g., 'In this context, numbers are not counted as words') that would reconcile the agent output with the expected output.")

    class TestHypothesis(dspy.Signature):
        """Simulate applying the proposed rule to the original agent execution trace to see if it reconciles the output with the ground truth described in the feedback."""
        task: str = dspy.InputField()
        proposed_rule: str = dspy.InputField()
        original_agent_trace: str = dspy.InputField(desc="The steps the agent took (thoughts, tool use, outputs).")
        discrepancy_analysis: str = dspy.InputField(desc="The feedback describing the gap between agent output and ground truth.")
        
        test_passed: bool = dspy.OutputField(desc="Does applying this rule resolve the discrepancy described in the feedback? (True/False)")
        explanation: str = dspy.OutputField(desc="Step-by-step explanation of how applying the rule changes the outcome and if it matches the ground truth expectation.")

    class OptimizePrompt(dspy.Signature):
        """Create a new prompt that incorporates validated rules to guide future behavior."""
        current_prompt: str = dspy.InputField()
        validated_rules: str = dspy.InputField(desc="List of rules discovered about this universe's ground truth.")
        
        improved_prompt: str = dspy.OutputField(desc="New prompt integrating the rules concisely. Must maintain core instructions about tool usage.")

    def __init__(self, dspy_lm, tools=None, checkpointer=None):
        self.dspy_lm = dspy_lm
        self.tools = tools or []
        self.checkpointer = checkpointer
        self._graph = None

        # Configure DSPy
        dspy.configure(lm=self.dspy_lm)

        # Create DSPy modules
        self.evaluator = dspy.ChainOfThought(self.EvaluatePerformance)
        self.hypothesis_generator = dspy.ChainOfThought(self.GenerateHypothesis)
        self.hypothesis_tester = dspy.ChainOfThought(self.TestHypothesis)
        self.optimizer = dspy.Predict(self.OptimizePrompt)

    # --- Graph Nodes ---

    @vero_agent("Running DSPy ReAct Agent")
    async def run_react_agent(self, state: OptimizerState) -> Dict[str, Any]:
        """Run DSPy ReAct agent with the current prompt and capture the execution trace."""
        prompts = state["prompts"]
        current_prompt = prompts[-1]
        task = state["task"]

        print(f"\n--- ATTEMPT {state['attempts'] + 1} / {state['max_attempts']} ---")
        print(f"[PROMPT] {current_prompt}")

        # Define the signature dynamically based on the optimized prompt
        class TaskSignature(dspy.Signature):
            """Complete the task."""
            question: str = dspy.InputField(desc="The task or question to solve")
            answer: str = dspy.OutputField(desc="The final answer")

        # Set instructions (the prompt) as the signature docstring for ReAct
        TaskSignature.__doc__ = f"{current_prompt}\n\nCRITICAL: You MUST use tools provided (like python_sandbox) for any calculation, counting, or factual verification. Output tool calls in the required format."

        # Convert LangChain tools to DSPy format
        dspy_tools = [self._wrap_langchain_tool(tool) for tool in self.tools]

        # Create DSPy ReAct agent instance
        react_agent = dspy.ReAct(
            signature=TaskSignature,
            tools=dspy_tools,
            max_iters=6
        )

        # Clear LM history before running to capture only this run's trace
        self.dspy_lm.history = []

        try:
            # DSPy ReAct is synchronous. LangGraph handles running this sync code in an executor.
            result = react_agent(question=task)
            answer = result.answer

            # Capture the execution trace from the LM history
            trace_info = self._capture_react_trace()
            
            print(f"[AGENT RESPONSE]\n{answer}")

        except Exception as e:
            answer = f"Error during execution: {str(e)}"
            trace_info = f"Execution failed: {str(e)}"
            print(f"\n[ERROR] Agent failed: {str(e)}")

        conversation_entry = {
            "task": task,
            "prompt": current_prompt,
            "response": answer,
            "trace": trace_info,
        }

        conversation = state.get("conversation", [])
        conversation.append(conversation_entry)

        return {"conversation": conversation, "hypothesis_status": None}

    def _capture_react_trace(self) -> str:
        """Extracts the thought process and tool interactions from the DSPy LM history."""
        trace = []
        for i, interaction in enumerate(self.dspy_lm.history):
            prompt = interaction.get('prompt', '')
            response = interaction.get('response', '')
            
            # Simplified trace extraction focusing on ReAct steps
            if 'Thought:' in response or 'Action:' in response or 'Observation:' in prompt:
                 trace.append(f"--- Step {i+1} ---\nPrompt Context (Observation/Instruction):\n{prompt}\n\nAgent Output (Thought/Action/Final Answer):\n{response}\n")
        
        return "\n".join(trace) if trace else "No detailed trace available."


    @vero_agent("Evaluating Performance")
    async def evaluate_score(self, state: OptimizerState) -> Dict[str, Any]:
        """Evaluate performance against the ground truth requirements."""
        conversation = state["conversation"]
        latest_conv = conversation[-1]
        eval_parameters = state["eval_parameters"]

        # Use DSPy evaluator
        eval_result = self.evaluator(
            task=state["task"],
            agent_response=latest_conv["response"],
            agent_trace=latest_conv["trace"],
            ground_truth_requirements=eval_parameters
        )

        try:
            score = int(eval_result.score)
        except ValueError:
            score = 0
            
        reasoning = eval_result.discrepancy_analysis

        print(f"\n[EVALUATION SCORE] {score}/100")
        print(f"[DISCREPANCY ANALYSIS]\n{reasoning}")

        # Update state
        latest_conv["evaluation"] = {"score": score, "reasoning": reasoning}
        scores = state["scores"] + [score]
        attempts = state["attempts"] + 1

        return {
            "conversation": conversation,
            "scores": scores,
            "attempts": attempts,
            "feedback": reasoning
        }

    @vero_agent("Generating Hypothesis")
    async def generate_hypothesis(self, state: OptimizerState) -> Dict[str, Any]:
        """Generate a hypothesis about an unknown rule based on the discrepancy analysis."""
        latest_score = state["scores"][-1]
        
        if latest_score >= state["score_threshold"]:
            return {"current_hypothesis": "", "hypothesis_status": "validated"} # Treat success as a validated state

        latest_conv = state["conversation"][-1]
        feedback = state["feedback"]

        # Generate hypothesis
        hypothesis_result = self.hypothesis_generator(
            task=state["task"],
            agent_output=latest_conv["response"],
            discrepancy_analysis=feedback
        )

        hypothesis = hypothesis_result.proposed_rule
        print(f"\n[GENERATED HYPOTHESIS]\n{hypothesis}")

        return {
            "current_hypothesis": hypothesis,
            "hypothesis_status": "pending"
        }

    @vero_agent("Testing Hypothesis")
    async def test_hypothesis(self, state: OptimizerState) -> Dict[str, Any]:
        """Test if the proposed hypothesis actually explains the discrepancy."""
        current_hypothesis = state["current_hypothesis"]
        if not current_hypothesis:
            return {"hypothesis_status": "validated"} # No hypothesis needed if already optimized

        latest_conv = state["conversation"][-1]
        agent_trace = latest_conv.get("trace", "No trace available")
        feedback = state["feedback"]

        print(f"\n[TESTING HYPOTHESIS] Rule: {current_hypothesis}")

        # Test the hypothesis against the trace and feedback
        test_result = self.hypothesis_tester(
            task=state["task"],
            proposed_rule=current_hypothesis,
            original_agent_trace=agent_trace,
            discrepancy_analysis=feedback
        )
        
        # DSPy boolean output fields sometimes return strings
        test_passed = str(test_result.test_passed).strip().lower() in ['true', 'yes']

        hypotheses_tested = state["hypotheses_tested"]
        hypotheses_tested.append({
            "hypothesis": current_hypothesis,
            "passed": test_passed,
            "explanation": test_result.explanation,
            "score_at_time": state["scores"][-1]
        })

        if test_passed:
            print(f"[HYPOTHESIS RESULT] VALIDATED: {test_result.explanation}")
            # Add to discovered rules if validated
            discovered_rules = state["discovered_rules"] + [current_hypothesis]
            return {
                "hypothesis_status": "validated", 
                "discovered_rules": discovered_rules,
                "hypotheses_tested": hypotheses_tested
            }
        else:
            print(f"[HYPOTHESIS RESULT] REJECTED: {test_result.explanation}")
            return {
                "hypothesis_status": "rejected",
                "hypotheses_tested": hypotheses_tested
            }


    @vero_agent("Optimizing Prompt")
    async def optimize_prompt(self, state: OptimizerState) -> Dict[str, Any]:
        """Incorporate validated rules into a new prompt."""
        
        # Only run optimization if we have validated rules to add or modify
        if state["hypothesis_status"] != "validated":
             # Should ideally not be called if status isn't validated, but handle gracefully
            return {}

        prompts = state["prompts"]
        current_prompt = prompts[-1]
        discovered_rules = state["discovered_rules"]

        # Format discovered rules
        rules_text = "\n".join([f"- {rule}" for rule in discovered_rules]) if discovered_rules else "No specific rules discovered yet."

        # Use DSPy to generate improved prompt
        optimization_result = self.optimizer(
            current_prompt=current_prompt,
            validated_rules=rules_text
        )

        new_prompt = optimization_result.improved_prompt.strip()
        
        # Ensure the new prompt is different and not excessively long
        if new_prompt == current_prompt or len(new_prompt) > 2000:
            print("[OPTIMIZATION] New prompt is identical or too long. Skipping update.")
            return {}

        prompts.append(new_prompt)

        print(f"\n[NEW OPTIMIZED PROMPT]")
        print(new_prompt)

        return {"prompts": prompts}

    # --- Graph Control Flow ---

    def should_continue_optimization(self, state: OptimizerState) -> str:
        """Determine if optimization should continue, end, or proceed to hypothesis generation."""
        latest_score = state["scores"][-1] if state["scores"] else 0

        # Condition 1: Success
        if latest_score >= state["score_threshold"]:
            print(f"\n[DECISION] Threshold met ({latest_score}/{state['score_threshold']}). ENDING.")
            return "end"

        # Condition 2: Max attempts reached
        if state["attempts"] >= state["max_attempts"]:
            print(f"\n[DECISION] Max attempts reached ({state['attempts']}/{state['max_attempts']}). ENDING.")
            return "end"

        # Condition 3: Needs improvement -> Generate hypothesis
        print(f"\n[DECISION] Score {latest_score} below threshold. Proceeding to GENERATE HYPOTHESIS.")
        return "generate_hypothesis"

    def analyze_hypothesis_test(self, state: OptimizerState) -> str:
        """Decide what to do after testing a hypothesis."""
        status = state.get("hypothesis_status")

        if status == "validated":
            # Rule is good, incorporate it into the prompt
            print("\n[DECISION] Hypothesis validated. Proceeding to OPTIMIZE prompt.")
            return "optimize"
        
        elif status == "rejected":
            # The hypothesis was wrong. We failed to find the rule this round.
            
            # Check if we still have attempts left to try again
            if state["attempts"] < state["max_attempts"]:
                # We could try generating a different hypothesis, but for simplicity, 
                # we end this attempt cycle and retry the agent run (maybe with stochastic variation or hoping for better hypothesis next time)
                print("\n[DECISION] Hypothesis rejected. Retrying AGENT RUN if attempts remain.")
                return "run_agent"
            else:
                print("\n[DECISION] Hypothesis rejected. Max attempts reached. ENDING.")
                return "end"
        
        # Should not happen, default to end
        return "end"


    def create_graph(self) -> StateGraph:
        """Create the optimizer graph with hypothesis generation and testing."""
        builder = StateGraph(self.OptimizerState)

        # Nodes
        builder.add_node("run_agent", self.run_react_agent)
        builder.add_node("evaluate", self.evaluate_score)
        builder.add_node("generate_hypothesis", self.generate_hypothesis)
        builder.add_node("test_hypothesis", self.test_hypothesis)
        builder.add_node("optimize", self.optimize_prompt)

        # Edges
        builder.add_edge(START, "run_agent")
        builder.add_edge("run_agent", "evaluate")
        
        # Conditional Edge: After evaluation, decide whether to stop or generate a hypothesis
        builder.add_conditional_edges(
            "evaluate",
            self.should_continue_optimization,
            {
                "generate_hypothesis": "generate_hypothesis",
                "end": END
            }
        )
        
        builder.add_edge("generate_hypothesis", "test_hypothesis")

        # Conditional Edge: After testing hypothesis, decide whether to optimize, retry, or end
        builder.add_conditional_edges(
            "test_hypothesis",
            self.analyze_hypothesis_test,
            {
                "optimize": "optimize",
                "run_agent": "run_agent", # If rejected but attempts remain
                "end": END
            }
        )

        # Optimization loops back to running the agent with the new prompt
        builder.add_edge("optimize", "run_agent")

        return builder.compile(checkpointer=self.checkpointer)

    # --- Utility Methods ---

    async def tune(self, initial_prompt: str, task: str, eval_parameters: str, config: Dict[str, Any] = None, score_threshold: int = 100, max_attempts: int = 4) -> Dict[str, Any]:
        """Tune the agent's prompt for optimal task performance."""
        if self._graph is None:
            self._graph = self.create_graph()

        if config is None:
            config = {"configurable": {"thread_id": "dspy-vero-optimizer"}, "recursion_limit": 50}

        initial_state: self.OptimizerState = {
            "prompts": [initial_prompt],
            "task": task,
            "conversation": [],
            "scores": [],
            "feedback": "",
            "optimized": False,
            "score_threshold": score_threshold,
            "max_attempts": max_attempts,
            "attempts": 0,
            "eval_parameters": eval_parameters,
            "discovered_rules": [],
            "hypotheses_tested": [],
            "current_hypothesis": "",
            "hypothesis_status": None
        }

        # Use astream_log or ainvoke for execution
        return await self._graph.ainvoke(initial_state, config)
    
    def _wrap_langchain_tool(self, lc_tool):
        """Convert LangChain tool to a simple function for DSPy ReAct"""
        # DSPy ReAct expects tools to take string input and return string output
        def wrapped_func(query: str) -> str:
            # Assuming the LangChain tool accepts a single string argument
            return lc_tool.run(query)
        
        # DSPy uses name and docstring for tool selection
        wrapped_func.__name__ = lc_tool.name
        wrapped_func.__doc__ = f"{lc_tool.name}: {lc_tool.description}"
        return dspy.Tool(wrapped_func)


# --- Execution Example ---

async def main():
    """Test DspyVeroAgent with a specific 'universe' rule."""

    # 1. Define the Tool (LangChain format)
    @tool
    def python_sandbox(code: str) -> str:
        """Execute Python code to calculate or count. Input must be valid Python assigning the final output to a variable named 'result'."""
        print(f"\n[SANDBOX EXECUTING]:\n{code}\n")
        try:
            # Restrict the environment for safety if necessary, but for counting/math this is simple
            namespace = {}
            exec(code, namespace)
            
            if 'result' in namespace:
                output = str(namespace['result'])
                print(f"[SANDBOX RESULT]: {output}")
                return output
            else:
                return "Error: Code executed, but no variable named 'result' was found."
        except Exception as e:
            return f"Error executing Python code: {str(e)}"

    # 2. Setup Agent
    dspy_lm = await get_dspy_llm()
    
    # We use a dummy checkpointer for this example if Mongo isn't set up
    checkpointer = None # VeroMongoDBSaver() 

    agent = DspyVeroAgent(
        dspy_lm=dspy_lm,
        tools=[python_sandbox],
        checkpointer=checkpointer
    )

    # 3. Define the Task and the "Hidden Ground Truth" (The Universe Rules)
    
    TASK = "How many words are in this exact sentence: 'The quick brown fox jumps over the lazy dog in 2025'?"
    
    # The hidden rule of this universe: Numbers do not count as words. The answer should be 9.
    GROUND_TRUTH_EVAL = """
    1. Correctness: The final answer MUST be exactly '9'.
    2. Tool Usage: Must use the `python_sandbox` tool to perform the counting.
    3. Rule Adherence: The agent must follow the rules of this universe, where numeric characters (like '2025') are NOT counted as words.
    """
    
    INITIAL_PROMPT = "You are a helpful assistant. Always use the python_sandbox tool to verify counts and calculations."

    # 4. Run Optimization
    print(f"Starting optimization for task:\n{TASK}\n")
    
    result = await agent.tune(
        initial_prompt=INITIAL_PROMPT,
        task=TASK,
        eval_parameters=GROUND_TRUTH_EVAL,
        score_threshold=100,
        max_attempts=4 # Give it 4 attempts to discover the rule
    )

    # 5. Display Results
    print(f"\n\n{'='*20} OPTIMIZATION FINISHED {'='*20}\n")
    
    scores = result.get('scores', [])
    prompts = result.get('prompts', [])
    discovered_rules = result.get('discovered_rules', [])

    if scores:
        best_idx = scores.index(max(scores))
        # The best prompt might be the one used in the best run (index best_idx)
        best_prompt = prompts[best_idx] 
        best_score = scores[best_idx]
    else:
        best_prompt = prompts[-1] if prompts else "N/A"
        best_score = 0

    print(f"All Scores: {scores}")
    print(f"Discovered Rules: {discovered_rules}")
    print(f"\nBest Score: {best_score}/100")
    print(f"\n[BEST PROMPT]:\n{best_prompt}")
    
    print("\n--- Final Conversation Trace (Best Attempt) ---")
    if result.get('conversation'):
        best_conv = result['conversation'][best_idx]
        print(f"Prompt Used: {best_conv['prompt']}")
        print(f"Agent Trace:\n{best_conv['trace']}")
        print(f"Final Answer: {best_conv['response']}")
        print(f"Evaluation: {best_conv.get('evaluation', {}).get('reasoning')}")


if __name__ == "__main__":
    # Ensure required environment variables (like OPENAI_API_KEY) are set before running
    if not os.environ.get("OPENAI_API_KEY"):
        print("ERROR: OPENAI_API_KEY environment variable not set.")
        sys.exit(1)
        
    asyncio.run(main())