from langgraph.graph import StateGraph, START, END
from software.ai.graph.director_state import DirectorState, vero_agent, AnalystState, console
from langgraph.prebuilt import create_react_agent
from software.ai.llm.llm_connect import get_llm_connect
from typing import Dict, Any, List, Annotated
import uuid
import os
import json
import datetime
import subprocess
import tempfile
from langchain_core.messages import (
    SystemMessage,
    HumanMessage,
    BaseMessage,
    AIMessage,
    ToolMessage
)
from langchain_core.tools import tool
from software.db.report_repository import ReportRepository
import asyncio

# Global variable for template location
REPORT_TEMPLATE_PATH = "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/Vero/software/library/templates/research_report_template.html"


@tool
async def ReadFilesJSON(folder_name: str) -> str:
    """Read the files.json status manager to see which files are ready to process"""
    console.print(f"[cyan]Reading files.json from: {folder_name}[/cyan]")
    
    try:
        base_path = "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/Vero/software/library/research"
        json_path = os.path.join(base_path, folder_name, "files.json")
        
        if not os.path.exists(json_path):
            return f"files.json not found in {folder_name}"
        
        def read_json():
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        data = await asyncio.to_thread(read_json)
        
        # Extract files array from the JSON structure
        files_data = data.get('files', []) if isinstance(data, dict) else data
        
        # Format output with clear status information
        output = ["Files Status Manager:"]
        output.append("-" * 50)
        
        for file_info in files_data:
            filename = file_info.get('filename', 'unknown')
            status = file_info.get('status', 'unknown')
            datetime_str = file_info.get('datetime', 'unknown')
            output.append(f"📄 {filename}")
            output.append(f"   Status: {status}")
            output.append(f"   Created: {datetime_str}")
            output.append("")
        
        return "\n".join(output)
        
    except Exception as e:
        error_msg = f"Error reading files.json: {str(e)}"
        console.print(f"[red]{error_msg}[/red]")
        return error_msg


@tool
async def UpdateFileStatus(folder_name: str, filename: str, new_status: str) -> str:
    """Update the status of a file in files.json. Valid statuses: ready, in-progress, completed-used, completed-unused, failed"""
    console.print(f"[cyan]Updating status for {filename} to: {new_status}[/cyan]")
    
    valid_statuses = ["ready", "in-progress", "completed-used", "completed-unused", "failed"]
    if new_status not in valid_statuses:
        return f"Error: Invalid status '{new_status}'. Valid statuses: {', '.join(valid_statuses)}"
    
    try:
        base_path = "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/Vero/software/library/research"
        json_path = os.path.join(base_path, folder_name, "files.json")
        
        if not os.path.exists(json_path):
            return f"files.json not found in {folder_name}"
        
        # Read current data
        def read_json():
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        data = await asyncio.to_thread(read_json)
        
        # Extract files array from the JSON structure
        files_data = data.get('files', []) if isinstance(data, dict) else []
        
        # Update status for the specific file
        file_found = False
        for file_info in files_data:
            if file_info.get('filename') == filename:
                file_info['status'] = new_status
                file_found = True
                break
        
        if not file_found:
            return f"File '{filename}' not found in files.json"
        
        # Write back to file with the correct structure
        def write_json():
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump({'files': files_data}, f, indent=2)
        
        await asyncio.to_thread(write_json)
        
        console.print(f"[green]Successfully updated {filename} status to: {new_status}[/green]")
        return f"Status updated: {filename} -> {new_status}"
        
    except Exception as e:
        error_msg = f"Error updating file status: {str(e)}"
        console.print(f"[red]{error_msg}[/red]")
        return error_msg


@tool
async def CopyTemplateSchema(research_folder: str) -> str:
    """Copy the HTML report template to the research folder"""
    console.print(f"[cyan]Copying report template to: {research_folder}[/cyan]")
    
    try:
        import asyncio
        
        # Define paths
        base_path = "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/Vero/software/library/research"
        target_folder = os.path.join(base_path, research_folder)
        target_file = os.path.join(target_folder, "report.html")
        
        # Check if target file already exists
        if os.path.exists(target_file):
            return f"Error: report.html already exists in {research_folder}. Will not overwrite."
        
        # Ensure target folder exists
        await asyncio.to_thread(os.makedirs, target_folder, exist_ok=True)
        
        # Copy template file
        proc = await asyncio.create_subprocess_exec(
            "cp", REPORT_TEMPLATE_PATH, target_file,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await proc.communicate()
        
        if proc.returncode == 0:
            console.print(f"[green]Successfully copied template to: {target_file}[/green]")
            return f"Template copied successfully to: {research_folder}/report.html"
        else:
            error_msg = stderr.decode('utf-8')
            console.print(f"[red]Failed to copy template: {error_msg}[/red]")
            return f"Error copying template: {error_msg}"
            
    except Exception as e:
        error_msg = f"Error in CopyTemplateSchema: {str(e)}"
        console.print(f"[red]{error_msg}[/red]")
        return error_msg


@tool
async def EditingSummarizedResults(folder_name: str, filename: str, start_line: int, end_line: int, old_code: str, new_code: str) -> str:
    """Edit the Summarized Results section - can replace existing content or inject new result items. If more than 3 findings exist, add additional result-item divs"""
    console.print(f"[cyan]Replacing code in {folder_name}/{filename} lines {start_line}-{end_line}[/cyan]")
    
    try:
        import asyncio
        
        # Define file path
        base_path = "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/Vero/software/library/research"
        file_path = os.path.join(base_path, folder_name, filename)
        
        if not os.path.exists(file_path):
            return f"Error: File not found: {filename}"
        
        # Read the file
        def read_file():
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.readlines()
        
        lines = await asyncio.to_thread(read_file)
        total_lines = len(lines)
        
        # Validate line range
        if start_line < 1 or end_line > total_lines:
            return f"Error: Invalid line range. File has {total_lines} lines."
        
        if start_line > end_line:
            return f"Error: start_line ({start_line}) must be <= end_line ({end_line})"
        
        # Extract the specified range (convert to 0-based indexing)
        range_content = ''.join(lines[start_line-1:end_line])
        
        # Verify old code matches
        if range_content.strip() != old_code.strip():
            return f"Error: Old code does not match the content at lines {start_line}-{end_line}.\nExpected:\n{old_code}\n\nActual:\n{range_content}"
        
        # Replace the content
        new_lines = lines[:start_line-1]
        new_lines.append(new_code)
        if not new_code.endswith('\n'):
            new_lines.append('\n')
        new_lines.extend(lines[end_line:])
        
        # Write back to file
        def write_file():
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
        
        await asyncio.to_thread(write_file)
        
        console.print(f"[green]Successfully edited Summarized Results section at lines {start_line}-{end_line}[/green]")
        return f"Summarized Results section updated successfully in {filename}"
        
    except Exception as e:
        error_msg = f"Error in EditingSummarizedResults: {str(e)}"
        console.print(f"[red]{error_msg}[/red]")
        return error_msg


@tool
def ScanResearchDirectory(folder_name: str) -> str:
    """List all files and folders in research directory"""
    console.print(f"Scanning research directory: {folder_name}")
    base_path = "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/Vero/software/library/research"
    folder_path = os.path.join(base_path, folder_name)
    
    if not os.path.exists(folder_path):
        return f"Folder not found: {folder_name}"
    
    items = []
    for item in sorted(os.listdir(folder_path)):
        item_path = os.path.join(folder_path, item)
        if os.path.isdir(item_path):
            items.append(f"📁 {item}/")
        else:
            size = os.path.getsize(item_path)
            ext = os.path.splitext(item)[1]
            items.append(f"📄 {item} ({size} bytes){ext}")
    
    return f"Directory structure of {folder_name}:\n" + "\n".join(items)


@tool
def ReadFile(folder_name: str, filename: str) -> str:
    """Read content from any text file"""
    console.print(f"Reading file: {folder_name}/{filename}")
    base_path = "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/Vero/software/library/research"
    file_path = os.path.join(base_path, folder_name, filename)
    
    if not os.path.exists(file_path):
        return f"File not found: {filename}"
    
    # Check file size
    MAX_CHARS = 10000  # Small threshold for testing
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        return f"Error reading file: {str(e)}"
    
    # Add line numbers to each line
    numbered_lines = []
    for i, line in enumerate(lines, 1):
        # Format: "   123→content" similar to cat -n output
        numbered_lines.append(f"{i:6d}→{line}")
    
    content_with_numbers = ''.join(numbered_lines)
    
    if len(content_with_numbers) > MAX_CHARS:
        line_count = len(lines)
        return f"File too large: {len(content_with_numbers)} characters, {line_count} lines (max: {MAX_CHARS} chars)"
    
    return content_with_numbers


@tool
def ReadFileChunk(folder_name: str, filename: str, start_line: int = 1, end_line: int = 50) -> str:
    """Read specific lines from any text file"""
    console.print(f"Reading file chunk: {folder_name}/{filename} from line {start_line} to line {end_line}")
    base_path = "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/Vero/software/library/research"
    file_path = os.path.join(base_path, folder_name, filename)
    
    if not os.path.exists(file_path):
        return f"File not found: {filename}"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        return f"Error reading file: {str(e)}"
    
    total_lines = len(lines)
    actual_end = min(end_line, total_lines)
    
    if start_line > total_lines:
        return f"Start line {start_line} exceeds file length ({total_lines} lines)"
    
    chunk = lines[start_line-1:actual_end]
    # Add line numbers to each line in the chunk
    numbered_chunk = []
    for i, line in enumerate(chunk, start_line):
        numbered_chunk.append(f"{i:6d}→{line}")
    
    return f"Lines {start_line}-{actual_end} of {total_lines}:\n" + "".join(numbered_chunk)


@tool
def SearchInFile(folder_name: str, filename: str, keywords: List[str], start_line: int = 1) -> str:
    """Search keywords in any text file and return line numbers"""
    console.print(f"Searching in file: {folder_name}/{filename} for keywords: {keywords}")
    base_path = "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/Vero/software/library/research"
    file_path = os.path.join(base_path, folder_name, filename)
    
    if not os.path.exists(file_path):
        return f"File not found: {filename}"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        return f"Error reading file: {str(e)}"
    
    results = []
    for keyword in keywords:
        matches = []
        total_count = 0
        
        for i, line in enumerate(lines[start_line-1:], start=start_line):
            if keyword.lower() in line.lower():
                total_count += 1
                if len(matches) < 5:
                    preview = line.strip()[:50] + "..." if len(line.strip()) > 50 else line.strip()
                    matches.append(f"  Line {i}: {preview}")
        
        if matches:
            results.append(f"'{keyword}' found {total_count} times:")
            results.extend(matches)
            if total_count > 5:
                results.append(f"  ... and {total_count - 5} more occurrences")
        else:
            results.append(f"'{keyword}' not found")
    
    return "\n".join(results)


@vero_agent("Initializing")
async def initialize_documentor(state: AnalystState) -> Dict[str, Any]:
    """Initialize the documentor with mock analyst state"""
    
    # Mock analyst information if not provided
    mock_state = {
        "name_analyst": getattr(state, 'name_analyst', 'Elena Rodriguez'),
        "age_analyst": getattr(state, 'age_analyst', 34),
        "title_analyst": getattr(state, 'title_analyst', 'Senior Financial Analyst'),
        "background_analyst": getattr(state, 'background_analyst', 
            'MBA from Wharton, CFA Level III. 10 years experience in equity research with focus on tech sector analysis.'),
        "business_question": getattr(state, 'business_question', 
            'Should we invest in Apple (AAPL) in the next 30 days?'),
        "thread_id": getattr(state, 'thread_id', str(uuid.uuid4())),
        "director_id": getattr(state, 'director_id', str(uuid.uuid4())),
        "operations": getattr(state, 'operations', 3),
        "max_operations": getattr(state, 'max_operations', 7),
        "messages": [],
        "tool_logs": getattr(state, 'tool_logs', [
            [
                {
                    "tool_name": "SimpleWorkflowTool",
                    "status": "success",
                    "tool_output_content": "AAPL Analysis: Current price $150.25, RSI 45, Moving Average $148.50"
                },
                {
                    "tool_name": "TodayDateTool",
                    "status": "success", 
                    "tool_output_content": "Today's date is 2025-01-05"
                }
            ]
        ])
    }
    
    print(f"Initialized Documentor for: {mock_state['name_analyst']}")
    print(f"Research Question: {mock_state['business_question']}")
    print(f"Iteration: {mock_state['operations']}/{mock_state['max_operations']}")
    
    return mock_state




@vero_agent("Documenting")
async def deep_documentor(state: AnalystState) -> Dict[str, Any]:
    """Documentor agent that creates HTML report from research outputs"""
    
    # Get analyst info for folder path
    analyst_name = getattr(state, 'name_analyst', 'Unknown')
    thread_id = getattr(state, 'thread_id', None)
    if thread_id is None:
        thread_id = str(uuid.uuid4())
    operations = getattr(state, 'operations', 0)

    
    # Get LLM for documentor
    director_id = getattr(state, 'director_id', '')
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "documentor")
    
    # Create simple documentor agent
    documentor = create_react_agent(
        model=model,
        tools=[CopyTemplateSchema, ReadFilesJSON, UpdateFileStatus, ScanResearchDirectory, ReadFile, ReadFileChunk, SearchInFile, EditingSummarizedResults],
        name="documentor",
        prompt=f"""You are a Document Librarian creating HTML reports from research files. Each filename represents a step in the AI's analysis workflow.

<critical_rules>
1. NEVER process multiple files in parallel - always ONE file at a time
2. ALWAYS verify each edit succeeded before moving to the next
3. NEVER mark a file "completed" until ALL its edits are verified
4. If ANY edit fails, STOP and report the failure
</critical_rules>

<sequential_workflow>
PHASE 1 - INITIALIZATION:
1. Copy template using CopyTemplateSchema
2. Read entire report.html with ReadFile
3. Read files.json and count EXACTLY how many files have status="ready"
4. Store this count - this is how many tasks you MUST have at the end

PHASE 2 - PROCESS FILE 1:
1. Find first file with status="ready"
2. Update ONLY that file to "in-progress" 
3. Read the ENTIRE file content
4. Understand what this tool did (e.g., TodayDateTool → extracted date)
5. Make ALL edits for this file:
   a) Update header if ticker/date/recommendation found
   b) Update business question if found
   c) Update Task 1 with: "filename.md - what this tool accomplished"
   d) Add relevant findings to results
6. After EACH edit, verify it worked by reading those lines
7. ONLY after ALL edits verified: update file to "completed-used"

PHASE 3 - PROCESS FILE 2:
1. Find second file with status="ready"
2. Repeat EXACT same process as Phase 2
3. Update Task 2 (not Task 1 again!)

PHASE 4 - CLEANUP:
1. Count how many tasks exist in HTML
2. If more tasks than files processed:
   - DELETE extra task divs completely
   - Verify deletion by reading those lines
3. Final scan for ANY remaining placeholders

PHASE 5 - FINAL VERIFICATION:
Read the ENTIRE report.html and verify:
- NO text containing "[TICKER]", "[DATE]", "[Description", etc.
- Task count = File count EXACTLY
- All placeholders replaced with real data
</sequential_workflow>

<editing_process>
For EVERY single edit:
1. First: ReadFileChunk to see EXACT current content
2. Copy that content EXACTLY as your old_code
3. Call EditingSummarizedResults with exact old_code
4. Immediately verify: ReadFileChunk same lines again
5. If content unchanged, the edit FAILED - try again or STOP

NEVER assume an edit worked without verification!
</editing_process>

<understanding_filenames>
Filenames tell the story of the analysis workflow:
- TodayDateTool_[timestamp].md → Sets current date context
- SimpleWorkflowTool_[timestamp].md → Main analysis with predictions
- ExtractWorkflowTool_[timestamp].md → Data extraction results
- Each represents a logical step in answering the business question
</understanding_filenames>

<task_updates>
Template tasks look like:
<div class="task-item">
    <input type="checkbox">
    <strong>Task N:</strong> [Description of Nth task completed]
</div>

Replace with:
<div class="task-item">
    <input type="checkbox" checked>
    <strong>Task N:</strong> ActualFilename.md - What this tool/step accomplished
</div>

Example replacements:
- Task 1: TodayDateTool_20250705_212011_75018213.md - Established analysis date: 2025-07-05
- Task 2: SimpleWorkflowTool_20250705_212011_75b920a3.md - Analyzed NVDA with 6.25% upward prediction over 30 days
</task_updates>

<placeholder_reference>
These are ALL placeholders that MUST be replaced:
- [TICKER] → actual ticker like NVDA
- [BUY/SELL/HOLD] → actual recommendation 
- [buy/sell/hold] → lowercase for CSS
- [ANALYST NAME] → actual name
- [DATE] → actual date
- [INSERT MAIN BUSINESS QUESTION HERE] → actual question
- [Description of first task completed] → actual task description
- [Most relevant finding to business question] → actual finding
</placeholder_reference>

<verification_checklist>
After EVERY file:
□ Did you update status to "in-progress" BEFORE processing?
□ Did you make ALL edits for this file?
□ Did you verify EACH edit by re-reading?
□ Did you update status to "completed-used" AFTER all edits?

Final check:
□ Read entire HTML - ANY placeholders remaining?
□ Count tasks - does it match file count?
□ Are all files marked "completed-used"?
</verification_checklist>

REMEMBER: One file at a time. Verify every edit. No parallel processing. The edits must actually appear in the file, not just in the success message."""
    )
    
    # Prepare documentor task - use test folder for now
    research_folder = "Elena Rodriguez_6869cec33355"
    task = f"""Create an HTML report for research folder: {research_folder}

Execute the COMPLETE workflow as specified in your instructions. DO NOT stop to ask questions or seek confirmation. Complete ALL steps including:
- Reading the template after copying
- Managing file statuses properly
- Actually updating the HTML content
- Verifying your changes

You must produce a finished report with all placeholders replaced with actual data."""
    
    try:
        config = {"configurable": {"thread_id": f"doc_{thread_id}"}}
        response = await documentor.ainvoke(
            {"messages": [HumanMessage(content=task)]},
            config=config
        )

        # Extract the last message from the response
        if response and "messages" in response and response["messages"]:
            last_message = response["messages"][-1].content
            print(last_message)
        
        print(f"✓ Documentor completed for iteration {operations}")
    except Exception as e:
        print(f"Documentor error: {str(e)}")
    
    return {}


# Main graph implementation
def create_deep_documentor() -> StateGraph:
    """Create the state graph for Deep Documentor agent."""
    builder = StateGraph(AnalystState)
    
    # Add nodes
    builder.add_node("initialize", initialize_documentor)
    builder.add_node("documentor", deep_documentor)
    
    # Define edges
    builder.add_edge(START, "initialize")
    builder.add_edge("initialize", "documentor")
    builder.add_edge("documentor", END)
    
    return builder.compile()

graph = create_deep_documentor()

__all__ = ['graph']