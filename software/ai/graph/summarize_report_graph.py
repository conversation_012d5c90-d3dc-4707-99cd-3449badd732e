from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    SystemMessage,
    HumanMessage,
    RemoveMessage
)
from software.ai.graph.director_state import DirectorState, StructuredReport, print_step, print_debug, print_pretty, vero_agent, print_markdown
from software.ai.graph.input_memory_graph import graph as input_memory_graph
from software.ai.graph.output_memory_graph import graph as output_memory_graph
from software.ai.llm.llm_connect import get_llm_connect
from software.db.research_repository import ResearchRepository
from datetime import date
research_repo = ResearchRepository()

@vero_agent("Getting")
async def get_structured_report(state: DirectorState) -> DirectorState:
    """Generate a structured FinalReport using the LLM model"""
    
    # Get completed reports and take the last AI message (verdict)
    completed_reports = state.get("completed_reports", [])
    business_question = state.get("business_question", "Should we invest in Nvidia (NVDA) in the next 30 days?")
    
    # Get the last AI message which should be the verdict
    last_verdict = None
    for msg in reversed(completed_reports):
        if hasattr(msg, 'content') and isinstance(msg, type(msg)) and hasattr(type(msg), '__name__') and 'AIMessage' in type(msg).__name__:
            last_verdict = msg.content
            break
    
    if not last_verdict:
        print_debug("No verdict found in completed_reports", "Get Structured Report")
        last_verdict = "No verdict available"
        
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(state["director_id"], "get_structured_report")
    structured_model = model.with_structured_output(StructuredReport)

    # Get the schema directly from the model
    schema_json = StructuredReport.model_json_schema()

    # Structured prompt following Anthropic best practices
    system_message = SystemMessage(content=f"""<role>
You are a financial data extraction system that parses investment verdicts into structured data.
</role>

<task>
Extract structured investment data from the verdict text and return a StructuredReport JSON object.
</task>

<context>
The verdict contains labeled fields that must be extracted and validated.
The schema requires numeric values for all price fields - you cannot skip or omit them.
</context>

<extraction_instructions>
1. Locate these labeled fields in the verdict:
   - TICKER: Stock symbol
   - CURRENT PRICE: Price value or "Not available"
   - VERDICT: BUY/SELL/HOLD recommendation
   - CONFIDENCE: Percentage value
   - PRICE TARGET: Target price value
   - TIME HORIZON: Number of days

2. Transform into StructuredReport fields:
   - ticker: String from TICKER line
   - last_closing_price: Float from CURRENT PRICE (use -1.0 if "Not available")
   - closing_price_date: Today's date {date.today()}
   - forecasted_price: Float from PRICE TARGET
   - forecasted_horizon: Add TIME HORIZON days to today
   - recommendation: Exactly "BUY", "SELL", or "HOLD"
   - confidence_level: Integer 0-100
   - report_is_valid: Boolean based on data completeness
   - report_is_valid_reasoning: String explaining validation
</extraction_instructions>

<error_handling>
IMPORTANT: The schema requires numeric values for all fields.
- If CURRENT PRICE says "Not available" or similar: Use -1.0
- If any price cannot be parsed: Use -1.0
- Never skip fields or return null/undefined
- Always provide all fields with valid types
</error_handling>

<validation_logic>
Set report_is_valid to false when:
- last_closing_price is -1.0 (missing data)
- Any required field has invalid/missing data
- Prices are unrealistic or negative

Set report_is_valid to true when:
- All fields contain valid, realistic values
- Prices are positive numbers
- All data makes financial sense
</validation_logic>

<output_format>
Return a complete StructuredReport JSON matching this schema:
{schema_json}
</output_format>""")

    human_message = HumanMessage(content=f"""Extract structured data from this investment verdict:

BUSINESS QUESTION: {business_question}

VERDICT:
{last_verdict}""")
    
    try:
        response = await structured_model.ainvoke([system_message, human_message])
        print("Successfully extracted structured report")
        return {"structured_report": response}
    except Exception as e:
        print_debug(f"Error in get_structured_report: {str(e)}", "Get Structured Report")
        raise e

@vero_agent("Scheduling")
async def schedule_revisit(state: DirectorState) -> DirectorState:
    """Let's schedule a revisit to check if our prediction will come a reality"""
    
    structured_report = state.get("structured_report")
    if not structured_report:
        print("No structured report found")
        raise ValueError("No structured report found")
    
    # Check if the report is valid (already validated in get_structured_report)
    if structured_report.report_is_valid:
        print("✓ Report is valid, let's schedule a revisit")
        print(f"The reasoning for the report to be valid is: {structured_report.report_is_valid_reasoning}")
        
        forecast_data = {
            "ticker": structured_report.ticker,
            "forecasted_price": structured_report.forecasted_price,
            "forecasted_horizon": structured_report.forecasted_horizon.isoformat(),
            "last_closing_price": structured_report.last_closing_price,
            "closing_price_date": structured_report.closing_price_date.isoformat(),
            "recommendation": structured_report.recommendation,
            "confidence_level": structured_report.confidence_level
        }
        
        revisit_id = await research_repo.schedule_forecast_revisit(state["thread_id"], forecast_data)
        print(f"✓ Successfully scheduled forecast revisit with ID: {revisit_id}")
        return {"schedule_status": "success", "revisit_id": revisit_id}
    else:
        print(f"Cannot schedule revisit - report validation failed: {structured_report.report_is_valid_reasoning}")
        raise ValueError(f"Invalid report - cannot proceed: {structured_report.report_is_valid_reasoning}")


@vero_agent("Crafting")
async def generate_professional_report(state: DirectorState) -> DirectorState:
    """Generate professional hedge fund-grade report using LLM"""
    print("Generating professional report")
    
    # Get required state data
    structured_report = state.get("structured_report")
    if not structured_report:
        print("No structured report found")
        raise ValueError("No structured report found")
    
    fr = StructuredReport.model_validate(dict(structured_report))
    business_question = state.get("business_question", "")
    completed_reports = state.get("completed_reports", [])
    director_id = state.get("director_id", "")
    director_profile = state.get("director_profile", {})
    tool_logs = state.get("tool_logs", [])
    
    # Calculate price movement for context
    price_change = fr.forecasted_price - fr.last_closing_price
    # Handle division by zero or negative prices
    if fr.last_closing_price > 0:
        price_change_pct = (price_change / fr.last_closing_price) * 100
    else:
        price_change_pct = 0.0
    
    # Get LLM for professional report generation
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "generate_professional_report")
    
    # Extract director details
    director_name = director_profile.get("name", "Chief Investment Officer")
    director_title = director_profile.get("title", "Senior Investment Director")
    director_experience = director_profile.get("experience_years", 10)
    director_expertise = director_profile.get("expertise", ["general market analysis"])
    director_style = director_profile.get("analysis_style", "comprehensive fundamental and technical analysis")
    director_personality = director_profile.get("personality", "professional and analytical")
    director_background = director_profile.get("background", "extensive experience in financial markets")
    
    
    # Process tool logs to create research methodology summary
    tool_usage_summary = ""
    if tool_logs:
        tool_stats = {}
        total_tools_used = 0
        analyst_count = 0
        
        # Handle list of analyst dictionaries (clean format)
        if isinstance(tool_logs, list):
            analyst_count = len(tool_logs)
            
            for analyst_data in tool_logs:
                if isinstance(analyst_data, dict) and 'analyst_name' in analyst_data:
                    # Process clean format
                    analyst_name = analyst_data.get("analyst_name", "Unknown")
                    iterations = analyst_data.get("iterations", [])
                    
                    for iteration in iterations:
                        for tool in iteration.get("tools", []):
                            if isinstance(tool, dict):
                                tool_name = tool.get("tool_name", "unknown")
                                status = tool.get("status", "unknown")
                                
                                if tool_name not in tool_stats:
                                    tool_stats[tool_name] = {"success": 0, "error": 0, "total": 0, "analysts_used": set()}
                                
                                tool_stats[tool_name]["total"] += 1
                                tool_stats[tool_name]["analysts_used"].add(analyst_name)
                                total_tools_used += 1
                                
                                if status != "error":
                                    tool_stats[tool_name]["success"] += 1
                                else:
                                    tool_stats[tool_name]["error"] += 1
        
        # Create tool usage summary
        if tool_stats:
            tool_usage_summary = f"\n\nRESEARCH METHODOLOGY:\n"
            tool_usage_summary += f"- Total research operations: {total_tools_used}\n"
            tool_usage_summary += f"- Jury analysts deployed: {analyst_count}\n"
            tool_usage_summary += f"- Unique tools utilized: {len(tool_stats)}\n"
            tool_usage_summary += f"- Tool effectiveness breakdown:\n"
            
            for tool_name, stats in sorted(tool_stats.items(), key=lambda x: x[1]["total"], reverse=True):
                success_rate = (stats["success"] / stats["total"]) * 100 if stats["total"] > 0 else 0
                analysts_used_count = len(stats["analysts_used"])
                tool_usage_summary += f"  • {tool_name}: {stats['total']} uses across {analysts_used_count} analyst{'s' if analysts_used_count > 1 else ''} ({success_rate:.0f}% success rate)\n"
    else:
        print("No tool logs available or empty tool logs")
        raise ValueError("No tool logs available or empty tool logs")
    
    print(tool_usage_summary)
    
    # System message with structured data injected
    system_message = SystemMessage(content=f"""You are {director_name}, {director_title} with {director_experience} years of experience, writing a professional hedge fund research report.

Your Investment Profile:
- Expertise: {', '.join(director_expertise)}
- Analysis Style: {director_style}
- Background: {director_background}
- Approach: {director_personality}

Create a comprehensive investment analysis report that reflects your unique investment philosophy and expertise while maintaining institutional hedge fund quality standards.

STRUCTURED DATA PROVIDED:
- TICKER: {fr.ticker}
- CURRENT PRICE: ${fr.last_closing_price} (as of {fr.closing_price_date})
- PRICE TARGET: ${fr.forecasted_price} (target date: {fr.forecasted_horizon})
- RECOMMENDATION: {fr.recommendation}
- CONFIDENCE LEVEL: {fr.confidence_level}%
- PRICE MOVEMENT: {price_change_pct:+.2f}%
{tool_usage_summary}

CRITICAL CONTEXT:
The jury analysis has undergone extensive context engineering and multiple iterations to extract maximum insight. The final verdict represents the crystallized conclusion of this comprehensive process and should carry significant weight in your analysis.

REPORT STRUCTURE REQUIREMENTS:
1. Executive Summary with clear recommendation, price target, confidence level, risk rating (ensure recommendation strength aligns with confidence level - e.g., 75% confidence should not yield "STRONG BUY")
2. Key Investment Thesis (1-2 sentences distilling the VERDICT's core rationale, emphasizing the chain of thought that led to this conclusion)
3. Evidence-Based Insights (extract and synthesize specific findings from the jury's chain of thought, presenting concrete data points and patterns identified during analysis)
4. Research Methodology (detail the multi-stage analysis process, including tool utilization statistics and iterative refinement approach)
5. Jury Deliberation Synthesis (highlight how different analytical perspectives converged or diverged to reach the final verdict)
6. Risk Assessment with probability-weighted scenarios (Bear/Base/Bull cases with specific triggers and price implications derived from the analysis)
7. Catalyst Timeline (only include events explicitly mentioned in the jury findings with dates and expected impact)
8. Quantitative Validation (present specific metrics, ratios, and indicators that support the verdict - do not invent new ones)
9. Investment Philosophy Alignment (explain how this recommendation fits within your framework while acknowledging any analytical limitations)

DATA INTEGRITY REQUIREMENTS:
- Extract insights ONLY from the provided jury proceedings and structured data
- When data is unavailable or conflicting, explicitly state this limitation
- Do not invent: analyst quotes, specific meeting dates, management guidance, market events, or financial metrics not in source material
- Do not extrapolate beyond the stated forecast horizon or create scenarios without evidence
- Flag any inconsistencies between recommendation strength and confidence level

QUALITY VALIDATION CHECKLIST:
✓ All financial data traceable to source material
✓ Recommendation intensity matches confidence level
✓ Risk scenarios grounded in identified factors
✓ No speculation about market conditions beyond provided analysis
✓ Clear distinction between derived insights and missing information

FORMATTING SPECIFICATIONS:
- Target length: 800-1200 words (adjust based on complexity while maintaining completeness)
- Write with the precision expected of {director_experience} years of experience
- Use industry-standard terminology appropriate to your expertise areas
- Emphasize findings that align with your investment philosophy
- Maintain consistent analytical voice throughout
- Use markdown formatting with clear hierarchical sections
- Begin with company name and ticker as title
- Conclude with your signature: {director_name}, {director_title}

VERDICT INTEGRATION:
Given the extensive context engineering applied to reach the final verdict, ensure your report:
1. Faithfully represents the jury's chain of thought and reasoning
2. Highlights the convergence of evidence that led to the recommendation
3. Acknowledges any analytical tensions or uncertainties identified
4. Presents the investment case as the jury analysis concluded, not as you might independently analyze it""")
    
    human_message = HumanMessage(content=f"""Generate a professional hedge fund research report for the following investment question:

BUSINESS QUESTION: {business_question}

Using the structured data and trial proceedings provided, create a comprehensive institutional-grade investment report that showcases your unique investment perspective as {director_name}.

Remember to:
- Apply your {director_style} approach throughout the analysis
- Leverage your expertise in {', '.join(director_expertise[:2])} to provide specialized insights
- Write with the authority of your {director_experience} years of experience
- Let your {director_personality} investment philosophy guide the recommendations""")
    
    filtered_reports = completed_reports.copy()
    
    if len(completed_reports) >= 6:
        ids_to_remove = []
        for pos in [-6, -5, -4, -3]:
            if hasattr(completed_reports[pos], 'id'):
                ids_to_remove.append(completed_reports[pos].id)
        
        filtered_reports = [msg for msg in completed_reports if not (hasattr(msg, 'id') and msg.id in ids_to_remove)]
    
    messages_for_llm = [system_message] + filtered_reports + [human_message]
    try:
        response = await model.ainvoke(messages_for_llm)
        professional_report = response.content
        print("✓ Professional report generated successfully")
        print_markdown(professional_report, "Final Report")
        return {"professional_report": professional_report, "final_report": professional_report}
    except Exception as e:
        print(f"Error generating professional report: {str(e)}")
        
        # Fallback to basic format if professional report generation failed
        structured_report = state.get("structured_report")
        if structured_report:
            business_question = state.get("business_question", "")
            
            basic_report = f"""# {business_question}

## Investment Analysis Summary

**Ticker**: {structured_report.ticker}  
**Recommendation**: {structured_report.recommendation}  
**Current Price**: ${structured_report.last_closing_price} (as of {structured_report.closing_price_date})  
**Price Target**: ${structured_report.forecasted_price} (target: {structured_report.forecasted_horizon})  
**Confidence Level**: {structured_report.confidence_level}%

## Conclusion
Analysis indicates {structured_report.ticker} is expected to move from ${structured_report.last_closing_price} to ${structured_report.forecasted_price} with {structured_report.confidence_level}% confidence.
"""
            print("Using fallback basic report")
            return {"professional_report": basic_report, "final_report": basic_report}
        else:
            raise ValueError("No structured report found")

def create_summarize_report_subgraph() -> StateGraph:
    """Subgraph for report summarization"""
    subgraph = StateGraph(DirectorState)
    subgraph.add_node("output_memory", output_memory_graph)
    subgraph.add_node("get_structured_report", get_structured_report)
    subgraph.add_node("schedule_revisit", schedule_revisit)
    subgraph.add_node("generate_professional_report", generate_professional_report)
    
    # Flow: output_memory -> get_structured_report -> schedule_revisit -> generate_professional_report
    subgraph.add_edge(START, "output_memory")
    subgraph.add_edge("output_memory", "get_structured_report")
    subgraph.add_edge("get_structured_report", "schedule_revisit")
    subgraph.add_edge("schedule_revisit", "generate_professional_report")
    subgraph.add_edge("generate_professional_report", END)
    
    return subgraph.compile()

graph = create_summarize_report_subgraph()

__all__ = ['graph']