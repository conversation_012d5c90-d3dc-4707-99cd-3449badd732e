from langgraph.graph import StateGraph, START, END
from software.ai.graph.director_state import print_step, print_debug, print_pretty, vero_agent
from software.db.research_director_repository import ResearchDirectorRepository
from letta_client import Letta, MessageCreate

director_repo = ResearchDirectorRepository()

client = Letta(
    base_url="http://localhost:8283",
)

@vero_agent("Retrieving")
async def output_memory(state) -> dict:
    """Entering output memory"""

    # Handle both dictionary and AnalystState objects
    if hasattr(state, "get"):
        # It's a dictionary
        director_id = state.get("director_id")
        messages = state.get("messages")
    else:
        # It's an AnalystState or other object
        director_id = state.director_id if hasattr(state, "director_id") else None
        messages = state.messages if hasattr(state, "messages") else None

    result_message = None
    try:
        letta_agent_id = await director_repo.get_letta_agent_id(director_id)

        if not messages:
            result_message = "No messages to process"
        else:
            if hasattr(messages[-1], 'content'):
                user_prompt = messages[-1].content
            elif hasattr(messages[-1], 'reasoning'):
                user_prompt = messages[-1].reasoning
            elif isinstance(messages, str):
                user_prompt = messages
            else:
                user_prompt = "No user prompt provided."
                result_message = user_prompt

        if letta_agent_id and not result_message:
            response = client.agents.messages.create(
                agent_id=letta_agent_id,
                messages=[
                    MessageCreate(
                        role="user",
                        content=user_prompt,
                    ),
                ],
            )

            if response and hasattr(response, "messages") and response.messages:
                if hasattr(response.messages[-1], 'content'):
                    result_message = response.messages[-1].content
                elif hasattr(response.messages[-1], 'reasoning'):
                    result_message = response.messages[-1].reasoning
                else:
                    result_message = "Response message has no content or reasoning attribute"
            else:
                result_message = "No response from Letta"
    except Exception as e:
        print_debug(f"Error in output_memory: {str(e)}", "Error")
        result_message = f"Error: {str(e)}"

    # Always return a dictionary with messages key
    return {"messages": [result_message] if result_message else []}

def output_memory_subgraph() -> StateGraph:
    """Subgraph for director memory operations"""
    subgraph = StateGraph(dict)
    subgraph.add_node("output_memory", output_memory)
    subgraph.add_edge(START, "output_memory")
    subgraph.add_edge("output_memory", END)
    return subgraph.compile()

graph = output_memory_subgraph()

__all__ = ['graph']