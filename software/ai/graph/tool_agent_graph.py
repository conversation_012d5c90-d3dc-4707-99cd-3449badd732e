from typing import Dict, List, TypedDict, Annotated, Union, Sequence, Any
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage,
    ToolMessage
)
from langgraph.graph import StateGraph, END, START
from langgraph.prebuilt import ToolNode
from langgraph.graph.message import add_messages
from software.ai.llm.llm_connect import get_llm_connect
from software.ai.tools import registry
from software.db.research_repository import ResearchRepository
import rich
from rich.panel import Panel
from rich.pretty import Pretty
import json
from software.ai.graph.director_state import DirectorState, console
from software.ai.graph.feature_requestor_graph import graph as feature_requestor_graph

console = rich.get_console()
research_repo = ResearchRepository()

class State(TypedDict):
    """Track the state of the tool agent graph"""
    messages: Annotated[List[BaseMessage], add_messages]
    director_id: str
    thread_id: str

async def agent_node(state: State) -> Dict[str, Any]:
    """Node that uses LLM to decide tool usage and processes results"""
    try:
        console.print(Panel("[cyan]Entering agent node[/cyan]", title="Agent Node"))
        # Update workflow stage
        await research_repo.update_workflow_stage(state["thread_id"], "__start__", "completed")
        await research_repo.update_workflow_stage(state["thread_id"], "agent", "in_progress")
        
        # Get tools and bind them to the model
        tools = registry.get_langchain_tools()
        director_id = state.get("director_id")
        console.print(f"[violet]Director ID:[/violet] {director_id}")
        
        try:
            llm_connect = get_llm_connect()
            model = await llm_connect.get_llm_for_stage(director_id, "agent")
            if not model:
                raise ValueError("Failed to get LLM for agent stage")
            model_with_tools = model.bind_tools(tools)
        except Exception as e:
            console.print(Panel(f"[red]Error getting LLM:[/red] {str(e)}", title="LLM Error"))
            error_message = HumanMessage(content=f"Error: {str(e)}. Please try again or contact support.")
            return {"messages": [error_message]}
        
        # Initialize messages if needed
        if "messages" not in state:
            state["messages"] = []
            
        # Add system message on first run
        if not any(isinstance(msg, SystemMessage) for msg in state["messages"]):
            system_content = registry.generate_system_message() #dynamic system prompt for only Active tools
            system_message = SystemMessage(content= system_content)
            
            # Print the system prompt in a Rich panel
            console.print(Panel(
                system_content,
                title="[bold purple]DYNAMIC SYSTEM PROMPT[/bold purple]",
                border_style="purple"
            ))
            
            # Only add default question if there's no human message and no messages at all
            if not any(isinstance(msg, HumanMessage) for msg in state["messages"]) and len(state["messages"]) == 0:
                human_message = HumanMessage(content="What do Apple's (AAPL) technical indicators suggest about its price trend for next quarter?")
                state["messages"].extend([system_message, human_message])
            else:
                # Just add system message before existing messages
                state["messages"] = [system_message] + state["messages"]
        
        # Get model's response
        response = await model_with_tools.ainvoke(state["messages"])
        
        # Debug the response and tool calls
        if hasattr(response, 'tool_calls') and response.tool_calls:
            tool_calls_debug = "\n".join([
                f"[yellow]Tool:[/yellow] {call['name']}\n" +
                f"[yellow]Arguments:[/yellow] {json.dumps(call['args'], indent=2)}"
                for call in response.tool_calls
            ])
            console.print(Panel(
                tool_calls_debug,
                title="AGENT NODE: Tool Calls from LLM",
                border_style="yellow2"
            ))
        else:
            console.print(Panel(f"[green]Model Response:[/green]\n{response.content}", title="AGENT NODE: LLM Response", border_style="yellow2"))
        
        return {"messages": [response]}
    except Exception as e:
        error_msg = f"[red]Error in agent_node:[/red]\n{str(e)}"
        console.print(Panel(error_msg, title="Error"))
        raise

async def tool_node(state: State) -> Dict[str, Any]:
    """Node that executes tools"""
    try:
        console.print(Panel("[cyan]Entering tool node[/cyan]", title="Tool Node"))
        
        # Update workflow stage
        await research_repo.update_workflow_stage(state["thread_id"], "tools", "in_progress")
        
        # Debug the last message with tool calls
        if state.get("messages") and isinstance(state["messages"][-1], AIMessage) and state["messages"][-1].tool_calls:
            last_message = state["messages"][-1]
            tool_calls_debug = "\n".join([
                f"[yellow]Tool:[/yellow] {call['name']}\n" +
                f"[yellow]Arguments:[/yellow] {json.dumps(call['args'], indent=2)}"
                for call in last_message.tool_calls
            ])
            console.print(Panel(
                tool_calls_debug,
                title="DEBUG: Tool Calls Being Executed",
                border_style="red"
            ))
        else:
            console.print(Panel(
                "[red]No tool calls found in last message[/red]",
                title="DEBUG: Tool Calls Error",
                border_style="red"
            ))
            return {"messages": [AIMessage(content="I apologize, but I couldn't execute any tools because no valid tool calls were found.")]}
        
        # Get fresh tools and create tool executor
        tools = registry.get_langchain_tools()
        tool_executor = ToolNode(tools=tools)
        
        # Execute tools and return result
        try:
            result = await tool_executor.ainvoke(state)
            console.print(Panel.fit(Pretty(result), title="Tool Execution"))
            
            # Check if the result contains an error
            if "messages" in result and result["messages"] and isinstance(result["messages"][-1], ToolMessage):
                tool_message = result["messages"][-1]
                if isinstance(tool_message.content, dict) and "error" in tool_message.content:
                    error_info = tool_message.content["error"]
                    console.print(Panel(
                        f"[red]Tool returned error:[/red] {error_info}",
                        title="DEBUG: Tool Execution Error",
                        border_style="red"
                    ))
            
            return result
        except Exception as e:
            error_msg = f"Error executing tool: {str(e)}"
            console.print(Panel(error_msg, title="DEBUG: Tool Execution Error", border_style="red"))
            return {"messages": [ToolMessage(content={"error": error_msg}, name="error", tool_call_id="error")]}
            
    except Exception as e:
        error_msg = f"[red]Error in tool_node:[/red]\n{str(e)}"
        console.print(Panel(error_msg, title="Error"))
        raise

async def should_continue(state: State) -> Union[str, Sequence[str]]:
    """Determine if we should continue processing or end"""
    try:
        console.print(Panel("[cyan]Checking flow condition[/cyan]", title="Flow Control"))
        
        if not state.get("messages"):
            console.print(Panel("[yellow]No messages in state, ending[/yellow]", title="Flow Decision"))
            return END
            
        last_message = state["messages"][-1]
        if isinstance(last_message, AIMessage) and hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            console.print(Panel("[yellow]Tool calls detected, continuing to tools node[/yellow]", title="Flow Decision"))
            return "tools"

        await research_repo.update_workflow_stage(state["thread_id"], "agent", "completed")
        await research_repo.update_workflow_stage(state["thread_id"], "tools", "completed")
        await research_repo.update_workflow_stage(state["thread_id"], "END", "in_progress")
        await research_repo.update_workflow_stage(state["thread_id"], "END", "completed")
        console.print(Panel("[yellow]No tool calls, ending[/yellow]", title="Flow Decision"))

        return END
    except Exception as e:
        error_msg = f"[red]Error in should_continue:[/red]\n{str(e)}"
        console.print(Panel(error_msg, title="Error"))
        raise

def create_workflow_graph():
    """Create and return the workflow graph"""
    workflow = StateGraph(State)
    
    # Add nodes
    workflow.add_node("agent", agent_node)
    workflow.add_node("tools", tool_node)
    
    # Set entry point
    workflow.set_entry_point("agent")
    
    # Add edges
    workflow.add_conditional_edges(
        "agent",
        should_continue,
        {
            "tools": "tools",
            END: END
        }
    )
    workflow.add_edge("tools", "agent")
    
    # Compile the graph
    return workflow.compile()

# Create the graph
graph = create_workflow_graph()

def analyst_agent(state: DirectorState) -> DirectorState:
    console.print("[hot_pink]Entering analyst agent[/hot_pink]")
    return {"messages": "Analyst completed"}

def tools(state: DirectorState) -> DirectorState:
    console.print("[magenta1]Entering tools[/magenta1]")
    return {"messages": "Executing tools"}

def tool_calls(state: DirectorState) -> str:
    # Simple condition to demonstrate conditional edges
    if "Analyst" in state["messages"]:
        return "tools"
    elif "Feature Requestor" in state["messages"]:
        return "feature_requestor"
    else:
        return END

def create_tool_agent_subgraph() -> StateGraph:
    """Subgraph for tool agent operations"""
    subgraph = StateGraph(DirectorState)
    subgraph.add_node("analyst_agent", analyst_agent)
    subgraph.add_node("tools", tools)
    subgraph.add_node("feature_requestor", feature_requestor_graph)
    subgraph.add_edge(START, "analyst_agent")
    subgraph.add_edge("analyst_agent", "tools")
    subgraph.add_conditional_edges(
        "tools",
        tool_calls,
        {"analyst_agent": "analyst_agent", "feature_requestor": "feature_requestor", END: END}
    )
    subgraph.add_edge("feature_requestor", END)
    return subgraph.compile()

# graph = create_tool_agent_subgraph() # Commented out this line

__all__ = ['graph'] 