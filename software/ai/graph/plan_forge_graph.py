from typing import Dict, Any, Optional, List
from langgraph.graph import StateGraph, START, END
from langchain_core.messages import SystemMessage, HumanMessage
from software.ai.graph.director_state import DirectorState, PlanAssessment, PlanFramework, Plan, console, print_step, print_debug, print_pretty, vero_agent, print_markdown
from software.ai.llm.llm_connect import get_llm_connect
from software.db.research_director_repository import ResearchDirectorRepository
from software.ai.tools import registry
from datetime import datetime
import hashlib
import json
from bson import ObjectId

research_director_repo = ResearchDirectorRepository()

@vero_agent("Assessing")
async def assess_existing_plans(state: DirectorState) -> DirectorState:
    """Evaluate if existing plans can handle the business question"""
    
    director_id = state.get("director_id", "6866b0ed5bb9768d28d90920")
    business_question = state.get("business_question", "Should we invest in Nvidia (NVDA) in the next 30 days?")
    
    # Get director profile for context
    director_profile = state.get("director_profile")
    if not director_profile:
        full_profile = await research_director_repo.get_director(director_id)
        if full_profile:
            director_profile = {
                "name": full_profile.get("name", ""),
                "title": full_profile.get("title", ""),
                "experience_years": full_profile.get("experience_years", 0),
                "expertise": full_profile.get("expertise", []),
                "analysis_style": full_profile.get("analysis_style", ""),
                "background": full_profile.get("background", ""),
                "personality": full_profile.get("personality", "")
            }
    
    # Get all existing plans for this director
    existing_plans = await research_director_repo.get_director_plans(director_id)
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "plan_assessment")
    
    assessment_prompt = f"""<role>
You are the Strategic Planning Philosopher for {director_profile.get('name', 'Director')} ({director_profile.get('title', 'Investment Director')}).
You evaluate whether existing eternal planning frameworks can handle new business questions.
</role>

<director_context>
Director Profile:
- Name: {director_profile.get('name', 'Unknown')}
- Title: {director_profile.get('title', 'Unknown')}
- Experience: {director_profile.get('experience_years', 0)} years
- Expertise: {', '.join(director_profile.get('expertise', []))}
- Analysis Style: {director_profile.get('analysis_style', 'Unknown')}
- Background: {director_profile.get('background', 'Unknown')}
- Personality: {director_profile.get('personality', 'Unknown')}
</director_context>

<framework_status>
Current Planning Frameworks: {len(existing_plans)}/4 eternal slots used
Each framework must handle thousands of questions over decades.
</framework_status>

<existing_frameworks>
{json.dumps([{
    'plan_id': plan.get('plan_id', 'Unknown'),
    'goal': plan.get('goal', 'Not defined')
} for plan in existing_plans], indent=2)}
</existing_frameworks>

<business_question>
{business_question}
</business_question>

<critical_analysis>
FIRST, identify the CORE INTENT of the business question:
1. Is it asking for RESEARCH/ANALYSIS (gather data, investigate, analyze)?
2. Is it asking for REPORT WRITING (summarize findings, draft documents)?
3. Is it asking for DECISION MAKING (evaluate options, recommend actions)?
4. Is it asking for STRATEGY FORMULATION (develop plans, create frameworks)?
5. Is it asking for RISK ASSESSMENT (identify threats, evaluate exposure)?

THEN, evaluate how well each framework's stated goal matches this intent:
- A framework optimized for one task CAN handle others, but less effectively
- Prefer frameworks whose goals align with the question's primary intent
- Consider creative adaptations when no perfect match exists
</critical_analysis>

<evaluation_criteria>
Score each existing framework (0-100) for handling this specific question:
- 80-100: Excellent fit, framework's core purpose matches the question's intent
- 60-79: Good fit, framework can handle with minor adaptation
- 40-59: Moderate fit, framework's approach partially aligns
- 0-39: Poor fit, framework designed for different purpose

Threshold: Use existing if ANY framework scores ≥70
</evaluation_criteria>

<instructions>
1. FIRST identify the core intent/goal of the business question
2. Evaluate each existing framework against this core intent
3. REJECT frameworks that serve different purposes (e.g., research framework for report writing)
4. Consider the director's expertise and style in your assessment
5. Score each framework using the criteria above
6. If any framework scores ≥70, recommend the highest-scoring one
7. If all score <70, recommend creating new framework
8. Provide structured output using PlanAssessment model

<output_format>
{PlanAssessment.model_json_schema()}
</output_format>

</instructions>"""

    structured_model = model.with_structured_output(PlanAssessment)
    
    response = await structured_model.ainvoke([
        SystemMessage(content=assessment_prompt),
        HumanMessage(content="Analyze framework fitness for this business question")
    ])
    
    print(f"[bold green]Assessment:[/bold green] We are {("[bold green]creating a new[/bold green]" if response.create_new else "[bold orange]using[/bold orange]")} {response.selected_plan_id} plan")
    print(f"[bold green]Why?[/bold green] {response.justification}")
    
    # Update state
    state['create_new_plan'] = response.create_new
    state['selected_plan_id'] = response.selected_plan_id
    state['director_profile'] = director_profile
    
    return state

@vero_agent("Forging")
async def forge_new_framework(state: DirectorState) -> DirectorState:
    """Forge a new eternal planning framework"""
    
    director_id = state.get("director_id", "6866b0ed5bb9768d28d90920")
    business_question = state.get("business_question", "Should we invest in Nvidia (NVDA) in the next 30 days?")
    director_profile = state.get("director_profile", {})
    
    # Get existing plans to avoid bias
    existing_plans = await research_director_repo.get_director_plans(director_id)
    
    if len(existing_plans) >= 4:
        print_debug("All 4 planning slots used - cannot create new framework", "Plan Limit")
        return state
    
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "forge_framework")
    
    # Get available tools
    tool_names = await registry.get_tool_names_async()
    tool_names_comma = ', '.join(tool_names)
    
    forge_prompt = f"""<role>
You are the Eternal Framework Architect for {director_profile.get('name', 'Director')} ({director_profile.get('title', 'Investment Director')}).
You forge creative system prompts that excel at specific types of tasks.
</role>

<director_context>
- Expertise: {', '.join(director_profile.get('expertise', []))}
- Analysis Style: {director_profile.get('analysis_style', 'Unknown')}
- Background: {director_profile.get('background', 'Unknown')}
- Personality: {director_profile.get('personality', 'Unknown')}
</director_context>

<framework_status>
Current frameworks: {len(existing_plans)}/4 eternal slots
You have only 4 slots total - each must serve a distinct purpose.
</framework_status>

<existing_frameworks>
{json.dumps([{
    'goal': plan.get('goal', 'Not defined')
} for plan in existing_plans], indent=2)}
CRITICAL: Create a framework with a DIFFERENT primary purpose than those above.
</existing_frameworks>

<precipitating_question>
{business_question}
</precipitating_question>

<task_analysis>
Analyze what this business question is asking for:
- Is it seeking deep research and investigation?
- Is it requesting report writing and synthesis?
- Is it asking for risk evaluation and audit?
- Is it needing strategy formulation?
- Is it requiring decision consolidation?

Create a framework optimized for this type of task.
</task_analysis>

<available_tools>
Tools available for analysts: {tool_names_comma}
</available_tools>

<creative_framework_examples>
Research-focused: "The goal of this system prompt is to conduct forensic analysis on market anomalies"
Report-focused: "The goal of this system prompt is to synthesize findings into institutional-grade narratives"
Audit-focused: "The goal of this system prompt is to stress-test investment theses through adversarial review"
Strategy-focused: "The goal of this system prompt is to architect multi-horizon execution blueprints"
</creative_framework_examples>

<prompt_engineering_guidelines>
Your system prompt MUST follow these Anthropic best practices:

1. USE XML STRUCTURE for clarity:
   <role>Define a creative, specific persona</role>
   <philosophy>Core principles that guide this framework</philosophy>
   <decomposition_method>Unique approach to breaking down this type of task</decomposition_method>
   <task_blueprint>How to structure subtasks for this purpose</task_blueprint>
   <success_criteria>What excellence looks like for this framework</success_criteria>

2. TASK DECOMPOSITION with square bullets:
   □ Each subtask must be actionable and specific
   □ Include 6-8 subtasks tailored to the framework's purpose
   □ Make subtasks creative and non-obvious
   □ Ensure they work together as a system

3. CREATIVE DIFFERENTIATION:
   - Invent unique methodologies and approaches
   - Use distinctive terminology and concepts
   - Create memorable frameworks that stand out
   - Avoid generic or obvious decompositions

4. FRAMEWORK DIVERSITY IDEAS:
   - Forensic Investigation: Deep-dive detective work
   - Narrative Architecture: Story-driven synthesis
   - Adversarial Testing: Devil's advocate approach
   - Scenario Engineering: Future-state modeling
   - Pattern Recognition: Signal extraction
   - Systems Mapping: Interconnection analysis
   - Temporal Arbitrage: Time-based opportunities
</prompt_engineering_guidelines>

<requirements>
1. GOAL: Clear, task-driven statement in 15-20 words
   Format: "The goal of this system prompt is to [specific task focus]"
   Examples:
   - "The goal of this system prompt is to uncover hidden value through forensic financial archaeology"
   - "The goal of this system prompt is to craft compelling investment narratives from complex data"
   - "The goal of this system prompt is to identify systemic vulnerabilities through stress testing"

2. SYSTEM_PROMPT must include:
   a) <role> tag with creative, memorable persona
   b) <philosophy> tag with 3-5 guiding principles
   c) <decomposition_method> tag explaining the unique approach
   d) <task_blueprint> tag with 6-8 creative square-bullet subtasks
   e) <key_questions> tag with 5-7 probing questions
   f) <success_metrics> tag defining what excellence means

3. Framework must:
   - Excel at its designated task type
   - Use creative, non-obvious approaches
   - Have a distinct personality and method
   - Complement rather than duplicate existing frameworks
</requirements>

<output_format>
{PlanFramework.model_json_schema()}
</output_format>
"""

    structured_model = model.with_structured_output(PlanFramework)
    
    response = await structured_model.ainvoke([
        SystemMessage(content=forge_prompt),
        HumanMessage(content="Forge the eternal framework with great wisdom")
    ])
        
    # Save to database
    saved_plan = await research_director_repo.add_plan(
        director_id=director_id,
        goal=response.goal,
        system_prompt=response.system_prompt
    )
    
    if saved_plan:
        plan_id = saved_plan['plan_id']
        print(f"Framework saved with ID: {plan_id}")
        print(f"Goal: {response.goal}")
        
        # Store the plan_id for prepare_context to retrieve
        state['selected_plan_id'] = plan_id
    else:
        print_debug("Failed to save framework - likely at 4 plan limit", "Save Failed")
    
    return state

@vero_agent("Refining")
async def refine_framework(state: DirectorState) -> DirectorState:
    """Refine existing framework with critical thinking for specific question"""
    
    selected_plan_id = state.get("selected_plan_id")
    if not selected_plan_id:
        return state
    
    director_id = state.get("director_id", "")
    business_question = state.get("business_question", "")
    
    # Get the selected plan
    existing_plan = await research_director_repo.get_plan_by_id(director_id, selected_plan_id)
    if not existing_plan:
        return state
    
    original_system_prompt = existing_plan.get("system_prompt", "")
    original_goal = existing_plan.get("goal", "")
        
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "refine_framework")
    
    # Get available tools
    tool_names = await registry.get_tool_names_async()
    tool_names_comma = ', '.join(tool_names)
    
    refinement_prompt = f"""<role>
You are refining an eternal framework to add critical thinking for a specific business question.
You MUST return the system_prompt field as a PLAIN TEXT STRING, not as a message object or dictionary.
</role>

<original_framework>
GOAL: {original_goal}
SYSTEM_PROMPT (AS PLAIN STRING):
{original_system_prompt}
</original_framework>

<business_question>
{business_question}
</business_question>

<available_tools>
Tools available for analysts: {tool_names_comma}
</available_tools>

<task>
Enhance the system prompt by adding a <critical_thinking> section that:
1. Preserves the ENTIRE original system prompt as a single string
2. Adds critical thinking specific to this business question
3. Maintains the framework's eternal philosophy while adding targeted analysis

IMPORTANT: Return the enhanced system_prompt as a SINGLE STRING containing all the XML sections, 
not as a dictionary or object with 'role' keys.
</task>

<requirements>
1. GOAL: Keep exactly the same as original (return as a plain string)
2. SYSTEM_PROMPT: Must be a single string value containing:
   - ALL original XML sections intact
   - NEW <critical_thinking> section added at the end with:
     □ Specific factors critical for {business_question}
     □ Time-sensitive elements to consider
     □ Company/sector-specific dynamics
     □ Recent events or catalysts
     □ Assumptions requiring extra scrutiny
     □ 3-5 questions specific to this analysis
     □ 2-3 focus areas for deepest analysis
   
IMPORTANT: The system_prompt field MUST be a plain string, not a dict or object.
</requirements>

<example_critical_thinking>
<critical_thinking>
<context>Analyzing {business_question} requires special attention to [specific factors]</context>
<key_considerations>
□ [Specific factor 1 relevant to this question]
□ [Time-sensitive element specific to this timeframe]
□ [Company/sector dynamic that matters here]
□ [Recent catalyst or event to consider]
□ [Key assumption to validate for this case]
</key_considerations>
<targeted_questions>
1. [Question specific to this company/situation]
2. [Question about this timeframe]
3. [Question about this market context]
</targeted_questions>
<deep_focus_areas>
1. [Area 1 requiring deepest analysis]
2. [Area 2 critical for this decision]
</deep_focus_areas>
</critical_thinking>
</example_critical_thinking>

<example_correct_output>
Here's what your output should look like:
{{
  "goal": "Achieve optimal returns through timely and accurate analysis", 
  "system_prompt": "<role>You are the Template Framework...</role>\\n<context>...</context>\\n<critical_thinking>...</critical_thinking>"
}}

Note: system_prompt is ONE STRING containing all XML, not a dict like {{"role": "...", "context": "..."}}
</example_correct_output>

<output_format>
Return a PlanFramework with these fields:
- goal: string (keep original goal unchanged)
- system_prompt: string (the enhanced prompt as a single string, not a dict)

Schema: {PlanFramework.model_json_schema()}

CRITICAL: Both goal and system_prompt MUST be plain string values, not dictionaries or objects.
</output_format>
"""
    
    structured_model = model.with_structured_output(PlanFramework)
    
    try:
        response = await structured_model.ainvoke([
            SystemMessage(content=refinement_prompt),
            HumanMessage(content="Add critical thinking to the framework")
        ])
        
        # Ensure system_prompt is a string
        system_prompt_str = response.system_prompt
        if not isinstance(system_prompt_str, str):
            print_debug(f"ERROR: system_prompt is not a string, type: {type(system_prompt_str)}", "Type Error")
            # If it's not a string, convert it
            system_prompt_str = str(system_prompt_str)
            print(f"Converted system_prompt to string")
            
    except Exception as e:
        print_debug(f"Error in refine_framework: {str(e)}", "Refinement Error")
        # Fallback: just use the original system prompt
        system_prompt_str = original_system_prompt
        
    # Update the plan in MongoDB with the refined system prompt
    await research_director_repo.collection.update_one(
        {
            "_id": ObjectId(director_id),
            "plans.plan_id": selected_plan_id
        },
        {
            "$set": {
                "plans.$.system_prompt": system_prompt_str,
                "last_active": datetime.now()
            }
        }
    )
    
    print(f"Updated plan {selected_plan_id}")
    
    # Increment usage count
    await research_director_repo.increment_plan_usage(director_id, selected_plan_id)
    
    return state

@vero_agent("Routing")
def route_framework_decision(state: DirectorState) -> str:
    """Route based on whether to create new framework or use existing"""
    create_new = state.get("create_new_plan", False)
    
    if create_new:
        print(f"[bold green]Routing:[/bold green] Creating new framework")
        return "forge_framework"
    else:
        print(f"[bold orange]Routing:[/bold orange] Using existing framework")
        return "refine_framework"

@vero_agent("Preparing")
async def prepare_planning_context(state: DirectorState) -> DirectorState:
    """Prepare the system prompt for planning from selected framework"""
    
    director_id = state.get("director_id", "6866b0ed5bb9768d28d90920")
    selected_plan_id = state.get("selected_plan_id")
    business_question = state.get("business_question", "")
    
    if not selected_plan_id:
        print_debug("No plan selected", "Context Error")
        return state
    
    # Retrieve the plan from MongoDB
    plan = await research_director_repo.get_plan_by_id(director_id, selected_plan_id)
    if not plan:
        print_debug(f"Plan {selected_plan_id} not found", "Context Error")
        return state
    
    system_prompt = plan.get("system_prompt", "")

    print("Injecting new system prompt into the main planning node")
    
    # Simply pass the system prompt to be used by the main planning node
    return {'system_prompt_plan': system_prompt}

def create_plan_forge_subgraph() -> StateGraph:
    """Create the plan forge subgraph"""
    subgraph = StateGraph(DirectorState)
    
    # Add nodes
    subgraph.add_node("assess_plans", assess_existing_plans)
    subgraph.add_node("forge_framework", forge_new_framework)
    subgraph.add_node("refine_framework", refine_framework)
    subgraph.add_node("prepare_context", prepare_planning_context)
    
    # Define flow
    subgraph.add_edge(START, "assess_plans")
    subgraph.add_conditional_edges(
        "assess_plans",
        route_framework_decision,
        {
            "forge_framework": "forge_framework",
            "refine_framework": "refine_framework"
        }
    )
    subgraph.add_edge("forge_framework", "prepare_context")
    subgraph.add_edge("refine_framework", "prepare_context")
    subgraph.add_edge("prepare_context", END)
    
    return subgraph.compile()

graph = create_plan_forge_subgraph()

async def main():
    """Test the plan forge graph"""
    director_id = "67daf61442ee7bea5234c067"
    
    # Diverse business questions covering different intents (max 4 framework slots)
    test_questions = [
        # 1. RESEARCH/ANALYSIS - Investigative tasks
        "Should we invest in Apple (AAPL) considering the upcoming earnings report and market conditions?",
        
        # 2. REPORT WRITING - Document creation tasks
        "Write a comprehensive investment report summarizing our Q4 portfolio performance and recommendations for Q1 rebalancing for Nike",
        
        # 3. RISK ASSESSMENT - Threat evaluation tasks
        "Evaluate the systemic risks in our technology sector holdings given rising interest rates and regulatory pressures",
        
        # 4. STRATEGY FORMULATION - Planning tasks
        "Develop a 3-year investment strategy for emerging markets exposure with focus on ESG compliance",
        
        # 5. DECISION SYNTHESIS - Consolidation tasks (would exceed 4 slots)
        "Synthesize all analyst recommendations from the past month to decide on our position in semiconductor stocks"
    ]
    
    print_step(f"Testing with {len(test_questions)} diverse business questions", "Test Suite", "cyan")
    
    # Test with the report writing question
    business_question = test_questions[0]  # Report writing task
    
    initial_state = {
        "director_id": director_id,
        "business_question": business_question,
        "messages": []
    }
    
    result = await graph.ainvoke(initial_state)
    return result

if __name__ == "__main__":
    import asyncio
    result = asyncio.run(main())
    print("Final result:", result.get("critical_thinking_planning_result", "No result"))

__all__ = ['graph']