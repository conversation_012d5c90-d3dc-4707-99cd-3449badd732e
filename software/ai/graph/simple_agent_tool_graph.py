import random
import asyncio
import uuid
import logging
from langgraph.graph import StateGraph, START, END
from langchain_core.messages import (
    AIMessage,
    SystemMessage,
    HumanMessage,
    BaseMessage,
    ToolMessage,
)
from langgraph.types import Send
from software.ai.graph.director_state import AnalystState, AnalystOutput, AnalysisReport, print_step, print_pretty, print_debug
from langgraph.prebuilt import create_react_agent
from langgraph_supervisor import create_supervisor
from langgraph_supervisor import create_handoff_tool
from software.ai.llm.llm_connect_v2 import get_model_connector
from software.ai.llm.llm_connect import get_llm_connect
from chunking_evaluation.chunking import ClusterSemanticChunker
from software.ai.tools import registry
from typing import List, Dict, Any, Iterable, TypedDict, Optional, Annotated
import datetime
import json
from software.db.research_director_repository import ResearchDirectorRepository
import numpy as np

# Global configuration for memory retrieval
MAX_TOP_TOOLS = 5  # Maximum number of top tools to consider during retrieval
MAX_CHUNKS_PER_TOOL = 7  # Maximum number of chunks to include per tool
ENFORCE_TOOL_DIVERSITY = True  # If True, ensures diversity in tool selection by avoiding duplicate tool names


async def agent(state: AnalystState) -> Dict[str, Any]:
    print_step("AGENT NODE", "Starting agent")
    tools = await registry.get_langchain_tools_async()

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(state.director_id, "supervisor")
    # Model for agent/supervisor
    now = datetime.datetime.now()

    tool_prompt = await registry.generate_system_message_async()

    agent_persona_promt = f"""# FINANCIAL ANALYST AGENT

<identity>
You are {state.name_analyst}, a {state.age_analyst}-year-old {state.title_analyst} specializing in {state.background_analyst}.
Current date: {now.strftime('%Y-%m-%d')} at {now.strftime('%H:%M:%S')}
</identity>

<assignment>
You must answer: '{state.business_question}'
</assignment>

<available_tools>
{tool_prompt}
</available_tools>

<mandatory_protocol>
1. You MUST use tools to gather data - NEVER answer without tool-generated evidence
2. If your business question starts with "using [Tool Names]...", follow EXACTLY that tool sequence
3. Use ALL tools mentioned in the business question in the EXACT order specified
4. Follow a strict process:
   - ANALYZE the question to determine required data points
   - EXECUTE tools in the specified order if provided in the business question
   - INTERPRET results to inform your next action
   - CONTINUE using tools until you have comprehensive data
</mandatory_protocol>

<tool_sequence_handling>
When the business question specifies tools to use:
1. First, identify if the business question contains a tool sequence (typically starts with "using...")
2. If yes, extract the EXACT tool names in the order mentioned
3. Call these tools in EXACTLY this order - this is NON-NEGOTIABLE
4. NEVER skip any tool mentioned in the business question
5. Use ALL tools mentioned in the sequence, not just a subset
6. Understand that the tool sequence is critical because:
   - Each tool provides specific data needed for comprehensive analysis
   - Tools are designed to work together in sequence for optimal results
   - Skipping tools leads to incomplete data and flawed analysis
</tool_sequence_handling>

<function_calling_requirement>
You MUST use function calling to access tools. This is the ONLY way to perform analysis.
DO NOT just describe what you would do - ACTUALLY CALL THE TOOLS using function calling.
</function_calling_requirement>

<tool_usage_requirements>
1. Be SPECIFIC with parameters - provide exact ticker symbols, time periods, etc.
2. CHAIN tools together - use output from one tool as input to another
3. If a tool returns unexpected results, try a different approach
4. ALWAYS show your reasoning about which tool to use and why
5. Document the output of each tool clearly before proceeding to the next
</tool_usage_requirements>

<error_handling>
When tools fail or encounter constraints:
1. NEVER ask for human confirmation or intervention
2. If a tool requires specific parameters (e.g., minimum forecast days):
   - Automatically adjust your parameters to meet the tool's requirements
   - For forecast horizons, use the minimum required days (e.g., 5 days instead of 3)
   - Continue with the analysis using the adjusted parameters
3. If a tool fails completely:
   - Try ONE more time with adjusted parameters
   - If it still fails, document the failure and continue to the next tool
4. ALWAYS make autonomous decisions - there is no human in the loop
5. NEVER wait for user input or confirmation

<example>
INCORRECT (asking for confirmation):
"The SimpleWorkflowTool requires a minimum forecast horizon of 5 days, but the request is for 3 days.
Should I proceed with a 5-day forecast instead?"

CORRECT (making autonomous decision):
"I notice the SimpleWorkflowTool requires a minimum forecast horizon of 5 days, while our request is for 3 days.
I'll automatically adjust to use a 5-day forecast horizon to meet the tool's requirements and continue with the analysis."
</example>
</error_handling>

<execution_approach>
You are a TOOL-FIRST analyst. Your value comes from your ability to leverage tools effectively.
Begin by identifying which tools you need and execute them immediately.

When executing tools:
1. Start with the first tool in the sequence if specified
2. Carefully review the output of each tool
3. Use the output to inform parameters for subsequent tools
4. Continue until you have gathered all necessary data
5. Only then synthesize your findings into a comprehensive analysis
</execution_approach>
"""

    try:
        langgraph_agent_executor = create_react_agent(
            model=model,
            tools=tools,
            name="data_forecaster",
            prompt=agent_persona_promt,
            debug=False
        )
        # Successfully created react agent
    except Exception as e:
        print_debug(f"Error creating react agent: {str(e)}", "Agent Creation Error")
        raise
    # Create supervisor prompt with critical thinking section and available tools
    supervisor_prompt = f"""# FINANCIAL RESEARCH DIRECTOR

<role>
You are the VP of Financial Research directing analysts to answer business questions and create structured financial reports. Your expertise lies in orchestrating data collection, analysis, and synthesizing insights into actionable recommendations.
</role>

<available_tools>
{tool_prompt}
</available_tools>

<workflow>
Your work has TWO distinct sequential phases:

<phase_1>
DATA COLLECTION:
1. DELEGATE all data collection to your analyst using function calling
2. Call the 'data_forecaster' agent using this syntax:
   {{"name": "data_forecaster", "arguments": {{"input": "Your instructions here"}}}}
3. If the business question includes a tool sequence (e.g., "using ToolX, ToolY, ToolZ..."), instruct the analyst to follow that EXACT sequence
4. Pass the COMPLETE business question to the analyst, preserving all tool names mentioned
5. Request comprehensive data collection using ALL appropriate tools
6. Evaluate results and request additional data if needed
</phase_1>

<phase_2>
REPORT CREATION:
1. After sufficient data is collected, STOP using function calls
2. Create a TEXT-ONLY draft report (not a function call)
3. Your final message must be a plain text report, not a tool call
</phase_2>
</workflow>

<tool_sequence_handling>
When the business question specifies tools to use:
1. NEVER modify, remove, or substitute any tool names specified in the business question
2. ALWAYS pass the complete, unmodified tool sequence to the analyst
3. Ensure ALL tools mentioned are used in the EXACT order specified
4. Understand that the tool sequence is critical because:
   - Each tool provides specific data needed for comprehensive analysis
   - Tools are designed to work together in sequence for optimal results
   - Skipping tools leads to incomplete data and flawed analysis
   - The user has specifically requested this workflow for a reason

<reasoning_steps>
When determining how to handle tool sequences:
1. First, identify if the business question contains a tool sequence (typically starts with "using...")
2. If yes, extract the EXACT tool names in the order mentioned
3. Verify all tools exist in the available tools list
4. Pass the COMPLETE business question with ALL tool names preserved to the data_forecaster
5. Monitor that each tool in the sequence is actually used by the analyst
</reasoning_steps>

<error_handling>
When tools fail or encounter constraints:
1. NEVER ask for human confirmation or intervention
2. If a tool fails due to parameter constraints (e.g., minimum forecast days):
   - Automatically adjust parameters to meet the tool's requirements
   - For forecast horizons, use the tool's minimum required days
   - Document the adjustment in your final report
3. If a tool fails completely:
   - Try ONE more time with adjusted parameters
   - If it still fails, continue to the next tool in the sequence
   - Document the failure in your final report
4. ALWAYS make autonomous decisions - there is no human in the loop
5. NEVER return a draft report asking for confirmation or instructions

<example>
INCORRECT (asking for confirmation):
"The SimpleWorkflowTool requires a minimum forecast horizon of 5 days, but the request is for 3 days.
Please confirm if you would like me to proceed with a 5-day forecast horizon."

CORRECT (making autonomous decision):
"I'll proceed with the SimpleWorkflowTool using a 5-day forecast horizon (the minimum required by the tool)
instead of the requested 3-day horizon. This adjustment is necessary to complete the analysis with the
specified tools. The final report will note this adjustment."
</example>
</error_handling>
</tool_sequence_handling>

<final_report_format>
Your final message MUST be a plain text report including:

1. Business answer with specific numerical findings
2. Target metric with current and predicted values
3. Specific dates and realistic percentage changes
4. Confidence level (0-100%)
5. Clear BUY/SELL/HOLD recommendation
6. Key observations and supporting evidence
7. Balanced counterarguments

<success_criteria>
A successful report will:
- Directly answer the original business question
- Include specific numerical data from the tools
- Provide actionable insights based on the data
- Present a balanced view with both supporting and opposing arguments
- Maintain a professional, concise tone appropriate for executive review
- Draw clear connections between data points and recommendations
</success_criteria>
</final_report_format>

<critical_instruction>
After data collection is complete, your FINAL response must be a TEXT STRING containing the draft report.
DO NOT make another function call as your final response.
NEVER ask for human confirmation or input in your final report.
ALWAYS make autonomous decisions when tools encounter constraints.
If tools require parameter adjustments (e.g., 5-day forecast instead of 3-day), make those adjustments automatically and document them in your report.
Remember: There is NO human in the loop - you must complete the entire analysis without asking for confirmation.
</critical_instruction>
"""

    supervisor_vp = create_supervisor(
        [langgraph_agent_executor],
        model=model,
        prompt=supervisor_prompt,
        supervisor_name="supervisor_vp",
        output_mode="full_history"
    )
    supervisor_vp_app = supervisor_vp.compile()
    business_question = state.business_question
    response = await supervisor_vp_app.ainvoke({
        "messages": [
            {"role": "user", "content": business_question}
        ]
    })

    print_pretty(response)
    tool_calls_made = False
    for message in response.get("messages", []):
        if hasattr(message, "tool_calls") and message.tool_calls or hasattr(message, "function_call") and message.function_call:
            tool_calls_made = True

    if "messages" in response and response["messages"] and len(response["messages"]) > 0:
        last_message = response["messages"][-1]
        draft_report_vp = last_message.content if hasattr(last_message, "content") else str(last_message)
        print_step(draft_report_vp, "Draft Report from VP")

    if not tool_calls_made:
        print_debug("No tool calls were made by the supervisor", "No Tool Calls")
        pass

    return {"messages": response["messages"], 'draft_report_vp': draft_report_vp}

async def extract_successful_tools(state: AnalystState) -> Dict[str, Any]:
    print_step("EXTRACT SUCCESSFUL TOOLS", "Extracting successful tools from messages")
    successful_tools = {}
    tool_call_args, tool_responses = {}, {}

    if not hasattr(state, "messages"):
        return {"successful_tools": successful_tools}

    for m in state.messages:
        if getattr(m, "tool_calls", None):
            for t in m.tool_calls:
                tid = t["id"] if isinstance(t, dict) else t.id
                targs = t.get("args", {}) if isinstance(t, dict) else getattr(t, "args", {})
                if not targs and hasattr(t, "function"):
                    try:
                        targs = json.loads(getattr(t.function, "arguments", "{}"))
                    except Exception:
                        targs = {}
                tool_call_args[tid] = targs
        if getattr(m, "tool_call_id", None):
            tool_responses[m.tool_call_id] = {"name": m.name, "content": m.content}

    def format_json_value(value):
        if isinstance(value, dict):
            try:
                markdown = ""
                for k, v in value.items():
                    markdown += f"*{k}*\n{v}\n\n"
                return markdown
            except Exception:
                return str(value)
        elif isinstance(value, list):
            try:
                return json.dumps(value, indent=2)
            except Exception:
                return str(value)
        elif isinstance(value, str):
            try:
                parsed = json.loads(value)
                if isinstance(parsed, dict):
                    markdown = ""
                    for k, v in parsed.items():
                        markdown += f"*{k}*\n{v}\n\n"
                    return markdown
                return json.dumps(parsed, indent=2)
            except Exception:
                return value
        return str(value)

    def flatten_dict(d, parent_key='', sep='_'):
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(flatten_dict(v, new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)

    for tid, resp in tool_responses.items():
        tool_name = resp["name"]
        tool_content = resp["content"]

        # Skip transfer tools
        if tool_name.startswith(("transfer_to", "transfer_back")):
            continue

        # Skip error tools
        if "Error:" in str(tool_content):
            continue

        # Format content
        formatted_content = format_json_value(tool_content)

        # Format and flatten args
        args = tool_call_args.get(tid, {})
        if isinstance(args, str):
            try:
                args = json.loads(args)
            except Exception:
                pass
        flattened_args = flatten_dict(args) if isinstance(args, dict) else args
        formatted_args = format_json_value(flattened_args)

        successful_tools[tid] = {
            "name": tool_name,
            "content": formatted_content,
            "args": formatted_args
        }

    # Successful tools extracted
    return {"successful_tools": successful_tools}


async def create_chunks(state: AnalystState) -> AnalystState:
    print_step("CREATE CHUNKS", "Creating chunks for successful tools")
    successful_tools = state.successful_tools

    # Use the new connector
    connector = get_model_connector()

    # Get the embedding model asynchronously
    embedding_model = await connector.get_embedding_model()

    # Create an embedding function that works with ClusterSemanticChunker
    def embedding_function(texts):
        # This needs to be synchronous for ClusterSemanticChunker
        if isinstance(texts, str):
            texts = [texts]
        # Use the synchronous embed_documents method directly from the model
        return embedding_model.embed_documents(texts)

    for tool_id, tool in successful_tools.items():
        print_step(f"CHUNKING TOOL: {tool_id}", "Chunking tool")
        # Create chunker with the embedding function
        if len(tool["content"]) < 300:
            successful_tools[tool_id]["chunks"] = {'0': {'content': tool["content"]}}
            continue
        chunker = ClusterSemanticChunker(embedding_function, max_chunk_size=300)
        chunks = chunker.split_text(tool["content"])
        for i, chunk in enumerate(chunks):
            chunks[i] = chunk

        # Convert chunks to dict with id int as key
        chunks = {str(i): {'content': chunk} for i, chunk in enumerate(chunks)}
        successful_tools[tool_id]["chunks"] = chunks
    # Successful tools with chunks
    return {"successful_tools": successful_tools}

async def embed_chunks(state: AnalystState) -> AnalystState:
    print_step("EMBED CHUNKS", "Embedding chunks for successful tools")
    successful_tools = state.successful_tools
    connector = get_model_connector()
    embedding_model = await connector.get_embedding_model()

    for tool_id, tool in successful_tools.items():
        print_step(f"EMBEDDING TOOL: {tool_id}", "Embedding tool")

        # Main tool embedding
        tool_text = tool["content"]
        embeddings = embedding_model.embed_documents([tool_text])[0]  # Get the first embedding
        successful_tools[tool_id]["tool_embeddings"] = embeddings

        # Embed each chunk
        for chunk_id, chunk in tool["chunks"].items():
            # Extract the content string from the chunk dictionary
            chunk_content = chunk["content"]
            chunk_embeddings = embedding_model.embed_documents([chunk_content])[0]  # Get the first embedding
            #save embedding
            successful_tools[tool_id]["chunks"][chunk_id]["chunk_embeddings"] = chunk_embeddings

        # Note: MongoDB requires string keys, but we keep numeric keys here
        # The conversion happens in ResearchDirectorRepository.add_successful_tool_embeddings

    return {"successful_tools": successful_tools}


async def save_in_memory(state: AnalystState) -> AnalystState:
    print_step("SAVE IN MEMORY", "Saving successful tools in Director Memory")
    director_id = state.director_id
    successful_tools = state.successful_tools
    director_repo = ResearchDirectorRepository()
    await director_repo.add_successful_tool_embeddings(director_id, successful_tools)
    return {"successful_tools": successful_tools}

async def zoom_in_retrieve(state: AnalystState) -> Dict[str, Any]:
    print_step("ZOOM IN RETRIEVE", "Retrieving and analyzing past tool executions")
    director_id = state.director_id
    business_question = state.business_question

    # Initialize empty results in case of errors
    empty_result = {"retrieved_context": "", "rag_answer": ""}

    try:
        # Get director data
        director_repo = ResearchDirectorRepository()
        director = await director_repo.get_director(director_id)

        # Check if memory structure exists
        if not director or "memory" not in director:
            # Director not found or has no memory structure
            return empty_result

        if "successful_tool_embeddings" not in director["memory"]:
            # No tool embeddings found in director memory
            return empty_result

        embeddings_map = director["memory"]["successful_tool_embeddings"]
        # Found tool executions in memory

        # Extract key terms from business question
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm_for_stage(director_id, "zoom_in_retrieve")

        system_prompt = "Extract key financial search terms from business questions. Return ONLY a comma-separated list."
        human_prompt = f"Extract key terms from: {business_question}"

        extraction_response = await model.ainvoke([
            SystemMessage(content=system_prompt),
            HumanMessage(content=human_prompt)
        ])
        key_terms = extraction_response.content.strip()
        # Extracted key terms

        # Create enhanced query and generate embedding
        enhanced_query = f"{business_question} {key_terms}"
        connector = get_model_connector()
        embedding = await connector.embed_query(enhanced_query)
        # Created embedding vector

        # Find similar tools based on embeddings
        similar_tools = []
        for tool_id, tool_data in embeddings_map.items():
            # Skip tools without embeddings
            tool_embedding = tool_data.get("tool_embeddings")
            if not tool_embedding:
                # Tool has no tool_embeddings field
                continue

            # Calculate cosine similarity
            similarity = np.dot(embedding, tool_embedding) / (
                np.linalg.norm(embedding) * np.linalg.norm(tool_embedding)
            )

            # Add to results
            similar_tools.append({
                "tool_id": tool_id,
                "name": tool_data["name"],
                "similarity": float(similarity),
                "content": tool_data.get("content", "")
            })

        # Sort by similarity
        similar_tools = sorted(similar_tools, key=lambda x: x["similarity"], reverse=True)

        # Select top tools with diversity enforcement if enabled
        if ENFORCE_TOOL_DIVERSITY:
            # Select diverse tools based on name
            top_tools = []
            tool_names_seen = set()

            # Process all tools in order of similarity
            for tool in similar_tools:
                # Skip if we already have enough tools
                if len(top_tools) >= MAX_TOP_TOOLS:
                    break

                # Add tool if its name is not already in our selection
                if tool["name"] not in tool_names_seen:
                    top_tools.append(tool)
                    tool_names_seen.add(tool["name"])
                    # Added diverse tool

            # Enforced tool diversity
        else:
            # Just take top N without enforcing diversity
            top_tools = similar_tools[:MAX_TOP_TOOLS]
            # Tool diversity not enforced

        # Log similarity scores for selected tools
        # Log similarity scores for selected tools
        pass  # Selected top tools with similarities

        # Second stage: Find most relevant chunks for each top tool
        structured_context = "# Retrieved Past Tool Executions\n\n"
        total_chunks_found = 0

        for i, tool in enumerate(top_tools):
            tool_id = tool["tool_id"]
            tool_data = embeddings_map[tool_id]

            # Skip if no chunks available
            if "chunks" not in tool_data or not tool_data["chunks"]:
                # No chunks found for tool
                structured_context += f"## Tool Execution {i+1}: {tool['name']} (Similarity: {tool['similarity']:.2f})\n\n"
                structured_context += f"Content: {tool['content']}\n\n"
                structured_context += "---\n\n"
                continue

            # Find similar chunks within this tool
            chunks = tool_data["chunks"]
            similar_chunks = []

            # Found chunks for tool

            for chunk_id, chunk_data in chunks.items():
                # Skip chunks without embeddings
                chunk_embedding = chunk_data.get("chunk_embeddings")
                if not chunk_embedding:
                    continue

                # Calculate similarity between query and chunk
                chunk_similarity = np.dot(embedding, chunk_embedding) / (
                    np.linalg.norm(embedding) * np.linalg.norm(chunk_embedding)
                )

                similar_chunks.append({
                    "chunk_id": chunk_id,
                    "content": chunk_data.get("content", ""),
                    "similarity": float(chunk_similarity)
                })

            # Sort chunks by similarity and take top N
            similar_chunks = sorted(similar_chunks, key=lambda x: x["similarity"], reverse=True)
            top_chunks = similar_chunks[:MAX_CHUNKS_PER_TOOL]
            total_chunks_found += len(top_chunks)

            # Log chunk similarity scores
            if top_chunks:
                # Log chunk similarity scores
                pass  # Selected top chunks for tool

            # Add tool and its top chunks to context
            structured_context += f"## Tool Execution {i+1}: {tool['name']} (Similarity: {tool['similarity']:.2f})\n\n"

            if top_chunks:
                for j, chunk in enumerate(top_chunks):
                    structured_context += f"### Chunk {j+1} (Similarity: {chunk['similarity']:.2f})\n\n"
                    structured_context += f"{chunk['content']}\n\n"
            else:
                structured_context += f"Content: {tool['content']}\n\n"

            structured_context += "---\n\n"

        # Retrieved tools with chunks

        # Create a detailed context preview
        context_lines = structured_context.split('\n')
        context_preview = "\n".join(context_lines[:30])  # Show first 30 lines
        if len(context_lines) > 30:
            context_preview += "\n... (truncated) ..."

        # Show the structure of the context
        context_structure = []
        for line in context_lines:
            if line.startswith('# '):
                context_structure.append(line)
            elif line.startswith('## '):
                context_structure.append('  ' + line)
            elif line.startswith('### '):
                context_structure.append('    ' + line)

        # Print both the preview and structure
        # Structured context preview
        # Context structure

        # Extract relevant information from past executions
        system_message = SystemMessage(content=f"""Analyze past tool executions to extract information for an AnalysisReport.

Business Question: {business_question}

Based on these past executions:
{structured_context}

Extract information relevant to:
- confidence, conclusion (BUY/SELL/HOLD)
- current_price, predicted_price
- dates and time horizons
- supporting and opposing arguments
- numerical values and specific findings
""")

        response = await model.ainvoke([system_message])
        answer = response.content
        # Generated analysis from past tool executions

        return {
            "retrieved_context": structured_context,
            "rag_answer": answer
        }

    except Exception as e:
        print_debug(f"Error in zoom_in_retrieve: {str(e)}", "Error")
        return empty_result

async def synthesize_response(state: AnalystState) -> Dict[str, Any]:
    print_step("SYNTHESIZE RESPONSE", "Synthesizing response")
    business_question = state.business_question
    director_id = state.director_id
    rag_answer = getattr(state, "rag_answer", "")
    draft_report_vp = state.draft_report_vp

    # Get the LLM model
    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm_for_stage(director_id, "synthesize_response")

    # Create structured output model
    structured_model = model.with_structured_output(AnalysisReport)

    # Prepare context
    now = datetime.datetime.now()
    date_str = now.strftime('%Y-%m-%d')
    time_str = now.strftime('%H:%M:%S')

    # Create system message with context and example
    system_message = f"""You are a financial analysis AI that creates structured reports.

Business Question: {business_question}

{rag_answer if rag_answer else 'No historical data available.'}

Draft Report from VP: {draft_report_vp}

Create a complete AnalysisReport with realistic financial predictions and well-reasoned arguments.
Ensure all required fields are filled with appropriate values.

Make sure to include:
- Specific numerical findings with realistic values
- Clear BUY/SELL/HOLD recommendation with confidence level
- Current and predicted values with specific dates
- Well-reasoned arguments for and against your conclusion
"""

    # Create human message with current date/time
    human_message = f"Today is {date_str} at {time_str}. Generate a complete analysis report that answers the business question."

    # Use structured output to get the report
    # Invoking structured output model
    report = await structured_model.ainvoke([
        SystemMessage(content=system_message),
        HumanMessage(content=human_message)
    ])

    # Ensure report has an ID
    if not getattr(report, "analysis_report_id", None):
        report.analysis_report_id = str(uuid.uuid4())

    # Log the final report
    print_step("Successfully generated analysis report", "Synthesize Response")
    return {"completed_reports": [report]}


def create_simple_agent_tool_graph() -> StateGraph:
    subgraph = StateGraph(AnalystState, output=AnalystOutput)

    # Add all nodes
    subgraph.add_node("agent", agent)
    subgraph.add_node("extract_successful_tools", extract_successful_tools)
    subgraph.add_node("create_chunks", create_chunks)
    subgraph.add_node("embed_chunks", embed_chunks)
    subgraph.add_node("save_in_memory", save_in_memory)
    subgraph.add_node("zoom_in_retrieve", zoom_in_retrieve)
    subgraph.add_node("synthesize_response", synthesize_response)

    # Connect nodes with edges
    subgraph.add_edge(START, "agent")
    subgraph.add_edge("agent", "extract_successful_tools")
    subgraph.add_edge("extract_successful_tools", "create_chunks")
    subgraph.add_edge("create_chunks", "embed_chunks")
    subgraph.add_edge("embed_chunks", "save_in_memory")
    subgraph.add_edge("save_in_memory", "zoom_in_retrieve")
    subgraph.add_edge("zoom_in_retrieve", "synthesize_response")
    subgraph.add_edge("synthesize_response", END)
    return subgraph.compile()

graph = create_simple_agent_tool_graph()

__all__ = ['graph']