from langgraph.graph.message import add_messages
from langchain_core.messages import (
    BaseMessage,
)
from typing import Annotated, List, TypedDict, Optional, Literal, Any, Dict
from pydantic import BaseModel, Field
import uuid
from datetime import date

import rich
from rich.panel import Panel
from rich.pretty import Pretty
from rich.color import ANSI_COLOR_NAMES
from rich.markdown import Markdown
import random

from software.db.research_repository import ResearchRepository

# Custom reducer for aggregating tool logs from multiple analysts
def aggregate_tool_logs(existing: List[Dict[str, Any]], new: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Aggregate tool logs from multiple analysts.
    Each analyst returns a structured dict with their information and tool logs.
    This reducer appends each analyst's data to the master list.
    
    Note: When using Send with multiple analysts, LangGraph may call this reducer
    multiple times - once for each analyst's output.
    """
    if new is None:
        return existing if existing else []
    
    # Debug logging
    #print_debug(f"Reducer called - existing count: {len(existing)}, new type: {type(new)}", "Tool Logs Reducer")
    
    # Handle when new is a list (multiple analysts via Send)
    if isinstance(new, list):
        # Accumulate all analysts from the list
        result = existing.copy() if existing else []
        
        # Process each item in the list
        for item in new:
            if isinstance(item, dict) and 'analyst_name' in item:
                # Check if this analyst already exists in result (avoid duplicates)
                analyst_exists = any(
                    analyst.get('analyst_name') == item.get('analyst_name') and
                    analyst.get('analyst_title') == item.get('analyst_title')
                    for analyst in result
                )
                if not analyst_exists:
                    result.append(item)
                    #print_debug(f"Added analyst from list: {item.get('analyst_name')}", "Tool Logs Aggregation")
        
        return result
    
    # Handle when new is a single dict (single analyst)
    elif isinstance(new, dict) and 'analyst_name' in new:
        # Accumulate with existing analysts
        result = existing.copy() if existing else []
        
        # Check if this analyst already exists (avoid duplicates)
        analyst_exists = any(
            analyst.get('analyst_name') == new.get('analyst_name') and
            analyst.get('analyst_title') == new.get('analyst_title')
            for analyst in result
        )
        
        if not analyst_exists:
            result.append(new)
            print_debug(f"Added single analyst: {new.get('analyst_name')}", "Tool Logs Aggregation")
        else:
            print_debug(f"Skipped duplicate analyst: {new.get('analyst_name')}", "Tool Logs Aggregation")
        
        return result
    
    # If new is not in the expected format, return existing
    print_debug(f"Unexpected format for new: {type(new)}", "Tool Logs Aggregation Warning")
    return existing if existing else []

# ┌────────────────────────────────────────────────────────────────────┐
# │                       CONSOLE UTILITIES                             │
# └────────────────────────────────────────────────────────────────────┘
console = rich.get_console()
research_repo = ResearchRepository()
VERBOSE = True

def print_step(message: str, title: str, border_style: str = "random", verbose: bool = VERBOSE):
    if not verbose:
        return
    if border_style == "random":
        border_style = random.choice(list(ANSI_COLOR_NAMES.keys()))
        while "red" in border_style:
            border_style = random.choice(list(ANSI_COLOR_NAMES.keys()))
    console.print(Panel(message, title="STEP", title_align="left", subtitle=title, border_style=border_style))

def print_pretty(object: object):
    if not VERBOSE:
        return
    border_style = random.choice(list(ANSI_COLOR_NAMES.keys()))
    console.print(Panel(Pretty(object), title="PRETTY", title_align="left", border_style=border_style))

def print_debug(message: str, title: str = str(uuid.uuid4()), verbose: bool = True):
    if not verbose:
        return
    border_style = "bright_red"
    console.print(Panel(message, title="DEBUG", title_align="left", subtitle=title, border_style=border_style))

def print_markdown(markdown_text: str, title: str, border_style: str = "random", verbose: bool = VERBOSE):
    if not verbose:
        return
    if border_style == "random":
        border_style = random.choice(list(ANSI_COLOR_NAMES.keys()))
        while "red" in border_style:
            border_style = random.choice(list(ANSI_COLOR_NAMES.keys()))
    md = Markdown(markdown_text)
    console.print(Panel(md, title=title, title_align="center", border_style=border_style))

def vero_agent(message: str = None):
    """Production-ready decorator for visualizing parallel graph execution with colored output.
    
    Args:
        message: Optional custom action message (e.g., "Processing", "Analyzing")
                If not provided, a random action verb will be selected.
    """
    from rich.console import Console
    from rich.padding import Padding
    from datetime import datetime
    from functools import wraps
    import threading
    import builtins
    import time
    import asyncio
    import inspect
    
    # Initialize shared resources if not already done
    if not hasattr(vero_agent, '_initialized'):
        vero_agent._initialized = True
        vero_agent._console_lock = threading.Lock()
        vero_agent._assigned_colors = {}
        vero_agent._color_index = 0
        vero_agent._active_tasks = {}
        vero_agent._tasks_lock = threading.Lock()
        vero_agent._thread_local = threading.local()
        vero_agent._available_colors = [
            "bright_cyan", "bright_green", "bright_yellow", "bright_magenta",
            "bright_blue", "bright_red", "cyan", "green", "yellow", "magenta",
            "blue", "red", "bright_white", "orange1", "purple", "violet",
            "spring_green1", "dodger_blue1", "deep_pink1", "gold1", "orchid1",
            "turquoise2", "orange3", "light_coral", "salmon1", "dark_orange3",
            "plum1", "medium_purple1", "light_slate_blue", "light_pink1", "wheat1"
        ]
    
    def decorator(func):
        is_async = inspect.iscoroutinefunction(func)
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            action_verbs = [
                "Analyzing", "Processing", "Computing", "Evaluating",
                "Optimizing", "Synthesizing", "Reasoning", "Learning"
            ]
            
            func_name = func.__name__
            readable_name = ' '.join(word.capitalize() for word in func_name.split('_'))
            
            # Assign color to this function if not already assigned
            if func_name not in vero_agent._assigned_colors:
                vero_agent._assigned_colors[func_name] = vero_agent._available_colors[
                    vero_agent._color_index % len(vero_agent._available_colors)
                ]
                vero_agent._color_index += 1
            node_color = vero_agent._assigned_colors[func_name]
            
            action = message if message else random.choice(action_verbs)
            action_base = action.rstrip(".")
            
            console = Console()
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            # Build and print header
            header_lines = []
            header_lines.append(f"\n[{node_color}]● {readable_name}[/{node_color}] [dim]started at {timestamp}[/dim]")
            
            if func.__doc__:
                doc_lines = func.__doc__.strip().split('\n')
                for doc_line in doc_lines:
                    header_lines.append(f"  [dim italic]{doc_line.strip()}[/dim italic]")
            
            spinner_chars = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
            spinner_char = spinner_chars[int(time.time() * 10) % len(spinner_chars)]
            header_lines.append(f"  [{node_color}]{spinner_char}[/{node_color}] [italic]{action_base.lower()}[/italic]")
            
            with vero_agent._console_lock:
                console.print("\n".join(header_lines))
            
            with vero_agent._tasks_lock:
                vero_agent._active_tasks[func_name] = {
                    'name': readable_name,
                    'action': action_base.lower(),
                    'color': node_color,
                    'start_time': time.time()
                }
            
            start_time = time.time()
            original_print = print
            original_console_print = console.print
            captured_color = node_color
            
            def make_indented_print(color):
                def indented_print(*args, **kwargs):
                    if len(args) == 1 and hasattr(args[0], '__rich_console__'):
                        padded = Padding(args[0], pad=(0, 0, 0, 4))
                        with vero_agent._console_lock:
                            original_console_print(padded)
                    else:
                        message = " ".join(str(arg) for arg in args)
                        lines = message.split('\n')
                        with vero_agent._console_lock:
                            for line in lines:
                                original_console_print(f"  [{color}]│[/{color}] {line}")
                return indented_print
            
            node_print = make_indented_print(captured_color)
            vero_agent._thread_local.node_color = captured_color
            vero_agent._thread_local.console_print = original_console_print
            
            def thread_aware_print(*args, **kwargs):
                if hasattr(vero_agent._thread_local, 'node_color'):
                    color = vero_agent._thread_local.node_color
                    console_print = vero_agent._thread_local.console_print
                    message = " ".join(str(arg) for arg in args)
                    lines = message.split('\n')
                    with vero_agent._console_lock:
                        for line in lines:
                            console_print(f"  [{color}]│[/{color}] {line}")
                else:
                    original_print(*args, **kwargs)
            
            orig_builtin_print = builtins.print
            builtins.print = thread_aware_print
            console.print = lambda *args, **kwargs: node_print(*args, **kwargs)
            
            try:
                result = await func(*args, **kwargs)
                
                elapsed = time.time() - start_time
                time_str = f"{elapsed:.2f}s" if elapsed < 1 else f"{int(elapsed)}s"
                
                builtins.print = orig_builtin_print
                console.print = original_console_print
                
                if hasattr(vero_agent._thread_local, 'node_color'):
                    del vero_agent._thread_local.node_color
                if hasattr(vero_agent._thread_local, 'console_print'):
                    del vero_agent._thread_local.console_print
                
                with vero_agent._tasks_lock:
                    if func_name in vero_agent._active_tasks:
                        del vero_agent._active_tasks[func_name]
                
                end_timestamp = datetime.now().strftime("%H:%M:%S")
                with vero_agent._console_lock:
                    console.print(f"[{node_color}]✓ {readable_name} [/{node_color}][dim]completed in {time_str} at {end_timestamp}[/dim]\n")
                
                return result
            except Exception as e:
                builtins.print = orig_builtin_print
                console.print = original_console_print
                
                if hasattr(vero_agent._thread_local, 'node_color'):
                    del vero_agent._thread_local.node_color
                if hasattr(vero_agent._thread_local, 'console_print'):
                    del vero_agent._thread_local.console_print
                
                elapsed = time.time() - start_time
                time_str = f"{int(elapsed)}s" if elapsed >= 1 else f"{elapsed:.2f}s"
                
                with vero_agent._tasks_lock:
                    if func_name in vero_agent._active_tasks:
                        del vero_agent._active_tasks[func_name]
                
                end_timestamp = datetime.now().strftime("%H:%M:%S")
                with vero_agent._console_lock:
                    console.print(f"[red]✗ {readable_name}[/red] [dim]failed in {time_str} at {end_timestamp}[/dim]")
                    console.print(f"  [dim red]Error: {str(e)}[/dim red]\n")
                raise e
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            action_verbs = [
                "Analyzing", "Processing", "Computing", "Evaluating",
                "Optimizing", "Synthesizing", "Reasoning", "Learning"
            ]
            
            func_name = func.__name__
            readable_name = ' '.join(word.capitalize() for word in func_name.split('_'))
            
            # Assign color to this function if not already assigned
            if func_name not in vero_agent._assigned_colors:
                vero_agent._assigned_colors[func_name] = vero_agent._available_colors[
                    vero_agent._color_index % len(vero_agent._available_colors)
                ]
                vero_agent._color_index += 1
            node_color = vero_agent._assigned_colors[func_name]
            
            action = message if message else random.choice(action_verbs)
            action_base = action.rstrip(".")
            
            console = Console()
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            # Build and print header
            header_lines = []
            header_lines.append(f"\n[{node_color}]● {readable_name}[/{node_color}] [dim]started at {timestamp}[/dim]")
            
            if func.__doc__:
                doc_lines = func.__doc__.strip().split('\n')
                for doc_line in doc_lines:
                    header_lines.append(f"  [dim italic]{doc_line.strip()}[/dim italic]")
            
            spinner_chars = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
            spinner_char = spinner_chars[int(time.time() * 10) % len(spinner_chars)]
            header_lines.append(f"  [{node_color}]{spinner_char}[/{node_color}] [italic]{action_base.lower()}[/italic]")
            
            with vero_agent._console_lock:
                console.print("\n".join(header_lines))
            
            with vero_agent._tasks_lock:
                vero_agent._active_tasks[func_name] = {
                    'name': readable_name,
                    'action': action_base.lower(),
                    'color': node_color,
                    'start_time': time.time()
                }
            
            start_time = time.time()
            original_print = print
            original_console_print = console.print
            captured_color = node_color
            
            def make_indented_print(color):
                def indented_print(*args, **kwargs):
                    if len(args) == 1 and hasattr(args[0], '__rich_console__'):
                        padded = Padding(args[0], pad=(0, 0, 0, 4))
                        with vero_agent._console_lock:
                            original_console_print(padded)
                    else:
                        message = " ".join(str(arg) for arg in args)
                        lines = message.split('\n')
                        with vero_agent._console_lock:
                            for line in lines:
                                original_console_print(f"  [{color}]│[/{color}] {line}")
                return indented_print
            
            node_print = make_indented_print(captured_color)
            vero_agent._thread_local.node_color = captured_color
            vero_agent._thread_local.console_print = original_console_print
            
            def thread_aware_print(*args, **kwargs):
                if hasattr(vero_agent._thread_local, 'node_color'):
                    color = vero_agent._thread_local.node_color
                    console_print = vero_agent._thread_local.console_print
                    message = " ".join(str(arg) for arg in args)
                    lines = message.split('\n')
                    with vero_agent._console_lock:
                        for line in lines:
                            console_print(f"  [{color}]│[/{color}] {line}")
                else:
                    original_print(*args, **kwargs)
            
            orig_builtin_print = builtins.print
            builtins.print = thread_aware_print
            console.print = lambda *args, **kwargs: node_print(*args, **kwargs)
            
            try:
                result = func(*args, **kwargs)
                
                elapsed = time.time() - start_time
                time_str = f"{elapsed:.2f}s" if elapsed < 1 else f"{int(elapsed)}s"
                
                builtins.print = orig_builtin_print
                console.print = original_console_print
                
                if hasattr(vero_agent._thread_local, 'node_color'):
                    del vero_agent._thread_local.node_color
                if hasattr(vero_agent._thread_local, 'console_print'):
                    del vero_agent._thread_local.console_print
                
                with vero_agent._tasks_lock:
                    if func_name in vero_agent._active_tasks:
                        del vero_agent._active_tasks[func_name]
                
                end_timestamp = datetime.now().strftime("%H:%M:%S")
                with vero_agent._console_lock:
                    console.print(f"[{node_color}]✓ {readable_name}[/{node_color}] [dim]completed in {time_str} at {end_timestamp}[/dim]\n")
                
                return result
            except Exception as e:
                builtins.print = orig_builtin_print
                console.print = original_console_print
                
                if hasattr(vero_agent._thread_local, 'node_color'):
                    del vero_agent._thread_local.node_color
                if hasattr(vero_agent._thread_local, 'console_print'):
                    del vero_agent._thread_local.console_print
                
                elapsed = time.time() - start_time
                time_str = f"{int(elapsed)}s" if elapsed >= 1 else f"{elapsed:.2f}s"
                
                with vero_agent._tasks_lock:
                    if func_name in vero_agent._active_tasks:
                        del vero_agent._active_tasks[func_name]
                
                end_timestamp = datetime.now().strftime("%H:%M:%S")
                with vero_agent._console_lock:
                    console.print(f"[red]✗ {readable_name}[/red] [dim]failed in {time_str} at {end_timestamp}[/dim]")
                    console.print(f"  [dim red]Error: {str(e)}[/dim red]\n")
                raise e
                
        return async_wrapper if is_async else sync_wrapper
    return decorator

# ┌────────────────────────────────────────────────────────────────────┐
# │                          SEARCH SCOPE                               │
# └────────────────────────────────────────────────────────────────────┘

class Plan(BaseModel):
    tasks: List[str] = Field(default_factory=list, description="The tasks to be completed by the analyst")
    model_config = {"extra": "forbid"}

class PlanAssessment(BaseModel):
    create_new: bool = Field(description="True to create new framework, False to use existing")
    selected_plan_id: Optional[str] = Field(None, description="Plan ID if using existing")
    justification: str = Field(description="Reasoning for recommendation")
    model_config = {"extra": "forbid"}

class PlanFramework(BaseModel):
    goal: str = Field(description="Clear description of what this framework achieves")
    system_prompt: str = Field(description="The eternal system prompt for this framework")
    model_config = {"extra": "forbid"}

# ┌────────────────────────────────────────────────────────────────────┐
# │                         DELEGATE MODELS                             │
# └────────────────────────────────────────────────────────────────────┘

class AnalystState(BaseModel):
    """State class for the analyst workflow."""
    messages: Annotated[List[BaseMessage], add_messages] = Field(default_factory=list)
    business_question: str = Field(description="The business question the analyst needs to answer")
    name_analyst: str = Field(description="The name of the analyst")
    age_analyst: int = Field(description="The age of the analyst")
    title_analyst: str = Field(description="The title of the analyst")
    background_analyst: str = Field(description="The background/expertise/education of the analyst")
    successful_tools: dict[str, dict[str, Any]] = Field(default_factory=dict, description="The tools used by the analyst")
    director_id: Optional[str] = Field(None, description="The ID of the director overseeing this analyst.")
    thread_id: Optional[str] = Field(None, description="The LangGraph thread ID for state persistence and monitoring.")
    retrieved_context: Optional[str] = None
    rag_answer: Optional[str] = None
    # Deep search fields
    tasks: List[str] = Field(default_factory=list, description="List of research tasks for deep search")
    results: List[str] = Field(default_factory=list, description="List of results from deep search")
    summarized_tasks: str = Field(default="", description="Summarized tasks from deep search")
    summarized_results: str = Field(default="", description="Summarized results from deep search") 
    operations: int = Field(default=0, description="Number of operations performed in deep search")
    max_operations: int = Field(default=7, description="Maximum operations for deep search")
    tool_logs: List[List[Dict[str, str]]] = Field(default_factory=list, description="Tool logs used in deep search")
    next: str = Field(default="", description="Next step in deep search workflow")
    initial_prediction_done: bool = Field(default=False, description="Flag to ensure initial price check and prediction is done only once")
    current_target_value: Optional[float] = Field(default=None, description="Current target value from initial prediction")
    predicted_target_value: Optional[float] = Field(default=None, description="Predicted target value from initial prediction")

class AnalystList(BaseModel):
    analysts: List[AnalystState] = Field(default_factory=list, description="A list of analysts with their assigned tasks and personas.")

    model_config = {"extra": "forbid"}

class FeatureRequest(BaseModel):
    feature_request: str = Field(description="The feature request to be added to the list")
    feature_type: Literal["data", "feature"] = Field(description="The type of feature request")

    model_config = {"extra": "forbid"}

class FeatureRequestList(BaseModel):
    feature_requests: List[FeatureRequest] = Field(description="A list of feature requests")

    model_config = {"extra": "forbid"}

class InitialPrediction(BaseModel):
    """Model for extracting initial prediction from tool outputs (one-time use)"""
    latest_closing_price: float = Field(description="The latest closing price of the stock")
    latest_closing_date: str = Field(description="The date of the latest closing price in YYYY-MM-DD format")
    initial_prediction: float = Field(description="Initial price prediction based on analysis")
    prediction_reasoning: str = Field(description="Brief reasoning for the initial prediction")

class AnalysisReport(BaseModel):
    analysis_report_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique identifier for the report")
    business_question: str = Field(description="The business question the analyst needs to answer")
    business_answer: str = Field(description="The business answer to the question")
    target_column: str = Field(description="The target column of the report", examples=["closing price", "volume", "RSI"])
    confidence: int = Field(description="The confidence level of the report", ge=0, le=100)
    conclusion: Literal["BUY", "SELL", "HOLD"] = Field(description="The conclusion of the report", examples=["BUY", "SELL", "HOLD"])
    observation: str = Field(description="The observation of the report")
    current_target_value: float = Field(description="The current target value of the stock")
    predicted_target_value: float = Field(description="The predicted target value of the stock. If no prediction is available, this will be the same as current_target_value")
    current_date: str = Field(description="The current date")
    horizon_date: str = Field(description="The horizon date")
    argument_for_conclusion: str = Field(description="The arguments for the conclusion")
    argument_against_conclusion: str = Field(description="The arguments against the conclusion")

class ReferenceList(BaseModel):
    references: List[str] = Field(
        description="A list of references",
        examples=['67f701257d751f9f7924abc7', '67f701257d751f9f7924abc8']
    )

    model_config = {"extra": "forbid"}

# ┌────────────────────────────────────────────────────────────────────┐
# │                    SUMMARIZE REPORT MODELS                          │
# └────────────────────────────────────────────────────────────────────┘

class StructuredReport(BaseModel):
    ticker: str = Field(...,
        description="The stock ticker symbol",
        examples=["AAPL", "MSFT", "TSLA"])

    last_closing_price: float = Field(...,
        description="The last recorded closing price of the stock",
        examples=[150.23, 325.75])

    closing_price_date: date = Field(...,
        description="The date of the last closing price",
        examples=["2023-11-15", "2023-12-01"])

    forecasted_price: float = Field(...,
        description="The forecasted price target by the director",
        examples=[175.50, 310.25])

    forecasted_horizon: date = Field(...,
        description="The target date for the forecasted price",
        examples=["2023-12-15", "2024-01-31"])

    recommendation: Literal["BUY", "SELL", "HOLD"] = Field(...,
        description="The director's investment recommendation",
        examples=["BUY", "SELL", "HOLD"])

    confidence_level: int = Field(...,
        description="Confidence level in the prediction (0-100)",
        ge=0, le=100,
        examples=[85, 70])

    report_is_valid: bool = Field(True,
        description="Whether all fields in the report are valid and reasonable. Set to False if any validation fails.",
        examples=[True, False])

    report_is_valid_reasoning: str = Field(...,
        description="Detailed reasoning explaining why report_is_valid was set to true or false. Must explain the validation decision with specific reasons.",
        examples=[
            "Report is valid: All required fields are present with realistic values. TSLA ticker is valid, prices are reasonable ($245.67 current, $280.00 forecast), dates are properly formatted and logical (2025-01-15 < 2025-02-15), BUY recommendation is supported by analysis, and confidence level of 75% is appropriate.",
            "Report is invalid: Missing forecasted price (shows $0.00), which is a critical field required for investment decisions. All other fields are properly formatted but the missing price target makes the report unusable for scheduling revisits.",
            "Report is valid: HOLD recommendation with good quality analysis. Current price $150.89, forecast $152.00, dates are valid (2025-01-10 to 2025-02-10), confidence 60% is reasonable for a conservative HOLD position. HOLD recommendations should not be rejected when analysis quality is sufficient."
        ])

    model_config = {"extra": "forbid"}

# ┌────────────────────────────────────────────────────────────────────┐
# │                        MAIN STATE MODEL                             │
# └────────────────────────────────────────────────────────────────────┘

class DirectorState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    director_id: str
    thread_id: str
    business_question: str
    critical_thinking_search: str
    critical_thinking_planning_result: str
    director_profile: Dict[str, Any]
    plan: Plan
    analysts: AnalystList
    # Changed to list of strings for research summaries
    completed_reports: Annotated[List[str], add_messages]
    embeddings_analysis_report: dict[str, list[float]]
    structured_report: StructuredReport
    professional_report: Optional[str]
    final_report: str
    judge_thinking: Optional[str]
    # Plan forge state
    create_new_plan: bool
    selected_plan_id: Optional[str]
    system_prompt_plan: Optional[str]
    # Tool logs from all analysts
    tool_logs: Annotated[List[Dict[str, Any]], aggregate_tool_logs]

# ┌────────────────────────────────────────────────────────────────────┐
# │                   GRAPH OUTPUTS AND EXPORTS                         │
# └────────────────────────────────────────────────────────────────────┘

class AnalystOutput(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    completed_reports: List[str]  # Changed from AnalysisReport to string
    retrieved_context: Optional[str] = None
    rag_answer: Optional[str] = None
    tool_logs: Optional[Dict[str, Any]] = None

__all__ = ['DirectorState', 'AnalystState', 'AnalystList', 'AnalystOutput',
           'Plan', 'PlanAssessment', 'PlanFramework',
           'FeatureRequest', 'FeatureRequestList', 'ReferenceList', 'AnalysisReport', 'InitialPrediction',
           'StructuredReport',
           'print_step', 'print_pretty', 'print_debug', 'print_markdown', 'vero_agent', 'research_repo', 'console']