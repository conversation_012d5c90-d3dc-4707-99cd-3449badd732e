from typing import Dict, Any, List, TypedDict
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import create_react_agent
from software.ai.llm.llm_connect import get_llm_connect
from software.ai.graph.mongodbsaver import VeroMongoDBSaver
from software.ai.graph.director_state import vero_agent
from pydantic import BaseModel, Field


@tool
def python_sandbox(code: str) -> str:
    """Execute Python code in a sandbox environment.

    Args:
        code: Python code to execute

    Returns:
        The output of the code execution
    """
    try:
        namespace = {}
        exec(code, namespace)

        if 'result' in namespace:
            return str(namespace['result'])
        return "Code executed successfully"
    except Exception as e:
        return f"Error: {str(e)}"


class VeroAgent:
    """Self-improving ReAct agent using iterative prompt optimization"""

    class Evaluation(BaseModel):
        """Generic evaluation model for responses and prompts"""
        score: float = Field(ge=0, le=100, description="Score from 0-100")
        reasoning: str = Field(description="Reasoning for the score")

    class OptimizerState(TypedDict):
        """State for prompt optimization workflow"""
        prompts: List[str]
        task: str
        conversation: List[BaseMessage]
        scores: List[float]
        feedback: str
        eval_parameters: str
        expected_results: str
        scores_response: List[float]
        scores_chain_of_thought: List[float]
        chain_of_thought_reasoning: str
        optimized: bool
        score_threshold: int
        max_attempts: int
        attempts: int

    def __init__(self, model, tools=None, checkpointer=None):
        self.model = model
        self.tools = tools
        self.checkpointer = checkpointer
        self._graph = None

    async def tune(self, initial_prompt: str, task: str, eval_parameters: str, expected_results: str,
                   config: Dict[str, Any] = None, score_threshold: int = 100, max_attempts: int = 3) -> Dict[str, Any]:
        """Tune the agent's prompt for optimal task performance."""
        if self._graph is None:
            self._graph = self.create_graph()

        initial_state = {
            "prompts": [initial_prompt],
            "task": task,
            "conversation": [],
            "scores": [],
            "feedback": "",
            "optimized": False,
            "score_threshold": score_threshold,
            "max_attempts": max_attempts,
            "attempts": 0,
            "eval_parameters": eval_parameters,
            "expected_results": expected_results,
            "scores_response": [],
            "scores_chain_of_thought": [],
            "chain_of_thought_reasoning": ""
        }

        return await self._graph.ainvoke(initial_state, config)
    
    @vero_agent("Running Agent")
    async def run_react_agent(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Run the react agent with current prompt"""
        prompts = state["prompts"]
        current_prompt = prompts[-1]
        task = state["task"]

        print(f"\n[PROMPT] Using prompt: {current_prompt}")
        print(f"[INFO] Attempt {len(prompts)} of {state['max_attempts']}")

        # Create a new agent with the current prompt for each attempt
        def prompt_func(state):
            return [{"role": "system", "content": current_prompt}] + state["messages"]

        agent_executor = create_react_agent(
            model=self.model,
            tools=self.tools,
            prompt=prompt_func
        )

        # Run the agent with clean messages
        result = await agent_executor.ainvoke({
            "messages": [("user", task)]
        })

        print(f"\n[AGENT RESPONSE]\n{result['messages'][-1].content}")

        # Add to conversation for evaluation
        conversation = state["conversation"]
        conversation.extend(result["messages"])

        return {"conversation": conversation}

    @vero_agent("Evaluating Response")
    async def evaluate_response(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Evaluate if agent gave correct response"""
        conversation = state["conversation"]
        task = state["task"]
        expected_results = state["expected_results"]

        eval_system = SystemMessage(content=f"""Evaluate the agent's response against the expected results.

Task: {task}
Expected results: {expected_results}
Agent's response: {conversation[-1].content if conversation else "No response"}

Provide a score from 0-100 and explain your reasoning.
<output_format>
{self.Evaluation.model_json_schema()}
</output_format>
""")

        eval_response = await self.model.ainvoke([eval_system])

        # Parse the response
        try:
            lines = eval_response.content.split('\n')
            score_line = next(line for line in lines if line.startswith('SCORE:'))
            reasoning_line = next(line for line in lines if line.startswith('REASONING:'))

            score = float(score_line.split(':', 1)[1].strip())
            reasoning = reasoning_line.split(':', 1)[1].strip()

            evaluation = self.Evaluation(score=score, reasoning=reasoning)
        except:
            evaluation = self.Evaluation(score=0, reasoning="Failed to parse evaluation response")

        print(f"\n[RESPONSE SCORE] {evaluation.score}/100")
        print(f"\n[RESPONSE EVALUATION]")
        print(evaluation.reasoning)

        scores_response = state["scores_response"]
        scores_response.append(evaluation.score)

        return {
            "conversation": conversation,
            "scores_response": scores_response
        }

    @vero_agent("Evaluating Chain of Thought")
    async def evaluate_chain_of_thought(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Evaluate the entire chain of thought from react agent"""
        conversation = state["conversation"]
        task = state["task"]
        expected_results = state["expected_results"]

        # Get all agent messages (not just the last one)
        print("print raw conversation", conversation)
        
        agent_messages = [msg.content for msg in conversation if hasattr(msg, 'content')]
        full_chain_of_thought = "\n".join(agent_messages)

        eval_system = SystemMessage(content=f"""Evaluate the agent's entire chain of thought and reasoning process.

Task: {task}
Expected results: {expected_results}
Agent's full chain of thought:
{full_chain_of_thought}

Evaluate how well the agent reasoned through the problem, used tools, and arrived at the answer.
Consider: logical flow, tool usage, error correction, and overall reasoning quality.

<output_format>
{self.Evaluation.model_json_schema()}
</output_format>""")

        eval_response = await self.model.ainvoke([eval_system])

        # Parse the response
        try:
            lines = eval_response.content.split('\n')
            score_line = next(line for line in lines if line.startswith('SCORE:'))
            reasoning_line = next(line for line in lines if line.startswith('REASONING:'))

            score = float(score_line.split(':', 1)[1].strip())
            reasoning = reasoning_line.split(':', 1)[1].strip()

            evaluation = self.Evaluation(score=score, reasoning=reasoning)
        except:
            evaluation = self.Evaluation(score=0, reasoning="Failed to parse chain of thought evaluation")

        print(f"\n[CHAIN OF THOUGHT SCORE] {evaluation.score}/100")
        print(f"\n[CHAIN OF THOUGHT EVALUATION]")
        print(evaluation.reasoning)

        scores_chain_of_thought = state.get("scores_chain_of_thought", [])
        scores_chain_of_thought.append(evaluation.score)

        return {
            "scores_chain_of_thought": scores_chain_of_thought,
            "chain_of_thought_reasoning": evaluation.reasoning
        }

    @vero_agent("Evaluating Prompt")
    async def evaluate_prompt(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Evaluate if optimized prompt complies with requirements"""
        current_prompt = state["prompts"][-1]
        eval_parameters = state["eval_parameters"]

        eval_system = SystemMessage(content=f"""Evaluate the system prompt against the requirements.

System prompt: {current_prompt}
Requirements: {eval_parameters}

<output_format>
{self.Evaluation.model_json_schema()}
</output_format>""")

        eval_response = await self.model.ainvoke([eval_system])

        # Parse the response
        try:
            lines = eval_response.content.split('\n')
            score_line = next(line for line in lines if line.startswith('SCORE:'))
            reasoning_line = next(line for line in lines if line.startswith('REASONING:'))

            score = float(score_line.split(':', 1)[1].strip())
            reasoning = reasoning_line.split(':', 1)[1].strip()

            evaluation = self.Evaluation(score=score, reasoning=reasoning)
        except:
            evaluation = self.Evaluation(score=0, reasoning="Failed to parse prompt evaluation")

        print(f"\n[PROMPT SCORE] {evaluation.score}/100")
        print(f"\n[PROMPT EVALUATION]")
        print(evaluation.reasoning)

        scores = state["scores"]
        scores.append(evaluation.score)
        attempts = state["attempts"] + 1

        return {
            "scores": scores,
            "attempts": attempts
        }

    @vero_agent("Generating Feedback")
    async def generate_feedback(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Generate feedback based on all evaluations"""
        eval_parameters = state["eval_parameters"]
        expected_results = state["expected_results"]
        scores_response = state["scores_response"]
        scores_chain_of_thought = state["scores_chain_of_thought"]
        chain_of_thought_reasoning = state["chain_of_thought_reasoning"]
        scores = state["scores"]

        # Get latest scores
        latest_response_score = scores_response[-1] if scores_response else 0
        latest_cot_score = scores_chain_of_thought[-1] if scores_chain_of_thought else 0
        latest_prompt_score = scores[-1] if scores else 0

        feedback_system = SystemMessage(content=f"""Generate human-like feedback to improve the system prompt.

Requirements: {eval_parameters}
Expected results: {expected_results}

Current performance:
- Response accuracy: {latest_response_score}/100
- Chain of thought quality: {latest_cot_score}/100
- Prompt compliance: {latest_prompt_score}/100

Chain of thought analysis: {chain_of_thought_reasoning}

Provide specific, actionable feedback on how to improve the system prompt.""")

        feedback_human = HumanMessage(content="What specific improvements should be made to the system prompt?")

        feedback_response = await self.model.ainvoke([feedback_system, feedback_human])
        feedback = feedback_response.content

        print(f"\n[FEEDBACK GENERATED]")
        print(feedback)

        return {"feedback": feedback}

    @vero_agent("Optimizing")
    async def optimize_prompt(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Optimize prompt using agent swarm for idea exchange"""
        feedback = state["feedback"]

        if not feedback:
            return {"optimized": True}

        prompts = state["prompts"]
        current_prompt = prompts[-1]
        eval_parameters = state["eval_parameters"]

        # Get available tools dynamically
        available_tools = []
        if self.tools:
            for tool in self.tools:
                available_tools.append(f"- {tool.name}: {tool.description}")
        tools_list = "\n".join(available_tools)

        # Simple single-agent optimization
        optimize_system = SystemMessage(content=f"""Current prompt: {current_prompt}
Feedback: {feedback}
Requirements: {eval_parameters}
Available tools: {tools_list}

Create an improved system prompt. Provide only the prompt, nothing else.""")

        optimize_human = HumanMessage(content="Create the optimized prompt.")
        optimize_response = await self.model.ainvoke([optimize_system, optimize_human])
        new_prompt = optimize_response.content
        prompts.append(new_prompt)

        print(f"\n[NEW OPTIMIZED PROMPT]")
        print(new_prompt)

        return {
            "prompts": prompts,
            "optimized": True
        }

    def should_continue(self, state: "VeroAgent.OptimizerState") -> str:
        """Decide whether to continue optimization"""
        max_attempts = state["max_attempts"]
        attempts = state["attempts"]
        scores_response = state["scores_response"]
        scores = state["scores"]
        score_threshold = state["score_threshold"]

        # Success - both response and prompt scores reach threshold
        response_good = len(scores_response) > 0 and scores_response[-1] >= score_threshold
        prompt_good = len(scores) > 0 and scores[-1] >= score_threshold

        if response_good and prompt_good:
            print(f"\n[SUCCESS] Both scores reached threshold - Response: {scores_response[-1]}, Prompt: {scores[-1]}")
            return "end"

        # Max attempts reached
        if attempts >= max_attempts:
            return "end"

        return "optimize"

    def create_graph(self) -> StateGraph:
        """Create optimizer graph with 6 nodes"""
        builder = StateGraph(self.OptimizerState)

        builder.add_node("run_agent", self.run_react_agent)
        builder.add_node("evaluate_response", self.evaluate_response)
        builder.add_node("evaluate_chain_of_thought", self.evaluate_chain_of_thought)
        builder.add_node("provide_feedback", self.generate_feedback)
        builder.add_node("optimize", self.optimize_prompt)
        builder.add_node("evaluate_prompt", self.evaluate_prompt)

        builder.add_edge(START, "run_agent")
        builder.add_edge("run_agent", "evaluate_response")
        builder.add_edge("evaluate_response", "evaluate_chain_of_thought")
        builder.add_edge("evaluate_chain_of_thought", "evaluate_prompt")
        builder.add_edge("evaluate_prompt", "provide_feedback")

        builder.add_conditional_edges(
            "provide_feedback",
            self.should_continue,
            {
                "optimize": "optimize",
                "end": END
            }
        )

        builder.add_edge("optimize", "run_agent")

        return builder.compile(checkpointer=self.checkpointer)


async def main():
    """Demonstrate VeroAgent with self-improvement"""
    print("🤖 VeroAgent Demo")
    print("=" * 40)

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()

    agent = VeroAgent(
        model=model,
        tools=[python_sandbox],
        checkpointer=VeroMongoDBSaver()
    )

    result = await agent.tune(
        initial_prompt="You are helpful assistant.",
        task="How many words in this sentence, given we are now in 2025?",
        eval_parameters="Must mention available tools, function calling, and use XML format • Must not leak the task or an example in the prompt, as well as the expected answer.",
        expected_results="The exact answer is 11 words",
        config={
            "configurable": {"thread_id": "vero-agent-test"},
            "recursion_limit": 50
        },
        score_threshold=100,
        max_attempts=3
    )

    # Get best results
    prompts = result['prompts']

    print(f"\n[FINAL RESULTS]")
    print(f"Total attempts: {len(prompts)}")
    print(f"\n[FINAL PROMPT]")
    print(prompts[-1])


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
