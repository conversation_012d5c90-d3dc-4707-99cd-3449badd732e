from typing import Dict, Any, List, TypedDict
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import InMemorySaver
from langgraph_swarm import create_handoff_tool, create_swarm
from software.ai.llm.llm_connect import get_llm_connect
from software.ai.graph.mongodbsaver import VeroMongoDBSaver
from software.ai.graph.director_state import vero_agent
from pydantic import BaseModel, Field


@tool
def python_sandbox(code: str) -> str:
    """Execute Python code in a sandbox environment.

    Args:
        code: Python code to execute

    Returns:
        The output of the code execution
    """
    try:
        namespace = {}
        exec(code, namespace)

        if 'result' in namespace:
            return str(namespace['result'])
        return "Code executed successfully"
    except Exception as e:
        return f"Error: {str(e)}"


class VeroAgent:
    """Self-improving ReAct agent using iterative prompt optimization"""

    class ResponseEvaluation(BaseModel):
        """Response evaluation model"""
        score: float = Field(ge=0, le=100, description="Response score from 0-100")
        reasoning: str = Field(description="Reasoning for the score")

    class OptimizerState(TypedDict):
        """State for prompt optimization workflow"""
        prompts: List[str]
        task: str
        conversation: List[BaseMessage]
        scores: List[float]
        feedback: str
        eval_parameters: str
        expected_results: str
        scores_response: List[float]
        optimized: bool
        score_threshold: int
        max_attempts: int
        attempts: int

    def __init__(self, model, tools=None, checkpointer=None):
        self.model = model
        self.tools = tools
        self.checkpointer = checkpointer
        self._graph = None

    async def tune(self, initial_prompt: str, task: str, eval_parameters: str, expected_results: str,
                   config: Dict[str, Any] = None, score_threshold: int = 100, max_attempts: int = 3) -> Dict[str, Any]:
        """Tune the agent's prompt for optimal task performance."""
        if self._graph is None:
            self._graph = self.create_graph()

        initial_state = {
            "prompts": [initial_prompt],
            "task": task,
            "conversation": [],
            "scores": [],
            "feedback": "",
            "optimized": False,
            "score_threshold": score_threshold,
            "max_attempts": max_attempts,
            "attempts": 0,
            "eval_parameters": eval_parameters,
            "expected_results": expected_results,
            "scores_response": []
        }

        return await self._graph.ainvoke(initial_state, config)
    
    @vero_agent("Running Agent")
    async def run_react_agent(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Run the react agent with current prompt"""
        prompts = state["prompts"]
        current_prompt = prompts[-1]
        task = state["task"]

        print(f"\n[PROMPT] Using prompt: {current_prompt}")
        print(f"[INFO] Attempt {len(prompts)} of {state['max_attempts']}")

        # Create a new agent with the current prompt for each attempt
        def prompt_func(state):
            return [{"role": "system", "content": current_prompt}] + state["messages"]

        agent_executor = create_react_agent(
            model=self.model,
            tools=self.tools,
            prompt=prompt_func
        )

        # Run the agent with clean messages
        result = await agent_executor.ainvoke({
            "messages": [("user", task)]
        })

        print(f"\n[AGENT RESPONSE]\n{result['messages'][-1].content}")

        # Add to conversation for evaluation
        conversation = state["conversation"]
        conversation.extend(result["messages"])

        return {"conversation": conversation}

    @vero_agent("Evaluating Response")
    async def evaluate_response(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Evaluate if agent gave correct response"""
        conversation = state["conversation"]
        task = state["task"]
        expected_results = state["expected_results"]

        eval_prompt = f"""Evaluate the agent's response against the expected results.

Task: {task}
Expected results: {expected_results}

Provide a score from 0-100 and explain your reasoning."""

        structured_model = self.model.with_structured_output(self.ResponseEvaluation)
        evaluation = await structured_model.ainvoke([HumanMessage(content=eval_prompt)])

        if evaluation is None:
            evaluation = self.ResponseEvaluation(score=0, reasoning="Failed to evaluate response")

        print(f"\n[RESPONSE SCORE] {evaluation.score}/100")
        print(f"\n[RESPONSE EVALUATION]")
        print(evaluation.reasoning)

        scores_response = state["scores_response"]
        scores_response.append(evaluation.score)

        return {
            "conversation": conversation,
            "scores_response": scores_response
        }

    @vero_agent("Evaluating Prompt")
    async def evaluate_prompt(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Evaluate if optimized prompt complies with requirements"""
        current_prompt = state["prompts"][-1]
        eval_parameters = state["eval_parameters"]

        eval_system = SystemMessage(content=f"""System prompt: {current_prompt}
Requirements: {eval_parameters}

Score 0-100 based on prompt compliance with requirements.""")

        eval_human = HumanMessage(content="Evaluate prompt compliance with requirements.")
        eval_response = await self.model.ainvoke([eval_system, eval_human])

        print(f"\n[PROMPT SCORE] N/A")
        print(f"\n[PROMPT EVALUATION]")
        print(eval_response.content)

        attempts = state["attempts"] + 1

        return {
            "attempts": attempts
        }

    @vero_agent("Generating Feedback")
    async def generate_feedback(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Generate feedback based on conversation history"""
        conversation = state["conversation"]
        eval_parameters = state["eval_parameters"]
        expected_results = state["expected_results"]

        feedback_system = SystemMessage(content=f"""Requirements: {eval_parameters}
Expected results: {expected_results}

Provide specific feedback on how to improve the system prompt to meet requirements and achieve expected results.""")

        feedback_human = HumanMessage(content="What specific improvement should be made to the system prompt?")

        messages_for_feedback = [feedback_system] + conversation + [feedback_human]

        feedback_response = await self.model.ainvoke(messages_for_feedback)
        feedback = feedback_response.content

        print(f"\n[FEEDBACK GENERATED]")
        print(feedback)

        return {"feedback": feedback}

    @vero_agent("Optimizing")
    async def optimize_prompt(self, state: "VeroAgent.OptimizerState") -> Dict[str, Any]:
        """Optimize prompt using agent swarm for idea exchange"""
        feedback = state["feedback"]

        if not feedback:
            return {"optimized": True}

        prompts = state["prompts"]
        current_prompt = prompts[-1]
        eval_parameters = state["eval_parameters"]

        # Get available tools dynamically
        available_tools = []
        if self.tools:
            for tool in self.tools:
                available_tools.append(f"- {tool.name}: {tool.description}")
        tools_list = "\n".join(available_tools)

        # Create swarm agents for optimization
        agent1 = create_react_agent(
            self.model,
            [create_handoff_tool(agent_name="agent2")],
            prompt=f"""You are Agent1, a prompt optimization expert.
Current prompt: {current_prompt}
Feedback: {feedback}
Requirements: {eval_parameters}
Available tools: {tools_list}

Analyze and improve the prompt. When you have insights, share with Agent2.""",
            name="agent1",
        )

        agent2 = create_react_agent(
            self.model,
            [create_handoff_tool(agent_name="agent1")],
            prompt=f"""You are Agent2, a prompt optimization expert.
Current prompt: {current_prompt}
Feedback: {feedback}
Requirements: {eval_parameters}
Available tools: {tools_list}

Build on Agent1's insights and create the final optimized prompt.""",
            name="agent2",
        )

        # Create and run swarm
        checkpointer = InMemorySaver()
        workflow = create_swarm([agent1, agent2], default_active_agent="agent1")
        app = workflow.compile(checkpointer=checkpointer)

        config = {"configurable": {"thread_id": "optimize"}}
        result = await app.ainvoke(
            {"messages": [{"role": "user", "content": "Create an improved system prompt"}]},
            config,
        )

        # Extract the optimized prompt from the conversation
        new_prompt = result["messages"][-1].content
        prompts.append(new_prompt)

        print(f"\n[NEW OPTIMIZED PROMPT]")
        print(new_prompt)

        return {
            "prompts": prompts,
            "optimized": True
        }

    def should_continue(self, state: "VeroAgent.OptimizerState") -> str:
        """Decide whether to continue optimization"""
        max_attempts = state["max_attempts"]
        attempts = state["attempts"]
        scores_response = state["scores_response"]
        score_threshold = state["score_threshold"]

        # Success - agent gave correct response
        if len(scores_response) > 0 and scores_response[-1] >= score_threshold:
            print(f"\n[SUCCESS] Agent response score {scores_response[-1]} reached threshold {score_threshold}")
            return "end"

        # Max attempts reached
        if attempts >= max_attempts:
            return "end"

        return "optimize"

    def create_graph(self) -> StateGraph:
        """Create optimizer graph with 5 nodes"""
        builder = StateGraph(self.OptimizerState)

        builder.add_node("run_agent", self.run_react_agent)
        builder.add_node("evaluate_response", self.evaluate_response)
        builder.add_node("provide_feedback", self.generate_feedback)
        builder.add_node("optimize", self.optimize_prompt)
        builder.add_node("evaluate_prompt", self.evaluate_prompt)

        builder.add_edge(START, "run_agent")
        builder.add_edge("run_agent", "evaluate_response")
        builder.add_edge("evaluate_response", "evaluate_prompt")
        builder.add_edge("evaluate_prompt", "provide_feedback")

        builder.add_conditional_edges(
            "provide_feedback",
            self.should_continue,
            {
                "optimize": "optimize",
                "end": END
            }
        )

        builder.add_edge("optimize", "run_agent")

        return builder.compile(checkpointer=self.checkpointer)


async def main():
    """Demonstrate VeroAgent with self-improvement"""
    print("🤖 VeroAgent Demo")
    print("=" * 40)

    llm_connect = get_llm_connect()
    model = await llm_connect.get_llm()

    agent = VeroAgent(
        model=model,
        tools=[python_sandbox],
        checkpointer=VeroMongoDBSaver()
    )

    result = await agent.tune(
        initial_prompt="You are helpful assistant.",
        task="How many words in this sentence, given we are now in 2025?",
        eval_parameters="Must mention available tools, function calling, and use XML format • Must not leak the task or an example in the prompt, as well as the expected answer.",
        expected_results="The exact answer is 11 words",
        config={
            "configurable": {"thread_id": "vero-agent-test"},
            "recursion_limit": 50
        },
        score_threshold=100,
        max_attempts=2
    )

    # Get best results
    prompts = result['prompts']

    print(f"\n[FINAL RESULTS]")
    print(f"Total attempts: {len(prompts)}")
    print(f"\n[FINAL PROMPT]")
    print(prompts[-1])


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
