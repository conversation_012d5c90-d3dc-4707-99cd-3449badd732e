"""
Simple 3-layer nested subgraph test for LangGraph Task Manager
Tests recursive subgraph detection (Main -> Subgraph -> Nested Subgraph)
"""

from langgraph.graph import StateGraph, START, END
from typing import TypedDict
import asyncio


class GraphState(TypedDict):
    messages: str


# --- Level 3: Nested Subgraph (innermost) ---
async def nested_process(state: GraphState) -> GraphState:
    """Process in nested subgraph"""
    await asyncio.sleep(2.0)
    return {
        **state,
        "messages": state["messages"] + " -> Nested processed"
    }


def create_nested_subgraph() -> StateGraph:
    """Create the innermost nested subgraph"""
    nested = StateGraph(GraphState)
    nested.add_node("nested_process", nested_process)
    nested.add_edge(START, "nested_process")
    nested.add_edge("nested_process", END)
    return nested.compile()


# --- Level 2: Middle Subgraph ---
async def middle_start(state: GraphState) -> GraphState:
    """Start middle subgraph"""
    await asyncio.sleep(10.0)
    return {
        **state,
        "messages": state["messages"] + " -> Middle started"
    }


# Create nested subgraph instance
nested_subgraph = create_nested_subgraph()


def create_middle_subgraph() -> StateGraph:
    """Create middle subgraph that contains nested subgraph"""
    middle = StateGraph(GraphState)
    middle.add_node("middle_start", middle_start)
    middle.add_node("nested_subgraph", nested_subgraph)  # Subgraph as node
    middle.add_edge(START, "middle_start")
    middle.add_edge("middle_start", "nested_subgraph")
    middle.add_edge("nested_subgraph", END)
    return middle.compile()


# --- Level 1: Main Graph ---
async def main_process(state: GraphState) -> GraphState:
    """Main graph processing"""
    await asyncio.sleep(0.5)
    return {
        **state,
        "messages": "Main started"
    }


# Create middle subgraph instance
middle_subgraph = create_middle_subgraph()


def create_graph() -> StateGraph:
    """Create main graph with nested subgraphs"""
    workflow = StateGraph(GraphState)
    workflow.add_node("main_process", main_process)
    workflow.add_node("middle_subgraph", middle_subgraph)  # Subgraph as node
    workflow.add_edge(START, "main_process")
    workflow.add_edge("main_process", "middle_subgraph")
    workflow.add_edge("middle_subgraph", END)
    return workflow.compile()


def get_initial_state() -> GraphState:
    """Get initial state for the graph"""
    return {
        "messages": "Test started"
    }


# Create the graph instance
graph = create_graph()