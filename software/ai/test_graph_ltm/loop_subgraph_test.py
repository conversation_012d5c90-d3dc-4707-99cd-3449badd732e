"""
Loop subgraph test for LangGraph Task Manager
Tests loop pattern as a subgraph within a main graph
Main Graph: initialize -> iterative_search (loop subgraph) -> finalize
"""

from langgraph.graph import StateGraph, START, END
from typing import TypedDict, Literal
import asyncio
import random


class LoopState(TypedDict):
    messages: list[str]
    iteration: int
    max_iterations: int


# --- Loop Subgraph Nodes ---
async def search_node(state: LoopState) -> LoopState:
    """Search for information - can be revisited multiple times"""
    iteration = state.get("iteration", 0)
    # Random sleep to simulate work
    await asyncio.sleep(random.uniform(2.0, 4.0))
    
    messages = state.get("messages", [])
    messages.append(f"🔍 Search iteration {iteration + 1}: Looking for new insights")
    
    return {
        **state,
        "messages": messages
    }


async def analyze_node(state: LoopState) -> LoopState:
    """Analyze the search results"""
    # Random sleep to simulate work
    await asyncio.sleep(random.uniform(2.0, 4.0))
    
    messages = state.get("messages", [])
    messages.append(f"📊 Analyzing results from iteration {state['iteration'] + 1}")
    
    return {
        **state,
        "messages": messages
    }


async def decision_node(state: LoopState) -> LoopState:
    """Decide whether to continue searching or finalize"""
    # Random sleep to simulate work
    await asyncio.sleep(random.uniform(2.0, 4.0))
    
    iteration = state.get("iteration", 0) + 1
    max_iterations = state.get("max_iterations", 3)
    
    messages = state.get("messages", [])
    
    # Decide based on iteration count
    if iteration < max_iterations:
        messages.append(f"🔄 Decision: Continue searching (iteration {iteration}/{max_iterations})")
        next_action = "search"
    else:
        messages.append(f"✅ Decision: Sufficient data collected after {iteration} iterations")
        next_action = "complete"
    
    return {
        **state,
        "messages": messages,
        "iteration": iteration,
        "next": next_action
    }


def create_loop_subgraph() -> StateGraph:
    """Create the loop subgraph"""
    loop_graph = StateGraph(LoopState)
    
    # Add nodes
    loop_graph.add_node("search", search_node)
    loop_graph.add_node("analyze", analyze_node)
    loop_graph.add_node("decide", decision_node)
    
    # Add edges - creating a loop pattern
    loop_graph.add_edge(START, "search")
    loop_graph.add_edge("search", "analyze")
    loop_graph.add_edge("analyze", "decide")
    
    # Conditional edge - this creates the loop!
    loop_graph.add_conditional_edges(
        "decide",
        lambda state: state.get("next", "complete"),
        {
            "search": "search",  # Loop back to search
            "complete": END  # Exit the subgraph
        }
    )
    
    return loop_graph.compile()


# --- Main Graph Nodes ---
async def initialize_node(state: LoopState) -> LoopState:
    """Initialize the workflow"""
    # Random sleep to simulate work
    await asyncio.sleep(random.uniform(2.0, 4.0))
    
    messages = state.get("messages", [])
    messages.append("🚀 Workflow initialized, starting iterative search")
    
    return {
        **state,
        "messages": messages
    }


async def finalize_node(state: LoopState) -> LoopState:
    """Finalize the results after loop completion"""
    # Random sleep to simulate work
    await asyncio.sleep(random.uniform(2.0, 4.0))
    
    messages = state.get("messages", [])
    messages.append(f"📝 Finalizing comprehensive report after {state['iteration']} iterations")
    messages.append("🎯 Main workflow with loop subgraph completed successfully!")
    
    return {
        **state,
        "messages": messages
    }


def create_graph() -> StateGraph:
    """Create main graph with loop subgraph"""
    # Create the loop subgraph instance
    loop_subgraph = create_loop_subgraph()
    
    workflow = StateGraph(LoopState)
    
    # Add nodes
    workflow.add_node("initialize", initialize_node)
    workflow.add_node("iterative_search", loop_subgraph)  # Loop subgraph
    workflow.add_node("finalize", finalize_node)
    
    # Add edges - simple linear flow with loop inside
    workflow.add_edge(START, "initialize")
    workflow.add_edge("initialize", "iterative_search")
    workflow.add_edge("iterative_search", "finalize")
    workflow.add_edge("finalize", END)
    
    return workflow.compile()


def get_initial_state() -> LoopState:
    """Get initial state for the graph"""
    return {
        "messages": ["🌟 Starting main workflow with iterative search subgraph"],
        "iteration": 0,
        "max_iterations": 3  # Will loop 3 times
    }


# Create the graph instance
graph = create_graph()

__all__ = ['graph']