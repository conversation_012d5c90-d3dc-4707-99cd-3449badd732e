"""
Parallel execution test graph - simplified version
Tests multiple sequential agents to simulate parallel processing
"""

from langgraph.graph import StateGraph, START, END
from typing import TypedDict, Annotated, Sequence
import asyncio


def add_messages(left: list, right: list) -> list:
    """Add messages from parallel execution"""
    return left + right

class ParallelState(TypedDict):
    messages: Annotated[Sequence[str], add_messages]


async def initialize_parallel(state: ParallelState) -> ParallelState:
    """Initialize parallel processing"""
    print("🚀 Initialize Parallel starting")
    print(f"🔍 Initial state: {state}")
    await asyncio.sleep(3.0)  # 3 seconds for visual detection

    new_state = {
        **state,
        "messages": ["Parallel processing initialized"]
    }

    print("✅ Initialize Parallel completed - LangGraph will now launch all 3 agents in parallel")
    print(f"🔍 New state: {new_state}")
    return new_state


async def agent_1_task(state: ParallelState) -> ParallelState:
    """Agent 1 processing task"""
    print("🚀 Agent 1 starting")
    await asyncio.sleep(4.0)  # 4 seconds for visual detection
    print("✅ Agent 1 completed")
    return {"messages": ["Agent 1 completed analysis"]}


async def agent_2_task(state: ParallelState) -> ParallelState:
    """Agent 2 processing task"""
    print("🚀 Agent 2 starting")
    await asyncio.sleep(5.0)  # 5 seconds for visual detection
    print("✅ Agent 2 completed")
    return {"messages": ["Agent 2 completed research"]}


async def agent_3_task(state: ParallelState) -> ParallelState:
    """Agent 3 processing task"""
    print("🚀 Agent 3 starting")
    await asyncio.sleep(6.0)  # 6 seconds for visual detection
    print("✅ Agent 3 completed")
    return {"messages": ["Agent 3 completed validation"]}



async def finalize_parallel(state: ParallelState) -> ParallelState:
    """Finalize parallel processing results"""
    print("🎯 Finalize Parallel starting")
    print(f"🔍 Received messages: {state['messages']}")
    await asyncio.sleep(3.0)  # 3 seconds for visual detection
    print("✅ Finalize Parallel completed")
    return {"messages": ["All 3 parallel agents completed successfully"]}


def create_graph() -> StateGraph:
    """Create and return the parallel test graph with proper synchronization"""
    workflow = StateGraph(ParallelState)

    # Add nodes
    workflow.add_node("initialize_parallel", initialize_parallel)
    workflow.add_node("agent_1_task", agent_1_task)
    workflow.add_node("agent_2_task", agent_2_task)
    workflow.add_node("agent_3_task", agent_3_task)
    workflow.add_node("finalize_parallel", finalize_parallel)

    # Add edges - parallel execution with join pattern
    workflow.add_edge(START, "initialize_parallel")
    workflow.add_edge("initialize_parallel", "agent_1_task")
    workflow.add_edge("initialize_parallel", "agent_2_task")
    workflow.add_edge("initialize_parallel", "agent_3_task")

    # All agents connect to finalize_parallel - LangGraph will wait for all
    workflow.add_edge("agent_1_task", "finalize_parallel")
    workflow.add_edge("agent_2_task", "finalize_parallel")
    workflow.add_edge("agent_3_task", "finalize_parallel")
    workflow.add_edge("finalize_parallel", END)

    print("🔧 Graph structure created with parallel join pattern")
    print("🔧 Nodes: initialize_parallel -> [agent_1_task, agent_2_task, agent_3_task] -> finalize_parallel -> END")
    print("🔧 LangGraph will automatically wait for ALL agents to complete before proceeding to finalize_parallel")

    compiled_graph = workflow.compile()
    print("✅ Graph compiled successfully")
    return compiled_graph




# Create the graph instance
graph = create_graph()

__all__ = ["graph"]