"""
Edge Connection Test - Tests dynamic edge creation from last node to <PERSON>ND
Simple subgraph to verify that the last real node connects properly to <PERSON><PERSON>
"""

from langgraph.graph import StateGraph, START, END
from typing import TypedDict
import asyncio


class GraphState(TypedDict):
    messages: str


# Simple subgraph with 3 nodes to test edge connections
async def step_one(state: GraphState) -> GraphState:
    """First step in subgraph"""
    await asyncio.sleep(1.0)
    return {
        **state,
        "messages": state["messages"] + " -> Step 1"
    }


async def step_two(state: GraphState) -> GraphState:
    """Second step in subgraph"""
    await asyncio.sleep(1.0)
    return {
        **state,
        "messages": state["messages"] + " -> Step 2"
    }


async def final_step(state: GraphState) -> GraphState:
    """Final step that should connect to END"""
    await asyncio.sleep(1.0)
    return {
        **state,
        "messages": state["messages"] + " -> Final step"
    }


def create_test_subgraph() -> StateGraph:
    """Create a simple subgraph with clear edge pattern"""
    subgraph = StateGraph(GraphState)
    subgraph.add_node("step_one", step_one)
    subgraph.add_node("step_two", step_two)
    subgraph.add_node("final_step", final_step)
    
    # Clear edge pattern: START -> step_one -> step_two -> final_step -> END
    subgraph.add_edge(START, "step_one")
    subgraph.add_edge("step_one", "step_two")
    subgraph.add_edge("step_two", "final_step")
    subgraph.add_edge("final_step", END)
    
    return subgraph.compile()


# Main graph that uses the subgraph
async def main_task(state: GraphState) -> GraphState:
    """Main task before subgraph"""
    await asyncio.sleep(0.5)
    return {
        **state,
        "messages": state["messages"] + " -> Main task"
    }


# Create subgraph instance
test_subgraph = create_test_subgraph()


def create_graph() -> StateGraph:
    """Create main graph with one subgraph to test edge connections"""
    workflow = StateGraph(GraphState)
    workflow.add_node("main_task", main_task)
    workflow.add_node("test_subgraph", test_subgraph)
    
    # Sequential: main_task -> test_subgraph
    workflow.add_edge(START, "main_task")
    workflow.add_edge("main_task", "test_subgraph")
    workflow.add_edge("test_subgraph", END)
    
    return workflow.compile()


def get_initial_state() -> GraphState:
    """Get initial state for the graph"""
    return {
        "messages": "Edge connection test started"
    }


# Create the graph instance
graph = create_graph()