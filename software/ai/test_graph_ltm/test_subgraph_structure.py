"""Test script to explore how LangGraph stores compiled subgraphs"""

import json
from typing import Dict, Any
from langgraph.graph import StateGraph, END
from langgraph.graph.state import CompiledStateGraph
from typing_extensions import TypedDict


class SimpleState(TypedDict):
    value: str
    counter: int


class SubgraphState(TypedDict):
    sub_value: str
    sub_counter: int


def node_a(state: SimpleState) -> SimpleState:
    """Simple node A"""
    return {"value": state["value"] + " -> A", "counter": state["counter"] + 1}


def node_b(state: SimpleState) -> SimpleState:
    """Simple node B"""
    return {"value": state["value"] + " -> B", "counter": state["counter"] + 1}


def sub_node_1(state: SubgraphState) -> SubgraphState:
    """Subgraph node 1"""
    return {"sub_value": state["sub_value"] + " -> Sub1", "sub_counter": state["sub_counter"] + 1}


def sub_node_2(state: SubgraphState) -> SubgraphState:
    """Subgraph node 2"""
    return {"sub_value": state["sub_value"] + " -> Sub2", "sub_counter": state["sub_counter"] + 1}


def explore_graph_structure(graph, graph_name="Graph", indent=0):
    """Recursively explore graph structure"""
    prefix = "  " * indent
    print(f"\n{prefix}=== Exploring {graph_name} ===")
    print(f"{prefix}Type: {type(graph)}")
    print(f"{prefix}Class name: {graph.__class__.__name__}")
    
    # Check various attributes
    attrs_to_check = ['nodes', '_nodes', 'edges', '_edges', '_graph', 'graph', 
                      '_compiled_graph', 'compiled_graph', '__dict__']
    
    for attr in attrs_to_check:
        if hasattr(graph, attr):
            value = getattr(graph, attr)
            print(f"\n{prefix}{attr}: {type(value)}")
            if isinstance(value, dict) and attr in ['nodes', '_nodes']:
                for k, v in value.items():
                    print(f"{prefix}  {k}: {type(v)}")
                    if hasattr(v, '__name__'):
                        print(f"{prefix}    Function name: {v.__name__}")
                    # Check if this node is a compiled graph
                    if hasattr(v, 'nodes') or hasattr(v, '_nodes') or isinstance(v, CompiledStateGraph):
                        print(f"{prefix}    >>> SUBGRAPH DETECTED!")
                        explore_graph_structure(v, f"Subgraph:{k}", indent + 2)
    
    # Print all attributes
    print(f"\n{prefix}All attributes:")
    for attr in dir(graph):
        if not attr.startswith('_'):
            try:
                value = getattr(graph, attr)
                if not callable(value):
                    print(f"{prefix}  {attr}: {type(value)}")
            except:
                pass


def create_subgraph():
    """Create a simple subgraph"""
    subgraph_builder = StateGraph(SubgraphState)
    
    subgraph_builder.add_node("sub_node_1", sub_node_1)
    subgraph_builder.add_node("sub_node_2", sub_node_2)
    
    subgraph_builder.set_entry_point("sub_node_1")
    subgraph_builder.add_edge("sub_node_1", "sub_node_2")
    subgraph_builder.add_edge("sub_node_2", END)
    
    return subgraph_builder.compile()


def main():
    """Main test function"""
    print("Creating main graph with subgraph...")
    
    # Create main graph
    main_graph = StateGraph(SimpleState)
    
    # Add regular nodes
    main_graph.add_node("node_a", node_a)
    main_graph.add_node("node_b", node_b)
    
    # Create and add subgraph
    subgraph = create_subgraph()
    
    # Try different ways of adding subgraph
    # Method 1: Direct assignment
    main_graph.add_node("my_subgraph", subgraph)
    
    # Set up edges
    main_graph.set_entry_point("node_a")
    main_graph.add_edge("node_a", "my_subgraph")
    main_graph.add_edge("my_subgraph", "node_b")
    main_graph.add_edge("node_b", END)
    
    # Compile the graph
    compiled_main = main_graph.compile()
    
    # Explore structure before compilation
    print("\n" + "="*60)
    print("BEFORE COMPILATION (StateGraph):")
    print("="*60)
    explore_graph_structure(main_graph)
    
    # Explore structure after compilation
    print("\n" + "="*60)
    print("AFTER COMPILATION (CompiledStateGraph):")
    print("="*60)
    explore_graph_structure(compiled_main)
    
    # Test the current detection logic
    print("\n" + "="*60)
    print("TESTING CURRENT DETECTION LOGIC:")
    print("="*60)
    
    def test_detection(graph, name):
        print(f"\nTesting {name}:")
        if hasattr(graph, "nodes"):
            print("  Has 'nodes' attribute")
            for node_name, node_value in graph.nodes.items():
                print(f"    {node_name}: {type(node_value)}")
                if hasattr(node_value, 'nodes') or hasattr(node_value, '_nodes'):
                    print(f"      -> Detected as subgraph!")
        elif hasattr(graph, "_nodes"):
            print("  Has '_nodes' attribute")
            for node_name, node_value in graph._nodes.items():
                print(f"    {node_name}: {type(node_value)}")
                if hasattr(node_value, 'nodes') or hasattr(node_value, '_nodes'):
                    print(f"      -> Detected as subgraph!")
    
    test_detection(compiled_main, "Compiled Main Graph")
    
    # Check if we can detect by type
    print("\n" + "="*60)
    print("TYPE-BASED DETECTION:")
    print("="*60)
    print(f"Subgraph type: {type(subgraph)}")
    print(f"Is CompiledStateGraph: {isinstance(subgraph, CompiledStateGraph)}")
    print(f"Main graph type: {type(compiled_main)}")
    print(f"Is CompiledStateGraph: {isinstance(compiled_main, CompiledStateGraph)}")


if __name__ == "__main__":
    main()