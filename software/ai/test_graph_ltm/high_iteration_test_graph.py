"""
High iteration test graph for LangGraph Task Manager
Tests workflows with high iteration counts (20+ iterations)
"""

from langgraph.graph import StateGraph, START, END
from typing import TypedDict, List
import asyncio


class HighIterationState(TypedDict):
    messages: str
    current_iteration: int
    max_iterations: int
    results: List[str]
    processing_time: float
    status: str


async def initialize_high_iteration(state: HighIterationState) -> HighIterationState:
    """Initialize high iteration workflow"""
    await asyncio.sleep(0.3)
    return {
        **state,
        "messages": state["messages"] + " -> High iteration workflow initialized",
        "status": "initialized"
    }


async def iteration_processor(state: HighIterationState) -> HighIterationState:
    """Process single iteration with variable timing"""
    current_iter = state["current_iteration"] + 1
    
    # Variable processing time based on iteration
    base_time = 0.1
    complexity_factor = (current_iter % 5) * 0.05  # Every 5th iteration is slower
    processing_time = base_time + complexity_factor
    
    await asyncio.sleep(processing_time)
    
    # Generate result for this iteration
    result = f"Iteration_{current_iter}_result_{current_iter*123}"
    updated_results = state["results"] + [result]
    
    return {
        **state,
        "current_iteration": current_iter,
        "results": updated_results,
        "processing_time": state["processing_time"] + processing_time,
        "status": f"processing_iteration_{current_iter}",
        "messages": state["messages"] + f" -> Completed iteration {current_iter}/{state['max_iterations']}"
    }


def should_continue_iteration(state: HighIterationState) -> str:
    """Conditional logic for iteration continuation"""
    if state["current_iteration"] >= state["max_iterations"]:
        return "finalize_high_iteration"
    else:
        return "iteration_processor"


async def progress_checkpoint(state: HighIterationState) -> HighIterationState:
    """Checkpoint every 10 iterations"""
    await asyncio.sleep(0.2)
    
    checkpoint_number = state["current_iteration"] // 10
    return {
        **state,
        "messages": state["messages"] + f" -> Checkpoint {checkpoint_number} reached",
        "status": f"checkpoint_{checkpoint_number}"
    }


def should_checkpoint(state: HighIterationState) -> str:
    """Check if we need a checkpoint"""
    if state["current_iteration"] % 10 == 0 and state["current_iteration"] < state["max_iterations"]:
        return "progress_checkpoint"
    else:
        return "iteration_processor"


async def finalize_high_iteration(state: HighIterationState) -> HighIterationState:
    """Finalize high iteration workflow"""
    await asyncio.sleep(0.5)
    
    avg_time = state["processing_time"] / state["current_iteration"] if state["current_iteration"] > 0 else 0
    
    return {
        **state,
        "messages": state["messages"] + f" -> High iteration workflow completed. Total: {state['current_iteration']} iterations, Avg time: {avg_time:.3f}s",
        "status": "completed"
    }


# Alternative high-speed iteration node
async def fast_iteration_processor(state: HighIterationState) -> HighIterationState:
    """Fast processor for very high iteration counts"""
    current_iter = state["current_iteration"] + 1
    
    # Minimal processing time for speed
    await asyncio.sleep(0.02)
    
    # Simplified result generation
    result = f"Fast_{current_iter}"
    updated_results = state["results"] + [result]
    
    return {
        **state,
        "current_iteration": current_iter,
        "results": updated_results,
        "processing_time": state["processing_time"] + 0.02,
        "status": f"fast_processing_{current_iter}",
        "messages": state["messages"] + (f" -> Fast iteration {current_iter}" if current_iter % 10 == 0 else "")
    }


def create_graph() -> StateGraph:
    """Create and return the high iteration test graph"""
    workflow = StateGraph(HighIterationState)
    
    # Add nodes
    workflow.add_node("initialize_high_iteration", initialize_high_iteration)
    workflow.add_node("iteration_processor", iteration_processor)
    workflow.add_node("progress_checkpoint", progress_checkpoint)
    workflow.add_node("finalize_high_iteration", finalize_high_iteration)
    
    # Add edges
    workflow.add_edge(START, "initialize_high_iteration")
    workflow.add_edge("initialize_high_iteration", "iteration_processor")
    
    # Conditional edges for iteration loop
    workflow.add_conditional_edges(
        "iteration_processor",
        should_continue_iteration,
        {
            "iteration_processor": "iteration_processor",  # Continue loop
            "finalize_high_iteration": "finalize_high_iteration"  # End loop
        }
    )
    
    # Checkpoint edges
    workflow.add_edge("progress_checkpoint", "iteration_processor")
    workflow.add_edge("finalize_high_iteration", END)
    
    return workflow.compile()


def create_fast_graph() -> StateGraph:
    """Create a fast version for very high iteration counts (100+)"""
    workflow = StateGraph(HighIterationState)
    
    workflow.add_node("initialize_high_iteration", initialize_high_iteration)
    workflow.add_node("fast_iteration_processor", fast_iteration_processor)
    workflow.add_node("finalize_high_iteration", finalize_high_iteration)
    
    workflow.add_edge(START, "initialize_high_iteration")
    workflow.add_edge("initialize_high_iteration", "fast_iteration_processor")
    
    workflow.add_conditional_edges(
        "fast_iteration_processor",
        should_continue_iteration,
        {
            "fast_iteration_processor": "fast_iteration_processor",
            "finalize_high_iteration": "finalize_high_iteration"
        }
    )
    
    workflow.add_edge("finalize_high_iteration", END)
    
    return workflow.compile()


def get_initial_state(max_iterations: int = 20) -> HighIterationState:
    """Get initial state for the graph"""
    return {
        "messages": f"High Iteration Test Started (target: {max_iterations})",
        "current_iteration": 0,
        "max_iterations": max_iterations,
        "results": [],
        "processing_time": 0.0,
        "status": "initialized"
    }


def get_extreme_initial_state() -> HighIterationState:
    """Get initial state for extreme iteration testing (100+ iterations)"""
    return get_initial_state(100)


# Create the graph instance
graph = create_graph()