from typing import List, TypedDict, Annotated
from langgraph.graph import StateGraph, START, END
from langgraph.types import Send
import operator
import asyncio
import random


class TestState(TypedDict):
    tasks: List[str]
    results: Annotated[List[str], operator.add]


class WorkerState(TypedDict):
    task: str
    results: Annotated[List[str], operator.add]


async def create_tasks(state: TestState) -> TestState:
    return {"tasks": ["task_1", "task_2", "task_3"]}


def dispatch_workers(state: TestState) -> List[Send]:
    return [Send("worker", {"task": task}) for task in state["tasks"]]


async def operation_1(state: WorkerState) -> WorkerState:
    sleep_time = random.uniform(6, 8)
    await asyncio.sleep(sleep_time)
    return {"results": [f"completed_{state['task']}_after_{sleep_time:.1f}s"]}


async def operation_2(state: WorkerState) -> WorkerState:
    sleep_time = random.uniform(6, 8)
    await asyncio.sleep(sleep_time)
    return {"results": [f"completed_{state['task']}_after_{sleep_time:.1f}s"]}


async def operation_3(state: WorkerState) -> WorkerState:
    sleep_time = random.uniform(6, 8)
    await asyncio.sleep(sleep_time)
    return {"results": [f"completed_{state['task']}_after_{sleep_time:.1f}s"]}


def create_worker_graph() -> StateGraph:
    graph = StateGraph(WorkerState)
    graph.add_node("operation_1", operation_1)
    graph.add_node("operation_2", operation_2)
    graph.add_node("operation_3", operation_3)
    graph.add_edge(START, "operation_1")
    graph.add_edge("operation_1", "operation_2")
    graph.add_edge("operation_2", "operation_3")
    graph.add_edge("operation_3", END)
    return graph.compile()

worker_graph = create_worker_graph()

def collect_results(state: TestState) -> TestState:
    return {}


def create_subgraph() -> StateGraph:
    graph = StateGraph(TestState)
    
    graph.add_node("create_tasks", create_tasks)
    graph.add_node("worker", worker_graph)
    graph.add_node("collect_results", collect_results)
    
    graph.add_edge(START, "create_tasks")
    graph.add_conditional_edges(
        "create_tasks",
        dispatch_workers,
        ["worker"]
    )
    graph.add_edge("worker", "collect_results")
    graph.add_edge("collect_results", END)
    
    return graph.compile()


delegate_subgraph = create_subgraph()


def search(state: TestState) -> TestState:
    return {"results": ["search_results"]}


def create_graph() -> StateGraph:
    graph = StateGraph(TestState)
    graph.add_node("search", search)
    graph.add_node("delegate", delegate_subgraph)

    graph.add_edge(START, "search")
    graph.add_edge("search", "delegate")
    graph.add_edge("delegate", END)

    return graph.compile()
graph = create_graph()

__all__ = ['graph']


