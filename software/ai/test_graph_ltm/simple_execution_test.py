"""Simple test to check execution flow"""

import asyncio
from .subgraph_test_graph import create_graph


async def test_execution():
    """Test graph execution to see node order"""
    print("Creating graph...")
    graph = create_graph()
    
    print("\nExecuting graph...")
    initial_state = {"messages": "Test", "level": 0}
    
    # Track execution order
    executed_nodes = []
    
    async for chunk in graph.astream(initial_state):
        for node_name, output in chunk.items():
            executed_nodes.append(node_name)
            print(f"Executed: {node_name}")
            if isinstance(output, dict):
                print(f"  State: messages='{output.get('messages', '')}', level={output.get('level', 0)}")
    
    print(f"\nExecution order: {' -> '.join(executed_nodes)}")
    print("\nExpected nested execution:")
    print("  main_process -> middle_subgraph (contains: middle_start -> nested_subgraph (contains: nested_process))")


if __name__ == "__main__":
    asyncio.run(test_execution())