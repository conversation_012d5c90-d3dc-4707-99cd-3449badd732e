"""
Error simulation test graph for LangGraph Task Manager
Tests various error scenarios and recovery mechanisms
"""

from langgraph.graph import StateGraph, START, END
from typing import TypedDict
import asyncio
import random


class ErrorState(TypedDict):
    messages: str
    error_count: int
    retry_count: int
    status: str
    error_type: str


async def start_node(state: ErrorState) -> ErrorState:
    """Start node that always succeeds"""
    await asyncio.sleep(0.5)
    return {
        **state,
        "messages": state["messages"] + " -> Started successfully",
        "status": "running"
    }


async def risky_operation(state: ErrorState) -> ErrorState:
    """Node that randomly fails to simulate errors"""
    await asyncio.sleep(1.0)
    
    # Simulate different types of errors
    error_scenarios = [
        {"type": "timeout", "probability": 0.3, "message": "Operation timeout"},
        {"type": "connection", "probability": 0.2, "message": "Connection failed"},
        {"type": "validation", "probability": 0.2, "message": "Data validation error"},
        {"type": "resource", "probability": 0.1, "message": "Insufficient resources"}
    ]
    
    # Check if we should simulate an error
    if random.random() < 0.9:  # 90% chance of error
        error_scenario = random.choice(error_scenarios)
        if random.random() < 0.8:  # 80% chance to actually trigger the error
            raise Exception(f"{error_scenario['type']}: {error_scenario['message']}")
    
    return {
        **state,
        "messages": state["messages"] + " -> Risky operation completed",
        "status": "processing"
    }


async def error_handler(state: ErrorState) -> ErrorState:
    """Handle errors and implement retry logic"""
    await asyncio.sleep(0.8)
    
    retry_count = state["retry_count"] + 1
    error_count = state["error_count"] + 1
    
    return {
        **state,
        "messages": state["messages"] + f" -> Error handled (retry {retry_count})",
        "error_count": error_count,
        "retry_count": retry_count,
        "status": "recovering"
    }


def should_retry(state: ErrorState) -> str:
    """Conditional logic for retry mechanism"""
    if state["retry_count"] >= 3:
        return "final_failure"
    else:
        return "risky_operation"


async def recovery_node(state: ErrorState) -> ErrorState:
    """Recovery node for successful completion after errors"""
    await asyncio.sleep(0.6)
    return {
        **state,
        "messages": state["messages"] + " -> Recovered successfully",
        "status": "completed"
    }


async def final_failure(state: ErrorState) -> ErrorState:
    """Final failure node when retries are exhausted"""
    await asyncio.sleep(0.3)
    return {
        **state,
        "messages": state["messages"] + " -> Maximum retries exceeded - final failure",
        "status": "failed",
        "error_type": "max_retries_exceeded"
    }


async def timeout_simulation(state: ErrorState) -> ErrorState:
    """Simulate a timeout scenario"""
    # Simulate long operation
    await asyncio.sleep(3.0)
    return {
        **state,
        "messages": state["messages"] + " -> Long operation completed",
        "status": "timeout_test"
    }


async def recursion_limit_test(state: ErrorState) -> ErrorState:
    """Simulate recursion limit error"""
    await asyncio.sleep(0.5)
    
    if state["retry_count"] > 25:  # Simulate recursion limit
        raise RecursionError("Maximum recursion depth exceeded")
    
    return {
        **state,
        "messages": state["messages"] + " -> Recursion test passed",
        "retry_count": state["retry_count"] + 1
    }


def create_graph() -> StateGraph:
    """Create and return the error test graph"""
    workflow = StateGraph(ErrorState)
    
    # Add nodes
    workflow.add_node("start_node", start_node)
    workflow.add_node("risky_operation", risky_operation)
    workflow.add_node("error_handler", error_handler)
    workflow.add_node("recovery_node", recovery_node)
    workflow.add_node("final_failure", final_failure)
    workflow.add_node("timeout_simulation", timeout_simulation)
    workflow.add_node("recursion_limit_test", recursion_limit_test)
    
    # Add edges
    workflow.add_edge(START, "start_node")
    workflow.add_edge("start_node", "risky_operation")
    
    # Conditional edges for retry logic
    workflow.add_conditional_edges(
        "error_handler",
        should_retry,
        {
            "risky_operation": "risky_operation",  # Retry
            "final_failure": "final_failure"  # Give up
        }
    )
    
    # Success path
    workflow.add_edge("risky_operation", "recovery_node")
    workflow.add_edge("recovery_node", END)
    
    # Failure paths
    workflow.add_edge("final_failure", END)
    
    return workflow.compile()


def create_timeout_graph() -> StateGraph:
    """Create a graph specifically for timeout testing"""
    workflow = StateGraph(ErrorState)
    
    workflow.add_node("start_node", start_node)
    workflow.add_node("timeout_simulation", timeout_simulation)
    
    workflow.add_edge(START, "start_node")
    workflow.add_edge("start_node", "timeout_simulation")
    workflow.add_edge("timeout_simulation", END)
    
    return workflow.compile()


def create_recursion_graph() -> StateGraph:
    """Create a graph specifically for recursion limit testing"""
    workflow = StateGraph(ErrorState)
    
    workflow.add_node("recursion_limit_test", recursion_limit_test)
    
    workflow.add_edge(START, "recursion_limit_test")
    workflow.add_edge("recursion_limit_test", END)
    
    return workflow.compile()


def get_initial_state() -> ErrorState:
    """Get initial state for the graph"""
    return {
        "messages": "Error Test Started",
        "error_count": 0,
        "retry_count": 0,
        "status": "initialized",
        "error_type": ""
    }


# Create the graph instance (main error test)
graph = create_graph()

__all__ = ['graph']