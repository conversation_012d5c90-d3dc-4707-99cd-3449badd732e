"""
Dynamic Detection Test - Proves 100% auto-detection capability
Creates a completely new graph structure to test universal detection
"""

from langgraph.graph import StateGraph, START, END
from typing import TypedDict
import asyncio


class GraphState(TypedDict):
    data: str
    counter: int


# Create a unique graph structure with conditional routing
async def initialize_data(state: GraphState) -> GraphState:
    """Initialize the data processing"""
    await asyncio.sleep(0.5)
    return {
        **state,
        "data": state.get("data", "") + " -> Initialized",
        "counter": state.get("counter", 0) + 1
    }


async def process_data(state: GraphState) -> GraphState:
    """Process the data"""
    await asyncio.sleep(1.0)
    return {
        **state,
        "data": state["data"] + " -> Processed",
        "counter": state["counter"] + 1
    }


async def validate_data(state: GraphState) -> GraphState:
    """Validate the processed data"""
    await asyncio.sleep(0.8)
    return {
        **state,
        "data": state["data"] + " -> Validated",
        "counter": state["counter"] + 1
    }


async def finalize_data(state: GraphState) -> GraphState:
    """Finalize the data processing"""
    await asyncio.sleep(0.3)
    return {
        **state,
        "data": state["data"] + " -> Finalized",
        "counter": state["counter"] + 1
    }


def create_graph() -> StateGraph:
    """Create a unique graph structure for dynamic detection testing"""
    workflow = StateGraph(GraphState)
    
    # Add nodes in a unique pattern
    workflow.add_node("initialize_data", initialize_data)
    workflow.add_node("process_data", process_data)
    workflow.add_node("validate_data", validate_data)
    workflow.add_node("finalize_data", finalize_data)
    
    # Create a sequential flow pattern for reliable testing
    workflow.add_edge(START, "initialize_data")
    workflow.add_edge("initialize_data", "process_data")
    workflow.add_edge("process_data", "validate_data")
    workflow.add_edge("validate_data", "finalize_data")
    workflow.add_edge("finalize_data", END)
    
    return workflow.compile()


def get_initial_state() -> GraphState:
    """Get initial state for the graph"""
    return {
        "data": "Dynamic detection test started",
        "counter": 0
    }


# Create the graph instance
graph = create_graph()
