"""Test recursive subgraph detection with the discovered approach"""

from langgraph.graph import StateGraph, END
from langgraph.graph.state import CompiledStateGraph
from typing_extensions import TypedDict
import json


class SimpleState(TypedDict):
    value: str


class SubgraphState(TypedDict):
    sub_value: str


class NestedSubgraphState(TypedDict):
    nested_value: str


def node_a(state: SimpleState) -> SimpleState:
    return {"value": state["value"] + " -> A"}


def sub_node_1(state: SubgraphState) -> SubgraphState:
    return {"sub_value": state["sub_value"] + " -> Sub1"}


def sub_node_2(state: SubgraphState) -> SubgraphState:
    return {"sub_value": state["sub_value"] + " -> Sub2"}


def nested_node_1(state: NestedSubgraphState) -> NestedSubgraphState:
    return {"nested_value": state["nested_value"] + " -> Nested1"}


def nested_node_2(state: NestedSubgraphState) -> NestedSubgraphState:
    return {"nested_value": state["nested_value"] + " -> Nested2"}


def create_nested_subgraph():
    """Create a nested subgraph"""
    nested_graph = StateGraph(NestedSubgraphState)
    
    nested_graph.add_node("nested_node_1", nested_node_1)
    nested_graph.add_node("nested_node_2", nested_node_2)
    
    nested_graph.set_entry_point("nested_node_1")
    nested_graph.add_edge("nested_node_1", "nested_node_2")
    nested_graph.add_edge("nested_node_2", END)
    
    return nested_graph.compile()


def create_subgraph_with_nested():
    """Create a subgraph that contains another subgraph"""
    subgraph_builder = StateGraph(SubgraphState)
    
    subgraph_builder.add_node("sub_node_1", sub_node_1)
    
    # Add nested subgraph
    nested_subgraph = create_nested_subgraph()
    subgraph_builder.add_node("nested_subgraph", nested_subgraph)
    
    subgraph_builder.add_node("sub_node_2", sub_node_2)
    
    subgraph_builder.set_entry_point("sub_node_1")
    subgraph_builder.add_edge("sub_node_1", "nested_subgraph")
    subgraph_builder.add_edge("nested_subgraph", "sub_node_2")
    subgraph_builder.add_edge("sub_node_2", END)
    
    return subgraph_builder.compile()


def detect_subgraphs_recursive(graph, path="", visited=None):
    """Recursively detect all subgraphs using the discovered approach"""
    if visited is None:
        visited = set()
    
    subgraphs = {}
    
    if hasattr(graph, 'nodes'):
        for node_name, node_value in graph.nodes.items():
            # Skip special nodes
            if node_name in ["__start__", "__end__"]:
                continue
            
            current_path = f"{path}/{node_name}" if path else node_name
            
            # Check if this is a PregelNode with subgraphs
            if hasattr(node_value, 'subgraphs') and node_value.subgraphs:
                # This node contains subgraphs
                for subgraph in node_value.subgraphs:
                    if isinstance(subgraph, CompiledStateGraph):
                        # Avoid infinite recursion
                        graph_id = id(subgraph)
                        if graph_id in visited:
                            continue
                        visited.add(graph_id)
                        
                        # Get subgraph nodes
                        sub_nodes = []
                        if hasattr(subgraph, 'nodes'):
                            sub_nodes = [n for n in subgraph.nodes.keys() 
                                       if n not in ["__start__", "__end__"]]
                        
                        subgraphs[current_path] = {
                            "nodes": ["__start__"] + sub_nodes + ["__end__"],
                            "type": "subgraph"
                        }
                        
                        # Recursively check the subgraph
                        nested_subgraphs = detect_subgraphs_recursive(subgraph, current_path, visited)
                        subgraphs.update(nested_subgraphs)
            
            # Also check bound attribute for backward compatibility
            elif hasattr(node_value, 'bound') and isinstance(node_value.bound, CompiledStateGraph):
                subgraph = node_value.bound
                
                # Avoid infinite recursion
                graph_id = id(subgraph)
                if graph_id in visited:
                    continue
                visited.add(graph_id)
                
                # Get subgraph nodes
                sub_nodes = []
                if hasattr(subgraph, 'nodes'):
                    sub_nodes = [n for n in subgraph.nodes.keys() 
                               if n not in ["__start__", "__end__"]]
                
                subgraphs[current_path] = {
                    "nodes": ["__start__"] + sub_nodes + ["__end__"],
                    "type": "subgraph"
                }
                
                # Recursively check the subgraph
                nested_subgraphs = detect_subgraphs_recursive(subgraph, current_path, visited)
                subgraphs.update(nested_subgraphs)
    
    return subgraphs


def main():
    """Main test function"""
    print("Creating main graph with nested subgraphs...")
    
    # Create main graph
    main_graph = StateGraph(SimpleState)
    
    # Add regular node
    main_graph.add_node("node_a", node_a)
    
    # Create and add subgraph with nested subgraph
    subgraph = create_subgraph_with_nested()
    main_graph.add_node("my_subgraph", subgraph)
    
    # Set up edges
    main_graph.set_entry_point("node_a")
    main_graph.add_edge("node_a", "my_subgraph")
    main_graph.add_edge("my_subgraph", END)
    
    # Compile the graph
    compiled_main = main_graph.compile()
    
    # Test recursive detection
    print("\n" + "="*60)
    print("RECURSIVE SUBGRAPH DETECTION:")
    print("="*60)
    
    subgraphs = detect_subgraphs_recursive(compiled_main)
    
    print("\nDetected subgraphs:")
    for path, info in subgraphs.items():
        print(f"\n{path}:")
        print(f"  Type: {info['type']}")
        print(f"  Nodes: {info['nodes']}")
    
    # Test the improved function that would replace the current implementation
    print("\n" + "="*60)
    print("IMPROVED IMPLEMENTATION:")
    print("="*60)
    
    def get_graph_structure_improved(graph):
        """Improved implementation for langgraph_task_manager.py"""
        all_nodes = []
        subgraphs = {}
        
        # Recursive function to detect subgraphs
        def detect_subgraphs(g, path="", visited=None):
            if visited is None:
                visited = set()
            
            local_subgraphs = {}
            
            if hasattr(g, 'nodes'):
                for node_name, node_value in g.nodes.items():
                    if node_name in ["__start__", "__end__"]:
                        continue
                    
                    current_path = f"{path}/{node_name}" if path else node_name
                    
                    # Check if PregelNode has subgraphs attribute
                    if hasattr(node_value, 'subgraphs') and node_value.subgraphs:
                        for subgraph in node_value.subgraphs:
                            if isinstance(subgraph, CompiledStateGraph):
                                graph_id = id(subgraph)
                                if graph_id in visited:
                                    continue
                                visited.add(graph_id)
                                
                                sub_nodes = []
                                if hasattr(subgraph, 'nodes'):
                                    sub_nodes = [n for n in subgraph.nodes.keys() 
                                               if n not in ["__start__", "__end__"]]
                                
                                local_subgraphs[current_path] = {
                                    "nodes": ["__start__"] + sub_nodes + ["__end__"],
                                    "type": "subgraph"
                                }
                                
                                nested = detect_subgraphs(subgraph, current_path, visited)
                                local_subgraphs.update(nested)
                    
                    # Fallback to bound attribute
                    elif hasattr(node_value, 'bound') and isinstance(node_value.bound, CompiledStateGraph):
                        subgraph = node_value.bound
                        graph_id = id(subgraph)
                        if graph_id in visited:
                            continue
                        visited.add(graph_id)
                        
                        sub_nodes = []
                        if hasattr(subgraph, 'nodes'):
                            sub_nodes = [n for n in subgraph.nodes.keys() 
                                       if n not in ["__start__", "__end__"]]
                        
                        local_subgraphs[current_path] = {
                            "nodes": ["__start__"] + sub_nodes + ["__end__"],
                            "type": "subgraph"
                        }
                        
                        nested = detect_subgraphs(subgraph, current_path, visited)
                        local_subgraphs.update(nested)
            
            return local_subgraphs
        
        # Get all nodes
        if hasattr(graph, "nodes"):
            all_nodes = list(graph.nodes.keys())
        elif hasattr(graph, "_nodes"):
            all_nodes = list(graph._nodes.keys())
        
        # Detect subgraphs recursively
        subgraphs = detect_subgraphs(graph)
        
        return all_nodes, subgraphs
    
    all_nodes, subgraphs = get_graph_structure_improved(compiled_main)
    print(f"\nAll nodes: {all_nodes}")
    print(f"\nSubgraphs: {json.dumps(subgraphs, indent=2)}")


if __name__ == "__main__":
    main()