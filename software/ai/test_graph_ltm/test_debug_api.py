"""
Test script to check the debug information from the API
"""

import requests
import json


def test_debug_api():
    """Test the debug API to see edge detection information"""
    
    # Test the run-test-graph endpoint to see debug info
    url = "http://localhost:5001/api/langgraph-task-manager/run-test-graph"
    
    payload = {
        "graph_name": "parallel_test_graph",
        "input_data": {"message": "test"}
    }
    
    try:
        print("🔍 Testing debug API...")
        response = requests.post(url, json=payload)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ API Response received")
            print(f"📊 Total nodes: {data.get('total_nodes', 'N/A')}")
            print(f"📊 Edges count: {len(data.get('edges', []))}")
            print(f"📊 Edges: {data.get('edges', [])}")
            
            # Check debug info
            debug_info = data.get('debug_info', {})
            if debug_info:
                print("\n🔍 DEBUG INFORMATION:")
                print(f"  Graph attributes: {debug_info.get('graph_attributes', [])}")
                print(f"  Builder attributes: {debug_info.get('builder_attributes', [])}")
                print(f"  Edge detection method: {debug_info.get('edge_detection_method', 'none')}")
                print(f"  Edges found count: {debug_info.get('edges_found_count', 0)}")
            else:
                print("❌ No debug_info in response")
                
            # Show full response structure
            print(f"\n📋 Response keys: {list(data.keys())}")
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    test_debug_api()
