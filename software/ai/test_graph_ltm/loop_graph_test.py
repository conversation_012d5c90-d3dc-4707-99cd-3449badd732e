"""
Loop detection test for LangGraph Task Manager
Tests iterative loop patterns similar to deep_search_graph workflow
Main Graph: search -> analyze -> decide -> (loop back to search OR continue to finalize)
"""

from langgraph.graph import StateGraph, START, END
from typing import TypedDict, Literal
import asyncio
import random


class LoopState(TypedDict):
    messages: list[str]
    iteration: int
    max_iterations: int


async def search_node(state: LoopState) -> LoopState:
    """Search for information - can be revisited multiple times"""
    iteration = state.get("iteration", 0)
    # Random sleep to simulate work
    await asyncio.sleep(random.uniform(4.0, 8.0))
    
    messages = state.get("messages", [])
    messages.append(f"🔍 Search iteration {iteration + 1}: Looking for new insights")
    
    return {
        **state,
        "messages": messages
    }


async def analyze_node(state: LoopState) -> LoopState:
    """Analyze the search results"""
    # Random sleep to simulate work
    await asyncio.sleep(random.uniform(4.0, 8.0))
    
    messages = state.get("messages", [])
    messages.append(f"📊 Analyzing results from iteration {state['iteration'] + 1}")
    
    return {
        **state,
        "messages": messages
    }


async def decision_node(state: LoopState) -> LoopState:
    """Decide whether to continue searching or finalize"""
    # Random sleep to simulate work
    await asyncio.sleep(random.uniform(4.0, 8.0))
    
    iteration = state.get("iteration", 0) + 1
    max_iterations = state.get("max_iterations", 3)
    
    messages = state.get("messages", [])
    
    # Decide based on iteration count
    if iteration < max_iterations:
        messages.append(f"🔄 Decision: Continue searching (iteration {iteration}/{max_iterations})")
        next_action = "search"
    else:
        messages.append(f"✅ Decision: Sufficient data collected after {iteration} iterations")
        next_action = "finalize"
    
    return {
        **state,
        "messages": messages,
        "iteration": iteration,
        "next": next_action
    }


async def finalize_node(state: LoopState) -> LoopState:
    """Finalize the results after loop completion"""
    # Random sleep to simulate work
    await asyncio.sleep(random.uniform(4.0, 8.0))
    
    messages = state.get("messages", [])
    messages.append(f"📝 Finalizing report after {state['iteration']} iterations")
    messages.append("🎯 Loop workflow completed successfully!")
    
    return {
        **state,
        "messages": messages
    }


def create_graph() -> StateGraph:
    """Create graph with loop pattern"""
    workflow = StateGraph(LoopState)
    
    # Add nodes
    workflow.add_node("search", search_node)
    workflow.add_node("analyze", analyze_node)
    workflow.add_node("decide", decision_node)
    workflow.add_node("finalize", finalize_node)
    
    # Add edges - creating a loop pattern
    workflow.add_edge(START, "search")
    workflow.add_edge("search", "analyze")
    workflow.add_edge("analyze", "decide")
    
    # Conditional edge - this creates the loop!
    workflow.add_conditional_edges(
        "decide",
        lambda state: state.get("next", "finalize"),
        {
            "search": "search",  # Loop back to search
            "finalize": "finalize"  # Or continue to finalize
        }
    )
    
    workflow.add_edge("finalize", END)
    
    return workflow.compile()


def get_initial_state() -> LoopState:
    """Get initial state for the graph"""
    return {
        "messages": ["🚀 Starting iterative search workflow"],
        "iteration": 0,
        "max_iterations": 3  # Will loop 3 times
    }


# Create the graph instance
graph = create_graph()

__all__ = ['graph']