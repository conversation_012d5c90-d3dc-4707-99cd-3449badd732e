"""Test script to explore PregelNode structure"""

from langgraph.graph import StateGraph, END
from langgraph.graph.state import CompiledStateGraph
from typing_extensions import TypedDict
import inspect


class SimpleState(TypedDict):
    value: str


class SubgraphState(TypedDict):
    sub_value: str


def node_a(state: SimpleState) -> SimpleState:
    return {"value": state["value"] + " -> A"}


def sub_node_1(state: SubgraphState) -> SubgraphState:
    return {"sub_value": state["sub_value"] + " -> Sub1"}


def sub_node_2(state: SubgraphState) -> SubgraphState:
    return {"sub_value": state["sub_value"] + " -> Sub2"}


def explore_pregel_node(node, node_name):
    """Explore the internal structure of a PregelNode"""
    print(f"\n=== Exploring PregelNode: {node_name} ===")
    print(f"Type: {type(node)}")
    
    # Get all attributes
    attrs = [attr for attr in dir(node) if not attr.startswith('__')]
    print(f"\nAttributes: {attrs}")
    
    # Check each attribute
    for attr in attrs:
        try:
            value = getattr(node, attr)
            print(f"\n{attr}:")
            print(f"  Type: {type(value)}")
            if not callable(value) and not inspect.ismethod(value):
                print(f"  Value: {value}")
                
                # Special handling for certain attributes
                if attr in ['func', 'runnable', 'mapper']:
                    print(f"  >>> Checking if this contains a graph...")
                    if hasattr(value, 'nodes') or hasattr(value, '_nodes'):
                        print(f"  >>> HAS NODES! This is a subgraph!")
                    if isinstance(value, CompiledStateGraph):
                        print(f"  >>> IS CompiledStateGraph! This is a subgraph!")
                    
                    # Check all attributes of the value
                    sub_attrs = [a for a in dir(value) if not a.startswith('__')]
                    print(f"  Sub-attributes: {sub_attrs[:10]}...")  # First 10
        except Exception as e:
            print(f"{attr}: Error accessing - {e}")


def create_subgraph():
    """Create a simple subgraph"""
    subgraph_builder = StateGraph(SubgraphState)
    
    subgraph_builder.add_node("sub_node_1", sub_node_1)
    subgraph_builder.add_node("sub_node_2", sub_node_2)
    
    subgraph_builder.set_entry_point("sub_node_1")
    subgraph_builder.add_edge("sub_node_1", "sub_node_2")
    subgraph_builder.add_edge("sub_node_2", END)
    
    return subgraph_builder.compile()


def main():
    """Main test function"""
    # Create main graph
    main_graph = StateGraph(SimpleState)
    
    # Add regular node
    main_graph.add_node("node_a", node_a)
    
    # Create and add subgraph
    subgraph = create_subgraph()
    main_graph.add_node("my_subgraph", subgraph)
    
    # Set up edges
    main_graph.set_entry_point("node_a")
    main_graph.add_edge("node_a", "my_subgraph")
    main_graph.add_edge("my_subgraph", END)
    
    # Compile the graph
    compiled_main = main_graph.compile()
    
    # Explore each node
    print("Exploring nodes in compiled graph:")
    for node_name, node_value in compiled_main.nodes.items():
        explore_pregel_node(node_value, node_name)
    
    # Try to find a recursive solution
    print("\n" + "="*60)
    print("RECURSIVE SUBGRAPH DETECTION:")
    print("="*60)
    
    def detect_subgraphs_recursive(graph, path=""):
        """Recursively detect all subgraphs"""
        subgraphs = {}
        
        if hasattr(graph, 'nodes'):
            for node_name, node_value in graph.nodes.items():
                current_path = f"{path}/{node_name}" if path else node_name
                
                # Skip special nodes
                if node_name in ["__start__", "__end__"]:
                    continue
                
                # Check if PregelNode contains a graph
                is_subgraph = False
                subgraph_ref = None
                
                if hasattr(node_value, 'func'):
                    func = getattr(node_value, 'func')
                    if isinstance(func, CompiledStateGraph):
                        is_subgraph = True
                        subgraph_ref = func
                
                if hasattr(node_value, 'runnable'):
                    runnable = getattr(node_value, 'runnable')
                    if isinstance(runnable, CompiledStateGraph):
                        is_subgraph = True
                        subgraph_ref = runnable
                
                if hasattr(node_value, 'mapper'):
                    mapper = getattr(node_value, 'mapper')
                    if isinstance(mapper, CompiledStateGraph):
                        is_subgraph = True
                        subgraph_ref = mapper
                
                if is_subgraph and subgraph_ref:
                    print(f"Found subgraph at: {current_path}")
                    # Get subgraph nodes
                    sub_nodes = []
                    if hasattr(subgraph_ref, 'nodes'):
                        sub_nodes = [n for n in subgraph_ref.nodes.keys() 
                                   if n not in ["__start__", "__end__"]]
                    
                    subgraphs[current_path] = {
                        "nodes": ["__start__"] + sub_nodes + ["__end__"],
                        "type": "subgraph"
                    }
                    
                    # Recursively check the subgraph
                    nested_subgraphs = detect_subgraphs_recursive(subgraph_ref, current_path)
                    subgraphs.update(nested_subgraphs)
        
        return subgraphs
    
    subgraphs = detect_subgraphs_recursive(compiled_main)
    print(f"\nDetected subgraphs: {subgraphs}")


if __name__ == "__main__":
    main()