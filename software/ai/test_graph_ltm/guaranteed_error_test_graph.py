"""
Guaranteed error test graph for LangGraph Task Manager
This graph ALWAYS fails on the second node to test error visualization
"""

from langgraph.graph import StateGraph, START, END
from typing import TypedDict
import asyncio


class ErrorState(TypedDict):
    messages: str


async def start_node(state: <PERSON>rror<PERSON>tate) -> ErrorState:
    """Start node that always succeeds"""
    await asyncio.sleep(0.5)
    return {
        **state,
        "messages": state["messages"] + " -> Started successfully"
    }


async def guaranteed_failure_node(state: ErrorState) -> ErrorState:
    """Node that ALWAYS fails to test error visualization"""
    await asyncio.sleep(1.0)
    
    # This will ALWAYS throw an error
    raise Exception("GUARANTEED_ERROR: This node always fails for testing purposes")


async def recovery_node(state: ErrorState) -> ErrorState:
    """Recovery node - should never be reached"""
    await asyncio.sleep(0.6)
    return {
        **state,
        "messages": state["messages"] + " -> This should never execute"
    }


def create_graph() -> StateGraph:
    """Create and return the guaranteed error test graph"""
    workflow = StateGraph(ErrorState)
    
    # Add nodes
    workflow.add_node("start_node", start_node)
    workflow.add_node("guaranteed_failure_node", guaranteed_failure_node)
    workflow.add_node("recovery_node", recovery_node)
    
    # Add edges
    workflow.add_edge(START, "start_node")
    workflow.add_edge("start_node", "guaranteed_failure_node")
    workflow.add_edge("guaranteed_failure_node", "recovery_node")
    workflow.add_edge("recovery_node", END)
    
    return workflow.compile()


def get_initial_state() -> ErrorState:
    """Get initial state for the graph"""
    return {
        "messages": "Guaranteed Error Test Started"
    }


# Create the graph instance
graph = create_graph()

__all__ = ['graph']