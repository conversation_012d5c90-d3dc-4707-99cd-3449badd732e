"""
Test script to verify 3-layer subgraph detection
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .subgraph_test_graph import create_graph


def test_hierarchy_detection():
    """Test that all 3 layers are detected"""
    print("Creating graph with 3-layer hierarchy...")
    graph = create_graph()
    
    print("\nDetecting subgraphs...")
    # Use the same function from the backend
    def detect_subgraphs_recursive_test(g, path="", parent=None, visited=None):
        """Recursively detect all subgraphs in a LangGraph with hierarchy info"""
        if visited is None:
            visited = set()
        
        local_subgraphs = {}
        
        if hasattr(g, 'nodes'):
            for node_name, node_value in g.nodes.items():
                # Skip special nodes
                if node_name in ["__start__", "__end__"]:
                    continue
                
                current_path = f"{path}/{node_name}" if path else node_name
                
                # Check if this is a PregelNode with subgraphs
                if hasattr(node_value, 'subgraphs') and node_value.subgraphs:
                    for subgraph in node_value.subgraphs:
                        # Avoid infinite recursion
                        graph_id = id(subgraph)
                        if graph_id in visited:
                            continue
                        visited.add(graph_id)
                        
                        # Get subgraph nodes
                        sub_nodes = []
                        if hasattr(subgraph, 'nodes'):
                            sub_nodes = [n for n in subgraph.nodes.keys() 
                                       if n not in ["__start__", "__end__"]]
                        
                        # Store with both node name and full path
                        local_subgraphs[node_name] = {
                            "nodes": ["__start__"] + sub_nodes + ["__end__"],
                            "type": "subgraph",
                            "path": current_path,
                            "parent": parent,
                            "depth": path.count('/') + 1 if path else 0
                        }
                        
                        # Recursively check the subgraph
                        nested = detect_subgraphs_recursive_test(subgraph, current_path, node_name, visited)
                        # Store nested subgraphs with full path as key
                        for nested_key, nested_value in nested.items():
                            full_key = f"{node_name}/{nested_key}" if node_name != nested_key else nested_key
                            local_subgraphs[full_key] = nested_value
                
                # Fallback: check bound attribute
                elif hasattr(node_value, 'bound'):
                    bound = node_value.bound
                    if hasattr(bound, 'nodes') or hasattr(bound, '_nodes'):
                        graph_id = id(bound)
                        if graph_id not in visited:
                            visited.add(graph_id)
                            
                            sub_nodes = []
                            if hasattr(bound, 'nodes'):
                                sub_nodes = [n for n in bound.nodes.keys() 
                                           if n not in ["__start__", "__end__"]]
                            elif hasattr(bound, '_nodes'):
                                sub_nodes = [n for n in bound._nodes.keys() 
                                           if n not in ["__start__", "__end__"]]
                            
                            if sub_nodes:
                                local_subgraphs[node_name] = {
                                    "nodes": ["__start__"] + sub_nodes + ["__end__"],
                                    "type": "subgraph",
                                    "path": current_path,
                                    "parent": parent,
                                    "depth": path.count('/') + 1 if path else 0
                                }
                                
                                # Recursively check
                                nested = detect_subgraphs_recursive_test(bound, current_path, node_name, visited)
                                # Store nested subgraphs with full path as key
                                for nested_key, nested_value in nested.items():
                                    full_key = f"{node_name}/{nested_key}" if node_name != nested_key else nested_key
                                    local_subgraphs[full_key] = nested_value
        
        return local_subgraphs
    
    subgraphs = detect_subgraphs_recursive_test(graph)
    
    print("\nDetected subgraphs:")
    for name, info in subgraphs.items():
        print(f"\n{name}:")
        print(f"  Path: {info['path']}")
        print(f"  Parent: {info['parent']}")
        print(f"  Depth: {info['depth']}")
        print(f"  Nodes: {info['nodes']}")
    
    print(f"\nTotal subgraphs detected: {len(subgraphs)}")
    
    # Verify we have all expected layers
    expected = {
        'middle_subgraph': 0,  # First level
        'middle_subgraph/nested_subgraph': 1  # Second level (nested)
    }
    
    print("\nVerification:")
    for expected_name, expected_depth in expected.items():
        if expected_name in subgraphs:
            actual_depth = subgraphs[expected_name]['depth']
            if actual_depth == expected_depth:
                print(f"✓ {expected_name} found at correct depth {expected_depth}")
            else:
                print(f"✗ {expected_name} found but at wrong depth {actual_depth} (expected {expected_depth})")
        else:
            print(f"✗ {expected_name} NOT FOUND")
    
    return len(subgraphs) == len(expected)


if __name__ == "__main__":
    success = test_hierarchy_detection()
    sys.exit(0 if success else 1)