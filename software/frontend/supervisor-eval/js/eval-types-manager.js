/**
 * Evaluation Types Manager for Supervisor Evaluations
 * Handles the evaluation types menu and switching between different evaluation types
 */

class EvalTypesManager {
    constructor() {
        this.currentEvalType = 'strategic_context_keeper_drift'; // Default to drift-only evaluations
        this.evaluationTypes = {};
        
        // UI elements
        this.evalTypesBtn = document.getElementById('eval-types-btn');
        this.evalTypesMenu = document.getElementById('eval-types-menu');
        this.evalTypesList = document.getElementById('eval-types-list');
        this.currentEvalName = document.getElementById('current-eval-name');
        this.currentEvalDescription = document.getElementById('current-eval-description');
        
        this.setupEventListeners();
    }

    /**
     * Setup event listeners for evaluation types menu
     */
    setupEventListeners() {
        // Toggle dropdown menu
        if (this.evalTypesBtn) {
            this.evalTypesBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleMenu();
            });
        }

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.evalTypesMenu?.contains(e.target) && !this.evalTypesBtn?.contains(e.target)) {
                this.hideMenu();
            }
        });

        // Close menu on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideMenu();
            }
        });
    }

    /**
     * Load and render evaluation types from API
     */
    async loadEvaluationTypes() {
        try {
            console.log('Loading evaluation types...');
            const data = await window.supervisorEvalApi.getEvaluationTypes();
            
            this.evaluationTypes = data.evaluation_types || {};
            this.renderEvaluationTypes();
            
            console.log('Evaluation types loaded:', this.evaluationTypes);
        } catch (error) {
            console.error('Error loading evaluation types:', error);
            this.showError('Failed to load evaluation types');
        }
    }

    /**
     * Render evaluation types in the dropdown menu
     */
    renderEvaluationTypes() {
        if (!this.evalTypesList) return;

        if (Object.keys(this.evaluationTypes).length === 0) {
            this.evalTypesList.innerHTML = `
                <div class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                    No evaluation types available
                </div>
            `;
            return;
        }

        const items = Object.entries(this.evaluationTypes).map(([key, config]) => {
            const isSelected = key === this.currentEvalType;
            // For frontend viewing, all evaluation types are always available
            const count = config.evaluation_count || 0;
            
            return `
                <div class="evaluation-type-item ${isSelected ? 'bg-indigo-50 dark:bg-indigo-900/20' : ''} 
                     cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
                     data-eval-type="${key}"
                     onclick="evalTypesManager.selectEvaluationType('${key}')">
                    <div class="px-4 py-3">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                                        ${config.name || key}
                                    </h4>
                                    ${isSelected ? `
                                        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
                                            Current
                                        </span>
                                    ` : ''}
                                </div>
                                <p class="mt-1 text-xs text-gray-600 dark:text-gray-400">
                                    ${config.description || 'No description available'}
                                </p>
                                <div class="mt-1 flex items-center text-xs text-gray-500 dark:text-gray-400">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    ${count} evaluations
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        this.evalTypesList.innerHTML = items;
    }

    /**
     * Select a specific evaluation type
     */
    selectEvaluationType(evalType) {
        if (!this.evaluationTypes[evalType]) {
            console.warn(`Evaluation type ${evalType} is not available`);
            return;
        }

        console.log(`Switching to evaluation type: ${evalType}`);
        
        this.currentEvalType = evalType;
        this.updateCurrentEvalDisplay();
        this.hideMenu();
        
        // Dispatch custom event for the main app to react
        const event = new CustomEvent('evaluationTypeChanged', {
            detail: {
                evalType: evalType,
                config: this.evaluationTypes[evalType]
            }
        });
        document.dispatchEvent(event);
        
        // Update the dropdown to show the new selection
        this.renderEvaluationTypes();
    }

    /**
     * Update the current evaluation type display banner
     */
    updateCurrentEvalDisplay() {
        const config = this.evaluationTypes[this.currentEvalType];
        if (!config) return;

        if (this.currentEvalName) {
            this.currentEvalName.textContent = config.name || this.currentEvalType;
        }
        
        if (this.currentEvalDescription) {
            this.currentEvalDescription.textContent = 
                config.description ? `- ${config.description}` : '';
        }
    }

    /**
     * Toggle the evaluation types menu
     */
    toggleMenu() {
        if (this.evalTypesMenu) {
            if (this.evalTypesMenu.classList.contains('hidden')) {
                this.showMenu();
            } else {
                this.hideMenu();
            }
        }
    }

    /**
     * Show the evaluation types menu
     */
    showMenu() {
        if (this.evalTypesMenu) {
            this.evalTypesMenu.classList.remove('hidden');
            // Load fresh data when opening menu
            this.loadEvaluationTypes();
        }
    }

    /**
     * Hide the evaluation types menu
     */
    hideMenu() {
        if (this.evalTypesMenu) {
            this.evalTypesMenu.classList.add('hidden');
        }
    }

    /**
     * Get the current evaluation type
     */
    getCurrentEvalType() {
        return this.currentEvalType;
    }

    /**
     * Show error in the menu
     */
    showError(message) {
        if (this.evalTypesList) {
            this.evalTypesList.innerHTML = `
                <div class="px-4 py-2 text-sm text-red-600 dark:text-red-400">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        ${message}
                    </div>
                </div>
            `;
        }
    }

    /**
     * Initialize the manager
     */
    async init() {
        console.log('Initializing Evaluation Types Manager...');
        await this.loadEvaluationTypes();
        this.updateCurrentEvalDisplay();
    }
}

// Create global instance
window.evalTypesManager = new EvalTypesManager();