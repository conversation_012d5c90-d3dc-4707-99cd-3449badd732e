---
type: "always_apply"
---

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Primary Development Server
```bash
# Start the main web application
poetry run python3 software/run_web.py
```

### Add new packages
```bash
poetry add <package>
```

## Development & Testing Guidelines

### Server Management
**Server runs on port 5001** - Always assume the server is running and must be killed before testing:

```bash
# RELIABLE SERVER RESTART SEQUENCE (use this exact sequence):

# 1. Find and kill all running processes manually (more reliable than pkill)
ps aux | grep "run_web.py" | grep -v grep
kill [PID_NUMBERS_FROM_ABOVE]

# 2. Wait for processes to fully terminate
sleep 5

# 3. Start the server in background
cd "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/Vero" && poetry run python3 software/run_web.py &

# 4. CRITICAL: Wait for server to fully start before testing
sleep 8
```

**Alternative one-liner for quick restart:**
```bash
ps aux | grep "run_web.py" | grep -v grep | awk '{print $2}' | xargs kill; sleep 5; cd "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/Vero" && poetry run python3 software/run_web.py &
```

### Testing Protocols
- **Server restart ONLY needed for backend changes** (Python code in `software/api/`, `software/db/`, etc.)
- **Frontend changes do NOT require server restart** (HTML/CSS/JS files are served statically)
- **ALWAYS wait minimum 8 seconds after server restart** before testing API endpoints
- **Use ps aux + manual kill instead of pkill** (more reliable)
- **Always use full absolute path when starting server**
- Test API endpoints: `curl http://0.0.0.0:5001/api/...` 
- All commands must use `poetry run` prefix
- Save unit tests in `software/tests/` directory
- Use `poetry run python3 software/tests/run_tests.py` for test execution

### Directory Structure Guide
- **`software/frontend/`**: Component-based UI with feature-specific directories and JS modules
- **`software/api/urls.py`**: Central URL routing configuration with Django-style patterns  
- **`software/db/`**: Backend database operations using repository pattern for MongoDB
- **`software/data/`**: External data sources and loaders for market research
- **`software/tests/`**: Unit tests and testing utilities

# Code Style Guidance
1. Always output minimalistic code style
2. follow exact instructions, don't go above and beyond
3. don't implement fallback, search existing methods before creating new ones, ask permission to create new functions.
4. When asked to improve code, please simplify code, don't hardcode, more is less.
5. Kill existing server processes before testing (server management required for testing).
6. Never add comments to my code, only docstrings if possible.
7. REMEMBER: Use Memory for key important inputs
8. Always greet with Hello Radi :)
9. NEVER commit changes unless explicitly told to do so by the user.
10. **Check recent commit**: Before starting work, run `git log -1` to understand the most recent changes and commit message patterns. This provides context for current development focus and helps maintain consistency.

## Architecture Overview

### Core System Structure
Vero is an AI-driven hedge fund management system built on FastAPI with LangGraph for AI workflow orchestration. The system uses MongoDB for data persistence and supports both local and external MongoDB instances.

### Key Architectural Components

**AI Graph System (`software/ai/graph/`)**
- Uses LangGraph for multi-agent workflows with graph-based orchestration
- `director_state.py`: Pydantic models and state management for AI agents
- `delegate_graph.py`: Main workflow delegation system with plan creation and analyst coordination
- `simple_agent_tool_graph.py`: Core agent-tool interaction patterns with supervisor validation
- `search_scope_graph.py`: S&P 500 company discovery for swing trading strategies
- `summarize_report_graph.py`: Report generation and forecast scheduling system

**Tool System (`software/ai/tools/`)**
- Async-first architecture inheriting from LangChain's StructuredTool
- `base_tool.py`: Foundation class for all custom tools
- `registry.py`: Dynamic tool loading and management system
- Market analysis tools: `SimpleWorkflowTool`, `SimpleJoinWorkflowTool`, `ExtractWorkflowTool`
- `EdgarTool_`: SEC EDGAR filings analysis with ReAct agent pattern

**LLM Management (`software/ai/llm/`)**
- `llm_connect.py`: Centralized LLM connection handling with database-driven configuration
- Supports multiple LLM providers: OpenAI, Anthropic, Google, Nvidia, Cerebras, Mistral, DeepSeek
- Dynamic model selection by graph stage or random selection from pools

**Data Layer (`software/data/` and `software/db/`)**
- **`software/data/`**: External data sources and loaders for market research
  - `base_data.py`: Foundation for all data loaders with common functionality
  - Stock market data loaders with various timeframes (10Y daily, 24M daily, 30D hourly, 1M 1-minute)
  - News sentiment analysis loaders (1Y, 3M) with comprehensive daily metrics
- **`software/db/`**: Backend database operations using repository pattern
  - Repository classes for async MongoDB operations with error handling
  - Database collections management and indexing
- **`software/api/urls.py`**: Central URL routing configuration with Django-style patterns

### Frontend Architecture
- HTML/CSS/JavaScript frontend with component-based organization
- Each feature has dedicated frontend directory with JS modules
- Base template system with dark mode support and responsive design
- Real-time updates using WebSocket connections for long-running AI tasks

### Database Collections
- `research_analysis`: AI director analysis tasks and reports
- `forecast_revisits`: Price prediction tracking and validation against actual market performance
- `llm_configurations`: LLM model configurations and preferences
- `tool_registry`: Dynamic tool registration and metadata

### Tool Development
All tools must inherit from `software/ai/tools/base_tool.py` and implement async patterns. Tools are automatically discovered and registered through the registry system.

### Graph Development  
LangGraph workflows use Pydantic models from `director_state.py` for state management. All nodes should handle state transitions cleanly and support supervisor patterns for validation.

### Database Operations
Use repository pattern classes from `software/db/` for all database operations. All operations are async and include proper error handling and index management.

### Frontend Integration
API endpoints in `software/api/routers/` connect to frontend components. Long-running operations use async patterns with status updates.

## Key Features

### AI Research System
- Multi-agent research directors with specialized analyst personas
- Automatic task delegation and report compilation
- Forecast tracking with scheduled revisits for accuracy validation
- Reinforcement learning integration for strategy optimization

### Market Analysis Tools
- Stock data analysis with technical indicators
- SEC EDGAR filings integration
- News sentiment analysis and correlation tracking
- Backtesting and strategy evaluation

### Auto-Deploy Mode
Research agents support continuous auto-deploy mode for automated analysis pipeline execution with persistent state management across browser sessions.

## AI Tools Reference

The following tools are available in `software/ai/tools/` and can be used by AI agents for market analysis:

### BacktestStrategyTool
**What it does**: Tests trading strategies against historical data to see how they would have performed  
**Simple explanation**: Give it a trading idea (like "buy when RSI is low") and a stock ticker, and it builds the strategy code and shows you how much money you would have made or lost  
**Use case**: "Backtest a strategy that buys Apple (AAPL) when RSI drops below 30"

### DataVizInsightTool  
**What it does**: Creates professional stock charts with technical indicators and explains what the charts mean  
**Simple explanation**: Makes pretty charts of stock prices with lines and indicators, then tells you what patterns it sees  
**Use case**: "Create a chart showing Tesla (TSLA) price with Bollinger Bands and RSI"

### EdgarTool
**What it does**: Reads and analyzes SEC filing documents (like 10-K annual reports) to extract financial information  
**Simple explanation**: Reads boring government financial documents and pulls out the interesting parts about a company's finances  
**Use case**: "Analyze Microsoft's (MSFT) latest 10-K filing for revenue growth trends"

### ExtractWorkflowTool
**What it does**: Gets small samples of historical stock data with calculated technical indicators  
**Simple explanation**: Grabs recent stock prices and adds technical analysis numbers like RSI and MACD  
**Use case**: "Get the last 15 days of Netflix (NFLX) data with moving averages"

### SimpleJoinWorkflowTool
**What it does**: Combines data from multiple sources or companies and makes price predictions  
**Simple explanation**: Takes data from different places, lines them up by date, and tries to predict future prices  
**Use case**: "Compare Amazon (AMZN) and Google (GOOGL) stock movements over the past month"

### SimpleWorkflowTool
**What it does**: Creates complete investment analysis reports with price forecasts  
**Simple explanation**: The main tool that does everything - gets data, analyzes it, and writes a full report about whether to buy or sell  
**Use case**: "Analyze Meta (META) for potential price movement in the next week"

### TodayDateTool
**What it does**: Provides the current date and time in any timezone  
**Simple explanation**: Tells you what day and time it is - required to start any analysis  
**Use case**: Always use this tool first to set the analysis context