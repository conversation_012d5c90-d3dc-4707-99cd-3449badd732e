"""
Test script for Edgar workflow components in isolated environment.
Tests knowledge_lookup_node, run_supervisor_node, and code_emulator_edgar tool.
"""

import asyncio
import tempfile
import subprocess
import os
from typing import Dict, Any
from edgar import *
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.tools import tool
from langgraph.prebuilt import create_react_agent

# Global tool counters
knowledge_lookup_count = 0
python_sandbox_count = 0
code_emulator_count = 0
save_memory_count = 0
retrieve_memory_count = 0

# Mock LLM connection for testing
class MockLLMConnect:
    async def get_llm(self):
        # Import here to avoid circular imports
        from software.ai.llm.llm_connect import get_llm_connect
        llm_connect = get_llm_connect()
        return await llm_connect.get_llm()

mock_llm_connect = MockLLMConnect()

async def get_llm_connect():
    return mock_llm_connect

@tool
async def knowledge_lookup_tool(search_query: str) -> str:
    """
    Look up specific EdgarTools documentation and API methods.

    Args:
        search_query: What to search for (e.g., "8-K filings", "Company class methods", "filing date filtering")

    Returns:
        str: Relevant EdgarTools API documentation
    """
    global knowledge_lookup_count
    knowledge_lookup_count += 1
    print(f"\n----- Knowledge Lookup Tool (Call #{knowledge_lookup_count}) -----")

    try:
        # Get LLM for knowledge lookup
        llm_connect = await get_llm_connect()
        model = await llm_connect.get_llm()

        # Read edgar.md documentation as knowledge base
        with open("edgar.md", "r", encoding="utf-8") as f:
            edgar_knowledge_base = f.read()

        system_message = SystemMessage(content=f"""<role>
You are an EdgarTools documentation expert. Use the knowledge base below to find relevant information.
</role>

<knowledge_base>
{edgar_knowledge_base}
</knowledge_base>

<verified_api_patterns>
IMPORT (ALWAYS use this exact import):
from edgar import *
set_identity("<EMAIL>")

COMPANY ACCESS:
company = Company("TSLA")  # Create company object by ticker
company = Company(1318605)  # Or by CIK number

FILING ACCESS:
filings = company.get_filings()  # All filings for company
filings = company.get_filings(form="8-K")  # Specific form type
filings = company.get_filings(form="8-K", date="2024-01-01:")  # With date filter

FILING NAVIGATION (PRIORITY - use pandas for navigation):
df = filings.to_pandas()  # BEST for data exploration
print(df.head())  # Shows first 5 filings
print(df.shape)   # Shows total count
filings.latest(n=5)  # Latest 5 filings
filings.next()  # Next page
filings.previous()  # Previous page

FILING CONTENT:
filing.html()  # Raw HTML content
filing.text()  # Clean text content
filing.markdown()  # Markdown format
filing.obj()  # Structured data

FILING ATTRIBUTES:
filing.company_name, filing.cik, filing.form
filing.filing_date, filing.accession_no
filing.primary_document, filing.document_count
</verified_api_patterns>

<task>
Find relevant EdgarTools API information for: {search_query}
</task>

<response_requirements>
- Return up to 200 characters of relevant information
- Focus on exact API methods and syntax
- Prioritize pandas navigation: df = filings.to_pandas()
- Include import statements if relevant
- Be concise and specific
</response_requirements>""")

        human_message = HumanMessage(content=f"""Search query: {search_query}

Provide the specific EdgarTools methods and patterns needed for this query.""")

        response = await model.ainvoke([system_message, human_message])

        # Limit to 200 characters
        result = response.content
        if len(result) > 200:
            result = result[:200] + "..."

        return result

    except Exception as e:
        return f"Error in knowledge lookup: {str(e)}"

@tool
async def python_sandbox_tool(code_snippet: str) -> str:
    """
    EXPLORATION TOOL: Execute Python code to explore EdgarTools API using various methods.

    Exploration Methods Available:
    1. BASIC NAVIGATION:
       - company.get_filings() - Get all filings
       - filings.to_pandas() - Convert to DataFrame (PRIORITY)
       - filings.latest(n) - Get latest n filings
       - filings.head(n) - Get first n filings
       - print(filings) - Display filings summary

    2. FILING CONTENT EXPLORATION:
       - filing.obj() - Structured data object
       - filing.markdown() - Markdown content (safer than .text()/.html())
       - filing.view() - Rich formatted display
       - print(dir(filing)) - Explore available methods

    3. XBRL FINANCIAL DATA:
       - XBRL.from_filing(filing) - Parse XBRL
       - xbrl.statements - Access financial statements
       - xbrl.facts - Access XBRL facts
       - statements.income_statement() - Income statement
       - statements.balance_sheet() - Balance sheet

    4. COMPANY FINANCIALS:
       - company.get_financials() - Latest financials
       - company.get_quarterly_financials() - Quarterly data
       - financials.income_statement - Income statement
       - financials.balance_sheet - Balance sheet

    5. OBJECT EXPLORATION:
       - print(dir(object)) - Discover methods
       - print(type(object)) - Check object type
       - print(object) - Display object summary

    AVOID: filing.text() and filing.html() - these can overflow context window

    Args:
        code_snippet: Python code for EdgarTools exploration

    Returns:
        str: Execution output for exploration
    """
    global python_sandbox_count
    python_sandbox_count += 1
    print(f"\n----- Python Sandbox Tool (Call #{python_sandbox_count}) -----")

    try:
        # Enhanced exploration code template
        full_code = f"""
from edgar import *
from edgar.xbrl import XBRL, XBRLS
import pandas as pd
set_identity("<EMAIL>")

{code_snippet}
"""

        # Create temporary file and execute
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
            temp_file.write(full_code)
            temp_file_path = temp_file.name

        try:
            # Execute using poetry run
            result = subprocess.run(
                ["poetry", "run", "python3", temp_file_path],
                capture_output=True,
                text=True,
                timeout=45,  # Increased timeout for XBRL processing
                cwd=os.getcwd()
            )

            if result.returncode == 0:
                return f"Exploration successful:\n{result.stdout}"
            else:
                return f"Exploration failed:\n{result.stderr}"

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        return f"Error in sandbox: {str(e)}"

@tool
async def summarize_report(business_question: str, findings: str) -> str:
    """
    SUMMARIZATION TOOL: Summarize exploration findings and answer the business question.

    This tool takes all findings from python_sandbox_tool exploration and creates a
    concise, accurate summary that directly answers the business question.

    Use this tool AFTER python_sandbox_tool has successfully found the actual data.

    Args:
        business_question: The original business question to answer
        findings: All findings and data discovered through python_sandbox_tool exploration

    Returns:
        str: Concise, accurate summary that answers the business question
    """
    global code_emulator_count
    code_emulator_count += 1
    print(f"\n----- Summarize Report Tool (Call #{code_emulator_count}) -----")

    try:
        # Get LLM for summarization
        llm_connect = await get_llm_connect()
        model = await llm_connect.get_llm()

        # Dynamic system message for summarization
        system_message = SystemMessage(content=f"""<role>
You are a financial analysis expert who summarizes EdgarTools exploration findings.
</role>

<objective>
Create a concise, accurate summary that directly answers: {business_question}
Based on the exploration findings provided.
</objective>

<summarization_requirements>
- Focus on answering the specific business question
- Extract key financial data and metrics from findings
- Present information in a clear, structured format
- Include specific numbers, dates, and values when available
- Be concise but comprehensive
- End with a clear answer to the business question
</summarization_requirements>

<output_format>
Structure your response as:

## Business Question Analysis
[Restate the business question]

## Key Findings
[Bullet points of key data discovered]

## Financial Data
[Specific numbers, metrics, dates found]

## Summary
[Direct answer to the business question]

===== FORMATTED ANSWER ======
[Final concise answer]
</output_format>""")

        # Human message with findings
        human_message = HumanMessage(content=f"""Business Question: {business_question}

Exploration Findings:
{findings}

Please summarize these findings and provide a clear answer to the business question.""")

        # Generate summary
        messages = [system_message, human_message]
        response = await model.ainvoke(messages)
        summary = response.content

        return f"Report summarization completed:\n\n{summary}"

    except Exception as e:
        return f"Error in report summarization: {str(e)}"

@tool
async def save_in_memory(business_question: str, findings: str, successful_code_patterns: str) -> str:
    """
    MEMORY SAVE TOOL: Save successful exploration findings with concrete code patterns to memory.

    This tool intelligently saves exploration findings only if they contain new information
    not already present in memory. Use this tool AFTER successfully completing exploration.

    Args:
        business_question: The original business question
        findings: The exploration findings and data discovered
        successful_code_patterns: Specific working code patterns and methods that succeeded

    Returns:
        str: Confirmation of memory save with memory ID, or skip message if duplicate
    """
    global save_memory_count
    save_memory_count += 1
    print(f"\n----- Save In Memory Tool (Call #{save_memory_count}) -----")

    try:
        from software.db.edgar_memory_repository import EdgarMemoryRepository
        from software.ai.llm.llm_connect import get_llm_connect
        from software.ai.llm.llm_connect_v2 import get_model_connector

        # First, check if similar memory already exists
        connector = get_model_connector()
        query_embedding = await connector.embed_query(business_question)

        memory_repo = EdgarMemoryRepository()
        similar_memories = await memory_repo.retrieve_similar_memories(
            query_embedding=query_embedding,
            limit=3,
            similarity_threshold=0.8  # High threshold for dedup check
        )

        # If we found very similar memories (similarity > 0.8), skip saving to avoid duplicates
        if similar_memories:
            highest_similarity = similar_memories[0].get("similarity", 0)
            if highest_similarity > 0.9:  # Very high similarity threshold
                return f"SKIPPED: Found very similar memory (similarity: {highest_similarity:.2f}). No new information to save.\nExisting research already covers this topic effectively."

        # Generate concrete recommendations using LLM
        llm_connect = get_llm_connect()
        model = await llm_connect.get_llm()

        # Create system prompt for generating concrete recommendations
        from langchain_core.messages import SystemMessage, HumanMessage

        system_message = SystemMessage(content=f"""<role>
You are an EdgarTools expert who creates concrete, actionable code recommendations from successful research patterns.
</role>

<objective>
Analyze the successful code patterns and create specific, concrete recommendations with working code examples.
Focus on the exact methods, API calls, and patterns that worked successfully.
</objective>

<output_format>
## Successful Methods Used:
[List specific EdgarTools methods that worked]

## Working Code Patterns:
```python
[Exact working code patterns]
```

## Key Insights:
[Specific insights about data access, filing types, etc.]

## Concrete Recommendations for Similar Research:
1. [Specific recommendation with code example]
2. [Specific recommendation with code example]
3. [Specific recommendation with code example]

## Data Access Patterns:
[Specific patterns for accessing the type of data found]
</output_format>""")

        human_message = HumanMessage(content=f"""Business Question: {business_question}

Findings: {findings}

Successful Code Patterns: {successful_code_patterns}

Please create concrete, actionable recommendations with specific code examples that future research can use for similar questions.""")

        # Generate concrete recommendations
        messages = [system_message, human_message]
        response = await model.ainvoke(messages)
        concrete_recommendations = response.content

        # Create embedding ONLY from business question for better similarity matching
        # Store full context separately for retrieval

        # Get embedding model
        from software.ai.llm.llm_connect_v2 import get_model_connector
        connector = get_model_connector()
        embedding = await connector.embed_query(business_question)  # Only embed the question!

        # Save to memory with concrete recommendations
        memory_repo = EdgarMemoryRepository()
        memory_id = await memory_repo.save_exploration_memory(
            business_question=business_question,
            findings=findings,
            exploration_data={
                "successful_code_patterns": successful_code_patterns,
                "concrete_recommendations": concrete_recommendations,
                "business_question": business_question,
                "findings": findings
            },
            embedding=embedding
        )

        return f"Memory saved successfully with ID: {memory_id}\n\nConcrete recommendations generated:\n{concrete_recommendations}\n\nThis will provide specific code examples for future similar research."

    except Exception as e:
        return f"Error saving to memory: {str(e)}"

@tool
async def retrieve_from_memory(business_question: str) -> str:
    """
    MEMORY RETRIEVAL TOOL: Retrieve similar past exploration findings from memory.

    This tool retrieves past exploration findings that are similar to the current business question.
    Use this tool EARLY in the workflow to learn from past research.

    Args:
        business_question: The current business question to find similar past research for

    Returns:
        str: Similar past findings and exploration methods, or empty if none found
    """
    global retrieve_memory_count
    retrieve_memory_count += 1
    print(f"\n----- Retrieve From Memory Tool (Call #{retrieve_memory_count}) -----")

    try:
        from software.db.edgar_memory_repository import EdgarMemoryRepository
        from software.ai.llm.llm_connect_v2 import get_model_connector

        # Get embedding for the business question (same approach as save_in_memory)
        connector = get_model_connector()
        query_embedding = await connector.embed_query(business_question)

        # Retrieve similar memories
        memory_repo = EdgarMemoryRepository()
        similar_memories = await memory_repo.retrieve_similar_memories(
            query_embedding=query_embedding,
            limit=3,
            similarity_threshold=0.6
        )

        if not similar_memories:
            return "No similar past research found in memory. This appears to be a new type of analysis."

        # Format retrieved memories with concrete recommendations
        memory_context = "## Similar Past Research Found:\n\n"

        for i, memory in enumerate(similar_memories, 1):
            similarity_score = memory.get("similarity", 0)
            past_question = memory.get("business_question", "")
            past_findings = memory.get("findings", "")
            usage_count = memory.get("usage_count", 0)
            exploration_data = memory.get("exploration_data", {})

            # Get concrete recommendations if available
            concrete_recommendations = exploration_data.get("concrete_recommendations", "")
            successful_code_patterns = exploration_data.get("successful_code_patterns", "")

            memory_context += f"### Past Research #{i} (Similarity: {similarity_score:.2f}, Used: {usage_count} times)\n"
            memory_context += f"**Question:** {past_question}\n\n"
            memory_context += f"**Findings:** {past_findings[:800]}{'...' if len(past_findings) > 800 else ''}\n\n"

            if successful_code_patterns:
                memory_context += f"**Successful Code Patterns:**\n{successful_code_patterns[:1200]}{'...' if len(successful_code_patterns) > 1200 else ''}\n\n"

            if concrete_recommendations:
                memory_context += f"**Concrete Recommendations:**\n{concrete_recommendations[:1500]}{'...' if len(concrete_recommendations) > 1500 else ''}\n\n"

            memory_context += "---\n\n"

        memory_context += "## Action Plan:\n"
        memory_context += "- Use the specific code patterns shown above that worked successfully\n"
        memory_context += "- Follow the concrete recommendations with exact method calls\n"
        memory_context += "- Adapt the working patterns to your specific ticker/company\n"
        memory_context += "- Build upon the successful data access methods shown\n"

        return memory_context

    except Exception as e:
        return f"Error retrieving from memory: {str(e)}"



def evaluate_business_answer(business_question: str, execution_result: str) -> bool:
    """Evaluate if the execution result actually answers the business question."""
    result_lower = execution_result.lower()

    # Simple check: just look for the formatted answer section
    has_formatted_answer = "===== formatted answer =====" in result_lower

    # Check for failure indicators
    has_failure_indicators = any([
        "error in exploratory agent" in result_lower,
        "modulenotfounderror" in result_lower,
        execution_result.strip() == "",
        len(execution_result) < 50
    ])

    return has_formatted_answer and not has_failure_indicators



async def run_exploratory_agent_node(business_question: str) -> str:
    """Run ReAct agent with exploratory tools for EdgarTools research."""
    try:
        # Get LLM for agent
        llm_connect = await get_llm_connect()
        model = await llm_connect.get_llm()

        # Create exploratory research agent with five tools
        research_agent = create_react_agent(
            model=model,
            tools=[retrieve_from_memory, knowledge_lookup_tool, python_sandbox_tool, summarize_report, save_in_memory],
            prompt=f"""<role>
You are an autonomous financial research agent specializing in SEC filing analysis using EdgarTools. You have extensive experience in extracting financial data, analyzing corporate filings, and providing actionable business insights. You operate completely independently without human intervention.
</role>

<context>
You are analyzing SEC filings to answer specific business questions for financial analysts, investors, and business stakeholders who need accurate, current data for decision-making. Your analysis will be used for investment decisions, competitive analysis, and strategic planning.
</context>

<task>
Analyze the business question: "{business_question}"
Extract relevant data from SEC filings using EdgarTools and provide a comprehensive, data-driven answer.
</task>

<success_criteria>
A successful analysis must:
1. Directly answer the specific business question with actual current data
2. Include specific numerical data, dates, and metrics from SEC filings
3. Provide clear, actionable insights based on the extracted data
4. Use only verified, current information from official SEC sources
5. Present findings in a professional format suitable for business stakeholders

FAILURE CONDITIONS:
- Providing only API documentation or examples without real data
- Using outdated or cached information instead of current filings
- Giving generic responses that don't address the specific question
- Stopping exploration before extracting the requested data
</success_criteria>

<available_tools>
1. retrieve_from_memory: Retrieve similar past research findings and proven code patterns
2. knowledge_lookup_tool: Get EdgarTools API documentation and method guidance
3. python_sandbox_tool: Execute code to extract current data from SEC filings
4. summarize_report: Create final business answer from extracted data
5. save_in_memory: Store successful code patterns for future research
</available_tools>

<reasoning_steps>
Follow this systematic approach for every business question:
1. MEMORY RETRIEVAL: Check for similar past research to learn proven methods
2. API UNDERSTANDING: Get necessary EdgarTools knowledge if memory insufficient
3. DATA EXTRACTION: Execute code to get current data using proven patterns
4. VALIDATION: Verify data accuracy and completeness
5. SYNTHESIS: Create comprehensive answer addressing the business question
6. KNOWLEDGE STORAGE: Save successful patterns for future use
</reasoning_steps>

<examples>
<example>
BUSINESS QUESTION: "Get the latest 10-Q for Nike and extract the revenue"
EXPECTED PROCESS:
1. retrieve_from_memory → Find similar revenue extraction patterns
2. python_sandbox_tool → Execute: Company("NKE").get_filings().filter(form="10-Q").latest()
3. python_sandbox_tool → Extract revenue using XBRL.from_filing().statements.income_statement()
4. summarize_report → "Nike's Q3 2025 revenue was $11,269 million for nine months ended Feb 28, 2025"
5. save_in_memory → Store working code patterns for future revenue extraction
</example>

<example>
BUSINESS QUESTION: "Analyze Apple's debt levels from their latest 10-K"
EXPECTED PROCESS:
1. retrieve_from_memory → Look for debt analysis patterns
2. python_sandbox_tool → Get latest 10-K: Company("AAPL").get_filings().filter(form="10-K").latest()
3. python_sandbox_tool → Extract debt data from balance sheet
4. summarize_report → "Apple's total debt as of Sept 2024 was $X billion, representing Y% of total assets"
5. save_in_memory → Store debt extraction methods
</example>
</examples>

<critical_workflow>
PHASE 0 - MEMORY RETRIEVAL (use retrieve_from_memory FIRST):
- Start with retrieve_from_memory to learn from past similar research
- Use insights from past successful explorations
- Build upon previous findings and methods

PHASE 1 - INITIAL UNDERSTANDING (use knowledge_lookup_tool if needed):
- Use knowledge_lookup_tool for basic API understanding if memory doesn't provide enough
- Get import patterns and basic navigation methods
- Avoid using knowledge_lookup_tool frequently after initial understanding

PHASE 2 - MANDATORY EXPLORATION (ALWAYS use python_sandbox_tool to get fresh data):
CRITICAL: Even if memory provides guidance, you MUST use python_sandbox_tool to get current data!
Memory provides methods, but data changes over time - always verify with fresh API calls.

STEP 1: Use Memory Guidance
- Apply the successful code patterns from memory
- Use the exact methods that worked before
- Follow the concrete recommendations provided

STEP 2: Execute Fresh Data Retrieval
- MANDATORY: Use python_sandbox_tool with the proven patterns from memory
- Get current filings and extract current data
- Verify the data is up-to-date and accurate

STEP 3: Validate Results
- Ensure you have actual current data (not just memory data)
- AVOID filing.text() and filing.html() - they can overflow context
- STOP exploring once you have extracted the actual CURRENT data needed to answer the business question

CRITICAL ERROR HANDLING:
When encountering errors during data extraction:
1. IMMEDIATE RESPONSE: Use print(dir(object)) to discover available methods
2. OBJECT ANALYSIS: Use print(type(object)) to understand object structure
3. METHOD DISCOVERY: Explore object properties before attempting method calls
4. AUTONOMOUS RECOVERY: Adjust approach based on discovered methods
5. DOCUMENTATION: Record successful recovery patterns for future use

NEVER guess methods - always discover them systematically!

PHASE 3 - SUMMARIZATION (use summarize_report when data found):
- Use summarize_report ONLY after python_sandbox_tool has successfully found ACTUAL DATA
- Must have extracted: actual business data (revenue, metrics, etc.) from filings
- Pass all findings to summarize_report for final business question answer

PHASE 4 - MEMORY SAVE (use save_in_memory LAST):
- Use save_in_memory ONLY after successful completion of research
- CRITICAL: Pass the specific working code patterns that successfully extracted the data
- Include exact method calls, API patterns, and successful exploration techniques
- This creates concrete recommendations for future similar research
- SMART SAVING: If you got the answer from retrieve_from_memory with high similarity (>0.9), you may skip saving unless you discovered new methods

IMPORTANT: Only skip save_in_memory if you used existing memory and found no new techniques!
</critical_workflow>

<edgartools_navigation_guidance>
DYNAMIC EXPLORATION METHODS (adapt to any business question):

1. COMPANY ACCESS (always start here):
   - Company(ticker) - Access any company by ticker
   - company.get_filings() - See all available filings
   - print(filings) - Understand what filings exist

2. FILING SELECTION (choose based on business question):
   - filings.latest() - Most recent filing
   - filings.filter(form="FORM_TYPE") - Specific form types
   - filings.filter(date="YYYY-MM-DD:YYYY-MM-DD") - Date ranges
   - Use business question to determine which filings are relevant

3. DATA EXTRACTION (try multiple methods):
   - filing.obj() - Structured data objects (try first)
   - XBRL.from_filing(filing) - Financial data parsing
   - company.get_financials() - Direct financial access
   - print(dir(object)) - Discover available methods
   - AVOID: filing.text(), filing.html() (too large)

4. OBJECT EXPLORATION (MANDATORY when errors occur):
   - FIRST: print(dir(object)) - See ALL available methods
   - SECOND: print(type(object)) - Check object type
   - THIRD: print(object) - Display object summary
   - NEVER guess methods - always discover them first!

CRITICAL: If you get AttributeError, stop guessing and use dir() immediately!
REMEMBER: Adapt your approach based on the specific business question - don't follow a rigid pattern!
</edgartools_navigation_guidance>

<autonomous_operation>
When tools fail or have constraints:
1. Automatically adjust parameters and retry once
2. Make independent decisions without asking for confirmation
3. Continue toward the business objective using alternative approaches
4. Document any adjustments in your final response
</autonomous_operation>

<efficiency_requirement>
1. Use retrieve_from_memory FIRST to learn from past research and avoid repeating work
2. Use knowledge_lookup_tool for initial understanding if memory doesn't provide enough
3. MANDATORY: ALWAYS use python_sandbox_tool to get fresh current data, even if memory provides guidance
4. ANTI-LOOP RULE: If python_sandbox_tool returns the same result twice, move to next phase
5. CRITICAL: python_sandbox_tool must successfully extract actual CURRENT business data before using summarize_report
6. Use summarize_report to create final answer from all findings
7. Use save_in_memory LAST with specific working code patterns that successfully extracted data
8. NEVER summarize based only on memory - always get fresh data with python_sandbox_tool first
9. ANTI-GUESSING RULE: If you get AttributeError, use print(dir(object)) immediately - DO NOT guess methods
</efficiency_requirement>

<output_format>
Your final response must follow this structure:
1. BUSINESS QUESTION ANALYSIS: Restate and break down the question
2. KEY FINDINGS: Bullet points of extracted data with specific numbers/dates
3. FINANCIAL DATA: Formatted presentation of relevant metrics
4. SUMMARY: Concise answer directly addressing the business question
5. FINAL ANSWER: Clear, actionable conclusion with specific data points
</output_format>

<autonomous_decision_making>
You must operate completely autonomously without human intervention:
1. Make independent decisions when encountering API limitations or errors
2. Automatically adjust parameters when tools require different specifications
3. Document all adjustments and decisions in your final output
4. Continue analysis using best available alternatives when ideal options aren't possible
5. NEVER ask for confirmation, clarification, or wait for human input
6. Use systematic exploration (dir, type, print) to overcome technical obstacles
</autonomous_decision_making>""",
            debug=False
        )

        # Stream agent execution with clean message output and recursion limit
        messages_input = {
            "messages": [
                {
                    "role": "user",
                    "content": f"Analyze: {business_question}"
                }
            ]
        }

        # Configure recursion limit (increased for complex exploration)
        config = {"recursion_limit": 100}

        print("🔄 Streaming agent execution...")
        execution_result = ""

        async for message, metadata in research_agent.astream(
            messages_input,
            config=config,
            stream_mode="messages"
        ):
            if hasattr(message, 'content') and message.content:
                print(message.content, end="", flush=True)
                execution_result += message.content

        print(f"\n\n📊 Agent execution completed successfully")
        return execution_result

    except Exception as e:
        return f"Error in exploratory agent: {str(e)}"

async def test_business_question(business_question: str):
    """Test a single business question through the Edgar workflow."""
    print(f"\n{'='*80}")
    print(f"TESTING: {business_question}")
    print(f"{'='*80}")

    try:
        # Run Exploratory Agent (combines exploration, research, and analysis)
        print("\n🔬 Exploratory Research Agent")
        execution_result = await run_exploratory_agent_node(business_question)
        print(f"Execution Result: {execution_result}")

        # Evaluate if the business question was actually answered
        success = evaluate_business_answer(business_question, execution_result)

        return {
            "business_question": business_question,
            "execution_result": execution_result,
            "status": "SUCCESS" if success else "FAILED"
        }

    except Exception as e:
        print(f"❌ Error testing business question: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "business_question": business_question,
            "status": "FAILED",
            "error": str(e)
        }

async def run_all_tests():
    """Run all business question tests."""
    # Reset global counters
    global knowledge_lookup_count, python_sandbox_count, code_emulator_count, save_memory_count, retrieve_memory_count
    knowledge_lookup_count = 0
    python_sandbox_count = 0
    code_emulator_count = 0
    save_memory_count = 0
    retrieve_memory_count = 0

    print("🚀 Starting Edgar Workflow Component Tests")
    print("=" * 80)

    # Set Edgar identity
    set_identity("<EMAIL>")

    # Test cases
    test_cases = [
        # 1. Analyze Quarterly Revenue Growth for Zebra Technologies (ZBRA)
        "Retrieve and compare Zebra Technologies' revenue figures from their recent 10-Q filings to assess quarterly growth trends.",

        # 2. Summarize Recent 8-K Filings for Enphase Energy (ENPH)
        "Identify and summarize material events reported in Enphase Energy's 8-K filings over the past six months.",

        # 3. Track Insider Transactions for Plug Power Inc. (PLUG)
        "Examine Form 4 filings to monitor insider trading activities for Plug Power Inc. within the last year.",

        # 4. Evaluate 13F Holdings of ARK Next Generation Internet ETF (ARKW)
        "Analyze the top holdings reported in the latest 13F filings by ARKW to understand their investment focus.",

        # 5. Assess Annual Reports of Beyond Meat Inc. (BYND) and Oatly Group AB (OTLY)
        "Review the most recent 10-K filings of Beyond Meat and Oatly to identify revenue trends and key business risks.",

        # 6. Investigate Form D Filings for Rivian Automotive Inc. (RIVN)
        "Explore any Form D filings submitted by Rivian to uncover information about private securities offerings.",

        # 7. Compare Financial Statements of SunPower Corporation (SPWR) and First Solar Inc. (FSLR)
        "Extract and compare balance sheets and income statements from recent 10-Q filings of SunPower and First Solar to evaluate financial health.",

        # 8. Monitor Insider Holdings for Lucid Group Inc. (LCID) and Fisker Inc. (FSR)
        "Use Form 3 and Form 4 filings to track changes in insider ownership for Lucid Group and Fisker Inc.",

        # 9. Analyze Risk Factors in 10-K Filings of ChargePoint Holdings Inc. (CHPT) and Blink Charging Co. (BLNK)
        "Examine the risk factors sections in the latest 10-K filings of ChargePoint and Blink Charging to understand potential challenges faced by these companies.",

        # 10. Review XBRL Data for Bloom Energy Corporation (BE) and FuelCell Energy Inc. (FCEL)
        "Parse XBRL data from filings of Bloom Energy and FuelCell Energy to extract detailed financial metrics for analysis."
    ]


    results = []

    for business_question in test_cases:
        result = await test_business_question(business_question)
        results.append(result)

    # Summary
    print(f"\n{'='*80}")
    print("📊 TEST SUMMARY")
    print(f"{'='*80}")

    successful = [r for r in results if r["status"] == "SUCCESS"]
    failed = [r for r in results if r["status"] == "FAILED"]

    print(f"✅ Successful: {len(successful)}/{len(results)}")
    print(f"❌ Failed: {len(failed)}/{len(results)}")

    if failed:
        print("\n❌ Failed Tests:")
        for result in failed:
            print(f"  - {result['business_question'][:50]}...: {result.get('error', 'Unknown error')}")

    # Tool usage summary
    print(f"\n{'='*80}")
    print("🔧 TOOL USAGE SUMMARY")
    print(f"{'='*80}")
    print(f"🔍 Retrieve From Memory Tool: {retrieve_memory_count} calls")
    print(f"📚 Knowledge Lookup Tool: {knowledge_lookup_count} calls")
    print(f"🧪 Python Sandbox Tool: {python_sandbox_count} calls")
    print(f"📝 Summarize Report Tool: {code_emulator_count} calls")
    print(f"💾 Save In Memory Tool: {save_memory_count} calls")
    print(f"📊 Total Tool Calls: {retrieve_memory_count + knowledge_lookup_count + python_sandbox_count + code_emulator_count + save_memory_count}")

    return results

if __name__ == "__main__":
    results = asyncio.run(run_all_tests())
